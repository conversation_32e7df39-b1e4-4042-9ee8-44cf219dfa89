package com.loyalty.offerservice.exception.model;

import com.loyalty.offerservice.helper.CorsHelper;
import lombok.NoArgsConstructor;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@NoArgsConstructor
public abstract class AbstractErrorResponse {

    @Autowired
    CorsHelper corsHelper;

    public ResponseEntity<?> build(String code, String msg, HttpStatus status) {
        Map<String, String> contextMap = MDC.getCopyOfContextMap();

        String retrievedCorrelationId = getCorrelationId(contextMap);

        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set("X-Correlation-Id", retrievedCorrelationId);

        return new ResponseEntity<>(Map.of("code", code, "message", msg), responseHeaders, status);
    }

    private String getCorrelationId(Map<String, String> contextMap) {
        String correlationId = "";
        if (contextMap != null) {
            correlationId = contextMap.getOrDefault("correlationId", "No Correlation Id found");
        }

        return correlationId;
    }
}
