@Library('jenkins-shared-lib-v2') _

def releaseVersion = params.releaseVersion
def envToDeploy = params.envToDeploy

// This job can only be triggered manually to deploy specific version to non prod

pipeline {
  agent none
  stages {
    stage("Trigger Deployment to Specific ENV") {
        steps{
            println "Release Version is " + releaseVersion
            println "Deploy to " + envToDeploy

            input(
                message: "Deploy " + releaseVersion + " to " + envToDeploy, 
                submitter: "OS-AWS-JenkinsDeveloper",
                ok: 'approved!')
        }
    }
    
        
    stage ('deploy to environment') {
      agent { label 'aws-ec2' } 
      steps {
        script {
          println "Deploying ECS Service Application"
          awsUtils.deployAndTestECS(envToDeploy, releaseVersion, "echo "+envToDeploy+" test")
        }
      }
    }
  }
  post {
    cleanup {
      deleteDir()
      dir("${workspace}@tmp") {
        deleteDir()
      }
      dir("${workspace}@script") {
        deleteDir()
      }
    }      
  }
}
