package com.loyalty.offerservice.util;

import com.loyalty.offerservice.enums.SortingEnum;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

public class SortEnumTest {
    @Test
    void validateList_withValidEnumValues_shouldNotThrowException() {
        // Given
        List<String> validValues = Arrays.asList("promotionId", "massOffer", "-endDate");

        // When & Then
        assertDoesNotThrow(() -> SortingEnum.validateList(validValues));
    }

    @Test
    void validateList_withEmptyList_shouldNotThrowException() {
        // Given
        List<String> emptyList = Collections.emptyList();

        // When & Then
        assertDoesNotThrow(() -> SortingEnum.validateList(emptyList));
    }

    @Test
    void validateList_withInvalidEnumValue_shouldThrowException() {
        // Given
        List<String> invalidValues = Arrays.asList("invalidSort", "massOffer");

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> SortingEnum.validateList(invalidValues));
        assertTrue(exception.getMessage().contains("invalidSort"));
    }

    @Test
    void validateList_withValidPartnerIdUuid_shouldNotThrowException() {
        // Given
        String uuid = "550e8400-e29b-41d4-a716-************";
        List<String> validPartnerIdValues = Arrays.asList("partnerId:" + uuid, "massOffer");

        // When & Then
        assertDoesNotThrow(() -> SortingEnum.validateList(validPartnerIdValues));
    }

    @Test
    void validateList_withInvalidPartnerIdUuid_shouldThrowException() {
        // Given
        List<String> invalidPartnerIdValues = Arrays.asList("partnerId:not-a-uuid", "massOffer");

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> SortingEnum.validateList(invalidPartnerIdValues));
        assertTrue(exception.getMessage().contains("invalid UUID"));
    }

    @ParameterizedTest
    @ValueSource(strings = {
            "promotionId", "partnerId", "massOffer", "-massOffer", "displayPriority",
            "-displayPriority", "endDate", "-endDate", "collectorrelevance", "regionrelevance"
    })
    void getKey_withValidEnumValue_shouldReturnCorrectEnum(String value) {
        // When
        SortingEnum result = SortingEnum.getKey(value);

        // Then
        assertNotNull(result);
        assertEquals(value, result.getValue());
    }
}
