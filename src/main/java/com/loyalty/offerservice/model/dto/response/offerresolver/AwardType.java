package com.loyalty.offerservice.model.dto.response.offerresolver;

import com.fasterxml.jackson.annotation.JsonProperty;

public enum  AwardType  {
    @JsonProperty("flatMiles")
    FlatMiles("flatMiles"),

    @JsonProperty("multiplierMiles")
    MultiplierMiles("multiplierMiles"),

    @JsonProperty("flatDiscount")
    FlatDiscount("flatDiscount"),

    @JsonProperty("percentDiscount")
    PercentDiscount("percentDiscount"),

    @JsonProperty("custom")
    Custom("custom");

    AwardType(String noAction) {
    }

}
