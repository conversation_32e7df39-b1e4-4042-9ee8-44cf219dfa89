package com.loyalty.offerservice.service.async;

import com.loyalty.offerservice.model.dto.request.OfferRequest;
import com.loyalty.offerservice.model.dto.response.offerstate.*;
import com.loyalty.offerservice.service.offerstate.OfferStateService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;

import static net.logstash.logback.marker.Markers.append;

@Component
@Slf4j
public class OfferState {


    @Autowired
    OfferStateService offerStateService;


    @Async
    public CompletableFuture<Map<UUID, List<Map<String, Object>>>> retrieveGetProcessAsync(List<UUID> offerIds, OfferRequest request, Map<String, String> context) throws IOException {
        if (context != null) {
            MDC.setContextMap(context);
        }

        Map<UUID, List<Map<String, Object>>> statesMap = null;

        if(request.getCollectorNumber() != null) {
            statesMap = retrieveGetProcess(request, offerIds);
            log.debug(append("stateMap",statesMap),"Offer State: GET call produce stateMap");
        } else {
            log.debug("Offer State: GET call skipped");
        }

        return CompletableFuture.completedFuture(statesMap);
    }


    // getStates will return offer state list from offer state service
    private Map<UUID, List<Map<String, Object>>> retrieveGetProcess(OfferRequest request, List<UUID> offerIds) throws IOException {
        GetCollectorOffersDTO collectorOffersDTO = offerStateService.getOfferState(request, request.getCollectorNumber(), offerIds);
        
        List<CollectorOfferDTO> offerList = collectorOffersDTO.getOffer();
        Map<UUID, List<Map<String, Object>>> statesMap = new HashMap<>();

        for (CollectorOfferDTO offer : offerList) {
            if (offer.getCollectorId().equals(request.getCollectorNumber()) && offerIds.contains(offer.getOfferId())) {
                List<Map<String, Object>> statesList = new ArrayList<>();
                for (CollectorOfferStateDTO stateDTO : offer.getStates()) {
                    Map<String, Object> stateMap = new HashMap<>();
                    stateMap.put("name", stateDTO.getName());
                    stateMap.put("value", stateDTO.getValue());
                    stateMap.put("updatedAt", DateTimeFormatter.ISO_INSTANT.format(stateDTO.getUpdatedAt()));
                    statesList.add(stateMap);
                }
                statesMap.put(offer.getOfferId(), statesList);
            }
        }
        return statesMap;
    }


    @Async
    public CompletableFuture<CountDTO> retrieveCountProcessAsync(OfferRequest request, Map<String, String> context) throws IOException {
        if (context != null) {
            MDC.setContextMap(context);
        }

        return CompletableFuture.completedFuture(retrieveCountProcess(request));
    }

    private CountDTO retrieveCountProcess(OfferRequest request) throws IOException {
        return offerStateService.findOfferStateCount(request, request.getCollectorNumber());
    }


    @Async
    public CompletableFuture<Map<UUID, List<Map<String, Object>>>> retrieveFindProcessAsync(OfferRequest request, Map<String, String> context) throws IOException {
        if (context != null) {
            MDC.setContextMap(context);
        }

        return CompletableFuture.completedFuture(retrieveFindProcess(request));
    }

    private Map<UUID, List<Map<String, Object>>> retrieveFindProcess(OfferRequest request) throws IOException {
        FindCollectorOffersDTO findCollectorOffersDTO = offerStateService.findOfferState(request, request.getCollectorNumber()); //nova-offer-state-reader (/find endpoint)
        List<CollectorOfferDTO> offerList = findCollectorOffersDTO.getOffers();
        Map<UUID, List<Map<String, Object>>> statesMap = new HashMap<>();

        for (CollectorOfferDTO offer : offerList) {
            List<Map<String, Object>> statesList = new ArrayList<>();
            for (CollectorOfferStateDTO stateDTO : offer.getStates()) {
                Map<String, Object> stateMap = new HashMap<>();
                stateMap.put("name", stateDTO.getName());
                stateMap.put("value", stateDTO.getValue());
                stateMap.put("updatedAt", DateTimeFormatter.ISO_INSTANT.format(stateDTO.getUpdatedAt()));
                statesList.add(stateMap);
            }
            statesMap.put(offer.getOfferId(), statesList);
        }
        return statesMap;
    }
}
