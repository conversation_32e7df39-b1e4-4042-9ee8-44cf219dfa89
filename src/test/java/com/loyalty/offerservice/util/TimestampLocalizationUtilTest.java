package com.loyalty.offerservice.util;

import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;

import java.time.Clock;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.ZoneOffset;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;
import java.time.temporal.ChronoUnit;

@ExtendWith(MockitoExtension.class)
public class TimestampLocalizationUtilTest {

    private void assertProvinceTimezone(String province, String zuluTime, String expectedTimezoned){
        Clock clock = Clock.fixed(Instant.parse(zuluTime), ZoneId.of("UTC"));
        Instant instant = Instant.now(clock);

        try (MockedStatic<Instant> mockedStatic = mockStatic(Instant.class, Mockito.CALLS_REAL_METHODS)) {
            mockedStatic.when(Instant::now).thenReturn(instant);

            assertThat(TimestampLocalizationUtil.getFormattedLocalTimestamp(province)).isEqualTo(expectedTimezoned);
        }
    }


    @Test
    public void givenZoneAB_returnedZonedTimestamp() {
        assertProvinceTimezone("AB", "2014-12-22T10:15:30Z", "2014-12-22T03:15:30");
    }

    @Test
    public void givenZoneBC_returnedZonedTimestamp() {
        assertProvinceTimezone("BC", "2014-12-22T10:15:30Z", "2014-12-22T02:15:30");
    }

    @Test
    public void givenZoneMB_returnedZonedTimestamp() {
        assertProvinceTimezone("MB", "2014-12-22T10:15:30Z", "2014-12-22T04:15:30");
    }

    @Test
    public void givenZoneNB_returnedZonedTimestamp() {
        assertProvinceTimezone("NB", "2014-12-22T10:15:30Z", "2014-12-22T06:15:30");
    }

    @Test
    public void givenZoneNL_returnedZonedTimestamp() {
        assertProvinceTimezone("NL", "2014-12-22T10:15:30Z", "2014-12-22T06:45:30");
    }

    @Test
    public void givenZoneNS_returnedZonedTimestamp() {
        assertProvinceTimezone("NS", "2014-12-22T10:15:30Z", "2014-12-22T06:15:30");
    }

    @Test
    public void givenZoneNT_returnedZonedTimestamp() {
        assertProvinceTimezone("NT", "2014-12-22T10:15:30Z", "2014-12-22T03:15:30");
    }

    @Test
    public void givenZoneNU_returnedZonedTimestamp() {
        assertProvinceTimezone("NU", "2014-12-22T10:15:30Z", "2014-12-22T05:15:30");
    }

    @Test
    public void givenZoneON_returnedZonedTimestamp() {
        assertProvinceTimezone("ON", "2014-12-22T10:15:30Z", "2014-12-22T05:15:30");
    }

    @Test
    public void givenZonePE_returnedZonedTimestamp() {
        assertProvinceTimezone("PE", "2014-12-22T10:15:30Z", "2014-12-22T06:15:30");
    }

    @Test
    public void givenZoneQC_returnedZonedTimestamp() {
        assertProvinceTimezone("QC", "2014-12-22T10:15:30Z", "2014-12-22T05:15:30");
    }

    @Test
    public void givenZoneSK_returnedZonedTimestamp() {
        assertProvinceTimezone("SK", "2014-12-22T10:15:30Z", "2014-12-22T04:15:30");
    }

    @Test
    public void givenZoneYT_returnedZonedTimestamp() {
        assertProvinceTimezone("YT", "2014-12-22T10:15:30Z", "2014-12-22T02:15:30");
    }

    @Test
    public void givenZoneTB_returnedZonedTimestamp() {
        assertProvinceTimezone("TB", "2014-12-22T10:15:30Z", "2014-12-22T05:15:30");
    }

}
