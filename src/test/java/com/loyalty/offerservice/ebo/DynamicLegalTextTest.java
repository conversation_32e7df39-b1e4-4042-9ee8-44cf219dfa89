package com.loyalty.offerservice.ebo;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.loyalty.offerservice.model.dto.request.OfferRequest;
import com.loyalty.offerservice.model.dto.response.offerdelivery.OfferDeliveryOfferObject;
import com.loyalty.offerservice.model.dto.response.offerdelivery.OfferDeliveryResponse;
import com.loyalty.offerservice.model.dto.response.offerresolver.OfferResolverResponseDTO;
import com.loyalty.offerservice.model.dto.response.partner.PartnerResponse;
import com.loyalty.offerservice.service.OfferServiceImpl;
import com.loyalty.offerservice.service.async.OfferDelivery;
import com.loyalty.offerservice.service.partner.PartnerService;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.util.ResourceUtils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.loyalty.offerservice.model.dto.response.OfferServiceResponse;

@SpringBootTest
public class DynamicLegalTextTest {

    ObjectMapper objectMapper = new ObjectMapper().registerModule(
            new JavaTimeModule()
    );

    @Autowired
    OfferServiceImpl offerService;

    @MockBean
    PartnerService partnerService;

//    @MockBean
//    OfferDelivery offerDelivery;

    @Test
    void ebo_legaltext_replacement_offerId() throws IOException{
        File jsonFile = ResourceUtils.getFile("classpath:ebo_legaltext_replacement_offerId.json");
        Map<String, Object> testingCriteriaMap = objectMapper.readValue(jsonFile, Map.class);
        Assertions.assertNotNull(testingCriteriaMap);

        OfferResolverResponseDTO offerResolverResponse = objectMapper.convertValue(testingCriteriaMap.get("resolverResponse"), OfferResolverResponseDTO.class);
        OfferDeliveryOfferObject offerDeliveryResponse = objectMapper.convertValue(testingCriteriaMap.get("offerDeliveryResponse"), OfferDeliveryOfferObject.class);
        PartnerResponse partnerResponse = objectMapper.convertValue(testingCriteriaMap.get("partnerResponse"), PartnerResponse.class);

        when(partnerService.getPartner(any(),any())).thenReturn(partnerResponse);
        // English Test
        OfferRequest offerRequest = OfferRequest.builder().language("en-US").build();
        OfferServiceResponse response = offerService.getOfferResponseForOfferId(offerResolverResponse, offerDeliveryResponse, offerRequest, null);
        Assertions.assertNotEquals(offerResolverResponse.getOffers().get(0).getStartDate(),response.getStartDate());
        Assertions.assertEquals(offerDeliveryResponse.getEndDate(),response.getEndDate());
        Assertions.assertTrue(response.getLegalText().contains("January 1, 2024"));
        Assertions.assertTrue(response.getLegalText().contains("January 1, 2025"));
        System.out.println("legal text test: "+ response.getLegalText());


        // French Test
        offerRequest = OfferRequest.builder().language("fr-CA").build();
        response = offerService.getOfferResponseForOfferId(offerResolverResponse, offerDeliveryResponse, offerRequest, null);
        Assertions.assertTrue(response.getLegalText().contains("1 janvier 2024"));
        Assertions.assertTrue(response.getLegalText().contains("1 janvier 2025"));
        System.out.println("legal text test: "+ response.getLegalText());

    }

    @Test
    void nonebo_no_replacement_offerId() throws IOException{
        File jsonFile = ResourceUtils.getFile("classpath:ebo_legaltext_replacement_offerId.json");
        Map<String, Object> testingCriteriaMap = objectMapper.readValue(jsonFile, Map.class);
        Assertions.assertNotNull(testingCriteriaMap);

        OfferResolverResponseDTO offerResolverResponse = objectMapper.convertValue(testingCriteriaMap.get("resolverResponse"), OfferResolverResponseDTO.class);
        offerResolverResponse.getOffers().get(0).setMassOffer(true);
        OfferDeliveryOfferObject offerDeliveryResponse = objectMapper.convertValue(testingCriteriaMap.get("offerDeliveryResponse"), OfferDeliveryOfferObject.class);
        offerDeliveryResponse.setEventBased(false);
        offerDeliveryResponse.setMassOffer(true);
        PartnerResponse partnerResponse = objectMapper.convertValue(testingCriteriaMap.get("partnerResponse"), PartnerResponse.class);

        when(partnerService.getPartner(any(),any())).thenReturn(partnerResponse);

        OfferServiceResponse response = offerService.getOfferResponseForOfferId(offerResolverResponse, offerDeliveryResponse, null, null);
        Assertions.assertEquals(offerResolverResponse.getOffers().get(0).getStartDate(),response.getStartDate());
        Assertions.assertNotEquals(offerDeliveryResponse.getEndDate(),response.getEndDate());
        Assertions.assertTrue(response.getLegalText().contains("<COLLECTOR_START_DATE>"));
        Assertions.assertTrue(response.getLegalText().contains("<COLLECTOR_END_DATE>"));

        System.out.println("legal text test: "+ response.getLegalText());

    }


    @Test
    void ebo_legaltext_replacement_offers_noState() throws IOException{
        File jsonFile = ResourceUtils.getFile("classpath:ebo_legaltext_replacement_offers.json");
        Map<String, Object> testingCriteriaMap = objectMapper.readValue(jsonFile, Map.class);
        Assertions.assertNotNull(testingCriteriaMap);

        OfferResolverResponseDTO offerResolverResponse = objectMapper.convertValue(testingCriteriaMap.get("resolverResponse"), OfferResolverResponseDTO.class);
        OfferDeliveryResponse offerDeliveryResponse = objectMapper.convertValue(testingCriteriaMap.get("offerDeliveryResponse"), OfferDeliveryResponse.class);
        PartnerResponse partnerResponse = objectMapper.convertValue(testingCriteriaMap.get("partnerResponse"), PartnerResponse.class);

        when(partnerService.getPartner(any(), any())).thenReturn(partnerResponse);
        CompletableFuture<OfferResolverResponseDTO> offerResolverResponseFuture = CompletableFuture.completedFuture(offerResolverResponse);
        CompletableFuture<Map<UUID, List<Map<String, Object>>>> offerStateResponseFuture = CompletableFuture.completedFuture(null);

        OfferDelivery offerDelivery = Mockito.mock(OfferDelivery.class);
        when(offerDelivery.getOfferDeliveryDataMap(any())).thenCallRealMethod();

        Map<UUID, OfferDeliveryOfferObject> offerDeliveryMap = offerDelivery.getOfferDeliveryDataMap(offerDeliveryResponse);
        List<UUID> OfferDeliveryIdList = offerDeliveryMap.keySet().stream().toList();

        //English test
        List<OfferServiceResponse> response = offerService.getOfferResponseAsync(offerResolverResponseFuture,offerDeliveryResponse,null,offerStateResponseFuture,OfferDeliveryIdList);
        Assertions.assertNotEquals(offerResolverResponse.getOffers().get(0).getStartDate(),response.get(0).getStartDate());
        Assertions.assertEquals(offerDeliveryResponse.getData().get(0).getEndDate(),response.get(0).getEndDate());
        Assertions.assertTrue(response.get(0).getLegalText().contains("January 1, 2024"));
        Assertions.assertTrue(response.get(0).getLegalText().contains("January 1, 2025"));
        System.out.println("legal text test: "+ response.get(0).getLegalText());

        // French Test
        OfferRequest offerRequest = OfferRequest.builder().language("fr-CA").build();
        response = offerService.getOfferResponseAsync(offerResolverResponseFuture,offerDeliveryResponse,offerRequest,offerStateResponseFuture,OfferDeliveryIdList);
        Assertions.assertTrue(response.get(0).getLegalText().contains("1 janvier 2024"));
        Assertions.assertTrue(response.get(0).getLegalText().contains("1 janvier 2025"));
        System.out.println("legal text test: "+ response.get(0).getLegalText());

    }
}
