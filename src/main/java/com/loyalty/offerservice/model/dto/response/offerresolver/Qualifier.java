package com.loyalty.offerservice.model.dto.response.offerresolver;

import com.fasterxml.jackson.annotation.JsonProperty;

public  enum Qualifier {

    @JsonProperty("product")
    Product("product"),

    @JsonProperty("storewide")
    StoreWide("storewide"),

    @JsonProperty("category")
    Category("category"),

    @JsonProperty("cashRedemption")
    CashRedemption("cashRedemption"),

    @JsonProperty("custom")
    Custom("custom"),

    @JsonProperty("fuel")
    Fuel("fuel"),

    @JsonProperty("perProduct")
    PerProduct("perProduct"),

    @JsonProperty("perUnit")
    PerUnit("perUnit"),

    @JsonProperty("perDollar")
    PerDollar("perDollar"),

    @JsonProperty("frequency")
    Frequency("frequency");

    Qualifier(String noAction) {
    }
}
