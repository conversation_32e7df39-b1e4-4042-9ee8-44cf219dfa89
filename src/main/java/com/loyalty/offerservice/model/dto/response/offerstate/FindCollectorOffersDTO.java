package com.loyalty.offerservice.model.dto.response.offerstate;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class FindCollectorOffersDTO {
    @JsonProperty("offers")
    private List<CollectorOfferDTO> offers;
    private Integer returnedCount;
}
