---
title: Feed mapping
type: docs
weight: 20.2
no_list: true
---

# Feed mapping

There are a lot of filters and sort combinitions in Offer Service. This gives clients a lot of flexibility in how to use `/offers` but can get combursome to keep specifing the same query parameter in every request across multiple places. To solve for this issue we have feed mapping. Feed mapping is a way to predefine a set of query parameters and their values. Each of this feed mapping is mapped to a specific `X-Origin-Client`. This way the client can just specify the `X-Origin-Client` and the feed mapping will be applied. 

> Feed mapping is managed by Offer team and is not configurable via API by the client. 

## Example of feed mapping

Mapping file:

```json
{
  "filterRequest": {
    "excludePartnerId": [
      "22a2cdfd-ff82-45f6-bc94-c14a3a533922"
    ]
  },
  "paginationRequest": {
    "sort": [
      "regionrelevance"
    ]
  }
}
```

Mapped to `X-Origin-Client: external:client:test`

The following requests are equvilant:

```http
GET /offers?region=ON HTTP/1.1
Host: cdn.airmilesapis.ca
X-Origin-Client: external:client:test
Accept-Language: en-US
```

```http
GET /offers?region=ON&sort=regionrelevance&exclude_partner_id=22a2cdfd-ff82-45f6-bc94-c14a3a533922 HTTP/1.1
Host: cdn.airmilesapis.ca
X-Origin-Client: external:client:other
Accept-Language: en-US
```

## Overriding feed mapping

When a client has set up a feed and makes a request to offer service `/offers`, the feed mapping is applied to the request. The feed mapping can be overriden by the query parameters specified in the request. The query parameters specified in the request will take precedence over the feed mapping.

Taking the example feed setup above if we make a request with `sort=collectorrelevance` the response will be sorted by `collectorrelevance` instead of `regionrelevance`.

```http
GET /offers?region=ON&sort=collectorrelevance HTTP/1.1
Host: cdn.airmilesapis.ca
X-Origin-Client: external:client:test
Accept-Language: en-US
```

> The above request will sort the offers by `collectorrelevance` instead of `regionrelevance` even though the feed mapping has `regionrelevance` as the sort.

