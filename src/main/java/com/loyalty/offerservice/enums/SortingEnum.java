package com.loyalty.offerservice.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.UUID;

@Getter
@AllArgsConstructor
public enum SortingEnum {

    PARTNER_ID("partnerId"),
    PROMOTION_ID("promotionId"),
    MASS_OFFER_ASC("massOffer"),
    MASS_OFFER_DESC("-massOffer"),
    DISPLAY_PRIORITY_ASC("displayPriority"),
    DISPLAY_PRIORITY_DESC("-displayPriority"),
    END_DATE_ASC("endDate"),
    END_DATE_DESC("-endDate"),
    RELEVANCE_COLLECTOR("collectorrelevance"),
    RELEVANCE_REGION("regionrelevance");

    private final String value;
    private static final String PARTNER_ID_PREFIX = "partnerId:";

    public String value() {
        return value;
    }

    public static void validateList(List<String> values) {
        if (values == null || values.isEmpty()) {
            return;
        }

        for (String value : values) {
			if (value.startsWith(PARTNER_ID_PREFIX)) {
				if (!isValidPartnerIdSort(value)) {
					throw new IllegalArgumentException(
                        String.format("Sorting by partnerId was detected but invalid UUID was provided as value: %s ", value));
				}
                // Checked the partnerId sort and its valid so continue on with the other sort values
                continue;
			}
            if (getKey(value) == null) {
                throw new IllegalArgumentException(
                        String.format("Sorting by %s resolves to the value of null. Sort value not supported", value));
            }
        }
    }

    public static SortingEnum getKey(String value) {
        
        return Arrays.stream(SortingEnum.values())
                .filter(item -> item.getValue().equals(value))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * Validates the partnerId value is valid UUID
     */
    private static boolean isValidPartnerIdSort(String value) {
        
        String uuidPart = value.substring(PARTNER_ID_PREFIX.length());
        try {
            UUID.fromString(uuidPart);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }
    
}
