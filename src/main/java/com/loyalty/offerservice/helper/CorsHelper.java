package com.loyalty.offerservice.helper;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

import static net.logstash.logback.marker.Markers.append;

@Component
@Slf4j
public class CorsHelper {

    @Value("${allowed-origins.authenticated}")
    String[] authenticatedOriginList;

    // Store pre-compiled patterns for efficiency
    private final List<Pattern> allowedOriginPatterns = new ArrayList<>();

    @PostConstruct
    public void init() {
        log.info("Compiling allowed origin patterns for CORS...");
        for (String origin : authenticatedOriginList) {
            String pattern = origin.replace(".", "\\.").replace("*", ".*");
            allowedOriginPatterns.add(Pattern.compile("^" + pattern + "$"));
            log.debug("Compiled CORS pattern: {}", pattern);
        }
    }

    public boolean isAllowedOrigin(String incomingOrigin) {
        if (incomingOrigin == null) {
            return false;
        }
        for (Pattern pattern : allowedOriginPatterns) {
            if (pattern.matcher(incomingOrigin).matches()) {
                log.debug("Incoming origin '{}' matched allowed pattern '{}'", incomingOrigin, pattern.pattern());
                return true;
            }
        }
        log.warn(append("incomingOrigin", incomingOrigin)
                        .and(append("allowedOrigins", authenticatedOriginList)),
                "Incoming Origin not in Allowed Origin list.");
        return false;
    }

    public HttpHeaders getPublicCorsHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, "*");
        headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_CREDENTIALS, "false");
        headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_HEADERS, "*");
        headers.put(HttpHeaders.ACCESS_CONTROL_ALLOW_METHODS, List.of("GET", "OPTIONS"));

        return headers;
    }

    public HttpHeaders getAuthenticatedCorsHeaders(String origin) {
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, origin);
        headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_CREDENTIALS, "true");
        headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_HEADERS, "*");
        headers.put(HttpHeaders.ACCESS_CONTROL_ALLOW_METHODS, List.of("PUT","GET","OPTIONS"));
        return headers;
    }
}
