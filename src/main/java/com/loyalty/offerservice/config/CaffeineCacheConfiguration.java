package com.loyalty.offerservice.config;

import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@EnableCaching
public class CaffeineCacheConfiguration {

    @Value("${cache-expire.default.value}")
    String defaultExpireValue;

    @Bean
    public Caffeine caffeineConfig() {
        return Caffeine.newBuilder().expireAfter(new CustomExpiryGenerator(defaultExpireValue));
    }

    @Bean
    public CaffeineCacheManager cacheManager(Caffeine caffeine) {
        CaffeineCacheManager caffeineCacheManager = new CaffeineCacheManager();
        caffeineCacheManager.setCaffeine(caffeine);
        return caffeineCacheManager;
    }

}
