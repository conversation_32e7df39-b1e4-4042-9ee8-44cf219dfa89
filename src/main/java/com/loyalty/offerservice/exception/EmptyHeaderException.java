package com.loyalty.offerservice.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.BAD_REQUEST)
public class EmptyHeaderException extends AppRuntimeException {

    public EmptyHeaderException(String message) {
        super(String.valueOf(HttpStatus.BAD_REQUEST.value()), message);
    }

    public EmptyHeaderException(String errorCode, String message) {
        super(errorCode, message);
    }

    public EmptyHeaderException(String errorCode, String message, Throwable cause) {
        super(errorCode, message, cause);
    }

}
