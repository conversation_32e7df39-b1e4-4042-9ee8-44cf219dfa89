spring:
  application:
    name: offer-service

logging:
  level:
    com.loyalty.offerservice.service: DEBUG

offer-delivery:
  internal: http://localhost:1234

partner-service:
  base: http://localhost:1234

offer-state:
  base: http://localhost:1234

partner:
  logo:
    url: https://dev.cdn.airmilesapis.ca/partner-logo

offer:
  image:
    url: https://dev.cdn.airmilesapis.ca/offer-image/

resolver-service:
  lambda-function: arn:aws:lambda:us-east-1:022122625207:function:dev-nova-offer-resolver-api-getOffers-lambda

cache-expire:
  partner:
    value: 3
  offer-resolver:
    value: 3
  default:
    value: 2

allowed-origins:
  authenticated: http://127.0.0.1:*,http://localhost:*,https://*.airmilesshops.ca,https://beta.airmiles.ca,https://author-beta.airmiles.ca,https://author-loyaltyone-prod.adobecqms.net,https://www.airmiles.ca,https://airmiles.ca,https://ams.airmiles.ca,https://travel2.airmiles.ca,https://travel.airmiles.ca,https://author-loyaltyone-qa.adobecqms.net