package com.loyalty.offerservice.config;

import com.github.benmanes.caffeine.cache.Expiry;
import com.loyalty.offerservice.cache.Cacheable;

import java.util.concurrent.TimeUnit;

public class CustomExpiryGenerator implements Expiry<String, Cacheable> {
    String defaultExpireValue;

    public CustomExpiryGenerator(String defaultExpireValue) {
        this.defaultExpireValue = defaultExpireValue;
    }

    @Override
    public long expireAfterCreate(String key, Cacheable value, long currentTime) {
        long seconds = value.getExpireValue() > 0 ? value.getExpireValue() : Long.valueOf(defaultExpireValue);
        return TimeUnit.HOURS.toNanos(seconds);
    }

    @Override
    public long expireAfterUpdate(String key, Cacheable value,
                                  long currentTime, long currentDuration) {
        return currentDuration;
    }

    @Override
    public long expireAfterRead(String key, Cacheable value,
                                long currentTime, long currentDuration) {
        return currentDuration;
    }
}

