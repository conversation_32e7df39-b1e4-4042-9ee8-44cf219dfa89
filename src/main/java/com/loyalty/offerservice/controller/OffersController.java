package com.loyalty.offerservice.controller;

import com.loyalty.offerservice.enums.LanguageCode;
import com.loyalty.offerservice.enums.ProgramTypeEnum;
import com.loyalty.offerservice.enums.RegionEnum;
import com.loyalty.offerservice.enums.SortingEnum;
import com.loyalty.offerservice.exception.EmptyHeaderException;
import com.loyalty.offerservice.model.dto.request.FilterRequest;
import com.loyalty.offerservice.model.dto.request.OfferRequest;
import com.loyalty.offerservice.model.dto.request.PaginationRequest;
import com.loyalty.offerservice.model.dto.response.OfferServiceExtendedOfferResponse;
import com.loyalty.offerservice.model.dto.response.OfferServiceExtendedOfferResponseForOfferId;
import com.loyalty.offerservice.service.GenericException;
import com.loyalty.offerservice.service.OfferService;
import com.loyalty.offerservice.util.States;
import com.loyalty.offerservice.util.TimestampLocalizationUtil;
import com.loyalty.offerservice.util.querymapping.QueryMappingConfig;
import com.loyalty.offerservice.util.querymapping.QueryMappingUtil;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Size;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

import static com.loyalty.offerservice.exception.utils.ExceptionConstantsUtils.EMPTY_HEADER_MESSAGE;
import static net.logstash.logback.marker.Markers.append;

@RestController
@RequestMapping(value = "/offers", headers = { "Accept=application/json" })
@Validated
@Slf4j
public class OffersController {

    @Autowired
    QueryMappingUtil queryMappingUtil;

    private final OfferService offerService;

    public OffersController(OfferService offerService) {
        this.offerService = offerService;
    }

    @GetMapping
    public ResponseEntity<OfferServiceExtendedOfferResponse> getOffers(
            @Valid @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) @RequestHeader(value = "X-Timestamp", required = false) LocalDateTime localDate,
            @RequestHeader(value = "X-Correlation-Id", required = false) UUID correlationId,
            @RequestHeader(value = "X-Origin-Client") String originClient,
            @RequestHeader(value = "Collector-Number", required = false) Long collectorNumber,
            @RequestHeader(value = "Accept-Language", defaultValue = "en-US") LanguageCode acceptLanguage,
            @RequestHeader(value = "Origin", required = false) String origin,
            @RequestParam(required = true) RegionEnum region,
            @RequestParam(required = false) @Size(max = 120) List<UUID> partner_id,
            @RequestParam(required = false) @Size(max = 15) List<UUID> category_id,
            @RequestParam(required = false) @Size(max = 15) List<UUID> subcategory_id,
            @RequestParam(required = false) UUID promotion_id,
            @RequestParam(required = false) List<String> type,
            @RequestParam(required = false) List<ProgramTypeEnum> program_type,
            @RequestParam(required = false) String states,
            @RequestParam(required = false) List<String> availability,
            @RequestParam(required = false) Boolean mass_offer,
            @RequestParam(required = false, defaultValue = "false") Boolean extended_metadata,
            @RequestParam(required = false) List<String> sort,
            @RequestParam(defaultValue = "0", required = false) @Min(0) Integer offset,
            @RequestParam(defaultValue = "20", required = false) @Min(1) @Max(96) Integer limit,
            @RequestParam(required = false) UUID experiment,
            @RequestParam(required = false) @Size(max = 120) List<UUID> exclude_partner_id) throws Exception {

        if (correlationId == null) {
            correlationId = UUID.randomUUID();
        }

        MDC.put("correlationId", String.valueOf(correlationId));
        MDC.put("originClient", originClient);
        MDC.put("origin", origin);
        MDC.put("uri", "/offers");
        MDC.put("requestType", "GET");
        MDC.put("collectorNumber", String.valueOf(collectorNumber));

        PaginationRequest paginationRequest = null;
        FilterRequest filterRequest = null;

        paginationRequest = PaginationRequest.builder().sort(sort).offset(offset).limit(limit).build();


        if (states != null && !states.isEmpty()) {
            filterRequest = FilterRequest.builder()
                    .region(region)
                    .eventBased(true)
                    .states(States.getInstance(states))
                    .build();
        } else {
            filterRequest = FilterRequest.builder()
                    .region(region)
                    .partnerId(partner_id)
                    .categoryId(category_id)
                    .subCategoryId(subcategory_id)
                    .promotionId(promotion_id)
                    .type(type)
                    .programType(program_type)
                    .states(States.getInstance(states))
                    .availability(availability)
                    .experiment(experiment)
                    .massOffer(mass_offer)
                    .excludePartnerId(exclude_partner_id)
                    .build();


            if (QueryMappingConfig.clientHasMapping(originClient)) {

                FilterRequest feedFilterRequest = queryMappingUtil.getFeedFilterMapping(originClient);
                PaginationRequest feedPaginationRequest = queryMappingUtil.getFeedPaginationMapping(originClient);
                log.info(
                        append("incomingFilterRequest", filterRequest)
                                .and(append("feedFilterRequest", feedFilterRequest))
                                .and(append("incomingPaginationRequest", paginationRequest))
                                .and(append("feedPaginationRequest", feedPaginationRequest)),
                        "Apply feed mapping with incoming request");

                filterRequest = feedFilterRequest.override(filterRequest);
                paginationRequest = feedPaginationRequest.override(paginationRequest);
            }

        }

        log.info(append("filterRequest", filterRequest).and(append("paginationRequest", paginationRequest)),
                "GET Offers: Request Received");

        SortingEnum.validateList(sort);

        if (states != null && !states.isEmpty() && !states.trim().isEmpty()) {
            if (collectorNumber == null) {
                throw new EmptyHeaderException(String.format(EMPTY_HEADER_MESSAGE, "Collector-Number"));
            }
        }

        OfferRequest request = OfferRequest.builder().filters(filterRequest).pagination(paginationRequest)
                .extendedMetadata(extended_metadata).localTime(getTimeStamp(localDate, String.valueOf(region)))
                .correlationId(correlationId).originClient(originClient).language(acceptLanguage.getCode())
                .collectorNumber(collectorNumber).origin(origin).build();

        OfferServiceExtendedOfferResponse responseBody = offerService.getOffers(request);
        HttpHeaders responseHeaders = offerService.addResponseHeaders(request);

        log.info(append("status", HttpStatus.OK.value()).and(append("headers", responseHeaders)),
                "GET Offers: Response Sent");
        MDC.clear();

        return ResponseEntity.ok().headers(responseHeaders).body(responseBody);
    }

    @GetMapping("/{offerId}")
    public ResponseEntity<?> getOffersByOfferId(@PathVariable("offerId") UUID offerId,
            @RequestHeader(value = "X-Timestamp", required = false) LocalDateTime localDate,
            @RequestHeader(value = "X-Correlation-Id", required = false) UUID correlationId,
            @RequestHeader(value = "X-Origin-Client") String originClient,
            @RequestHeader(value = "Collector-Number", required = false) Long collectorNumber,
            @RequestHeader(value = "Accept-Language", defaultValue = "en-US") LanguageCode acceptLanguage,
            @RequestHeader(value = "Origin", required = false) String origin,
            @RequestParam RegionEnum region) throws GenericException, IOException {
        if (correlationId == null) {
            correlationId = UUID.randomUUID();
        }

        MDC.put("correlationId", String.valueOf(correlationId));
        MDC.put("originClient", originClient);
        MDC.put("origin", origin);
        MDC.put("uri", String.format("/offers/%s", offerId));
        MDC.put("requestType", "GET");
        MDC.put("collectorNumber", String.valueOf(collectorNumber));
        MDC.put("offerId", offerId.toString());

        FilterRequest filterRequest = FilterRequest.builder().region(region).build();

        log.info(append("filterRequest", filterRequest), "GET Offer By OfferId: Request Received");

        OfferRequest request = OfferRequest.builder().correlationId(correlationId).filters(filterRequest)
                .offerId(offerId).localTime(getTimeStamp(localDate, String.valueOf(region)))
                .correlationId(correlationId).originClient(originClient)
                .collectorNumber(collectorNumber).language(acceptLanguage.getCode()).origin(origin).build();

        OfferServiceExtendedOfferResponseForOfferId responseBody = offerService.getOfferByOfferId(request);
        HttpHeaders responseHeaders = offerService.addResponseHeaders(request);

        log.info(append("status", HttpStatus.OK.value()).and(append("headers", responseHeaders)),
                "GET Offer By OfferId: Response");

        MDC.clear();

        return ResponseEntity.ok().headers(responseHeaders).body(responseBody);
    }

    private LocalDateTime getTimeStamp(LocalDateTime localDateTime, String region) {
        if (localDateTime == null) {
            return LocalDateTime.parse(TimestampLocalizationUtil.getFormattedLocalTimestamp(String.valueOf(region)));
        } else {
            return localDateTime;
        }
    }
}