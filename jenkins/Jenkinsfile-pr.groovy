@Library('jenkins-shared-lib-v2') _
def gitTag = new Date().format('yyyyMMddHHmmss')

pipeline {
    agent { node { label 'aws-ec2' } }
    stages {
        stage('Get Pipeline Meta-Data') {
            steps {
                script {
                    def appMeta = readProperties(file: 'meta.config')
                    repoName = appMeta.APP_ID
                    region = appMeta.REGION
                }
            }
        }
        stage('Unit Tests') {
            steps {
                script {
                    if(region){
                        dockerUtils.buildV2('-f dockerfiles/Dockerfile-Release .', repoName, region)
                    }else{     
                        dockerUtils.buildV2('-f dockerfiles/Dockerfile-Release .', repoName)
                    }
                  
                }
            }
        }
        stage('Docker Build') {
            environment {
                DOCKER_BUILDKIT='1'
            }
            steps {
                script {
                    //
                    println 'File structure in current directory'
                    sh 'ls'
                    // get docker build artifacts to scan in sonarqube along with the src code
                    
                    dockerUtils.buildV2('--build-arg BUILDKIT_INLINE_CACHE=1 --target sonarqube --output type=local,dest=sonarqube-pr-build -f dockerfiles/Dockerfile-Release .', repoName, region)
                    sh 'mkdir build && unzip sonarqube-pr-build/build.zip -d build/.'
                    
                    // Here we stash the important files which will be used in the scan stage.
                    // We do this so that build and scan steps can work independent of each other
                    // in different agents.
                    stash includes: 'build/**', name: 'build'
                    stash includes: 'src/**', name: 'src'
                }
            }
        }
        stage('SonarQube Scan') {
            agent {
                docker {
                    image '277983268692.dkr.ecr.us-east-1.amazonaws.com/ubuntu-22-slim'
                }
            }
            steps {
                script {
                    unstash 'build'
                    unstash 'src'
                    // run sonarqube scan
                    sonarqube.checkQualityGate("-Dsonar.projectVersion=$gitTag")
                }
            }
        }
    }
    post {
        always {
            script {
                gitUtils.reportPRStatus()
            }
        }
        cleanup {
            deleteDir()
            dir("${workspace}@tmp") {
                deleteDir()
            }
            dir("${workspace}@script") {
                deleteDir()
            }
        }      
    }
}
