server:
  port: 8080
  servlet:
    context-path: /offer-service
  error:
    include-message: always

spring:
  application:
    name: offer-service

cache-expire:
  partner:
    value: 9223372036854775807
  offer-resolver:
    value: 3
  default:
    value: 2
  default-local: "en-US"

logging:
  level:
    root: DEBUG

management:
  endpoint:
    health:
      show-details: "ALWAYS"

cache-control:
  max-age: 300


allowed-origins:
  authenticated: http://127.0.0.1:*,http://localhost:*,https://*.airmilesshops.ca,https://beta.airmiles.ca,https://author-beta.airmiles.ca,https://author-loyaltyone-prod.adobecqms.net,https://www.airmiles.ca,https://airmiles.ca,https://ams.airmiles.ca,https://travel2.airmiles.ca,https://travel.airmiles.ca,https://author-loyaltyone-qa.adobecqms.net,https://bmoupgrade.uat.airmiles.ca,https://b2c.mcstaging2.kent.ca*,https://kent.ca*