---
title: Mapping
linkTitle: Mapping
description: Refrence <PERSON> with partner, category and promotions.
type: docs
no_list: true
weight: 19
---

This document contains the mappings of UUID's to Partners, Categories, SubCategories, Promotions.
We can discuss later how Offer Service `/offers` API can be used to get some of these mappings aswell.

## Partner

| Id                                   | Name                          |
| ------------------------------------ | ----------------------------- |
| 5058f22a-1f5c-4625-b8ff-ffe116756722 | TIMBER MART                   |
| 33d0cf88-5eae-4186-be03-05092061eed9 | HP                            |
| 63e8543d-fbf7-458e-8712-d25332da3614 | AIR MILES Travel              |
| 22a2cdfd-ff82-45f6-bc94-c14a3a533922 | BMO Bank of Montreal          |
| e914624c-9676-4287-8ce9-04de68dd0507 | airmilesshops.ca              |
| 87ab4dd0-65b5-41a3-acd2-b97540dc1aff | United Van Lines              |
| ef73e58a-db63-4b27-baae-bd1e1d2ae86c | Peter & Paul's Gifts          |
| 0c988dcd-b2a9-4391-b2c4-48f0294d5658 | Canadian Appliance Source     |
| fbdc481f-3fa4-40e9-b1ca-d4dab4c36c30 | All Purpose Realty            |
| 11892e84-a87e-48b0-bf13-fcd0be389588 | Additional AIR MILES Partners |
| c13f2e1b-f828-4781-9369-fde0581c400d | Onlia Insurance               |
| 19e1bb2b-88d4-4b37-be72-262a694ae58d | National Car Rental           |
| 4a755252-876b-478e-9440-42961525e307 | Metro                         |
| 1563e943-25fd-480b-b77d-94aedc7202af | LG Electronics                |
| cbfcd38f-b1c0-4443-881c-c342833649a0 | Fresh                         |
| 91adc45f-0037-4fa8-9424-2e6e8f999467 | SMASH + TESS                  |
| b90e694d-3510-4cc2-84be-f9b6f61bd31b | Shell Mobility                |
| 27568f1b-c48d-4c27-8d9b-7c67666308c2 | Dollarama                     |
| 98d43a31-1c04-4db7-bfd3-b9a4a0ced034 | Mayflower                     |
| 9ab6f70f-588e-4460-b14c-ad4ad8c9a524 | La Vie en Rose                |
| 1b36212c-87a6-448c-82a7-c2f28c8b6d52 | HelloFresh                    |
| 5a92c696-cddd-4255-920e-1f74b121e7ea | Kernels                       |
| 40786f15-10e1-490f-a4bf-38003770ad73 | redtag.ca                     |
| 38178928-9281-42aa-9f70-8c39c453baf1 | Alamo Rent a Car              |

> There are many partners not listed in this list, you can contact _REPLACE_ME_ to get more details

## Categories

| Id                                   | Name                                   |
| ------------------------------------ | -------------------------------------- |
| 22b666ae-97c2-4e57-a437-e977de6beef4 | Grocery (Épicerie)                     |
| 1ba636e3-799d-44c3-9c84-42a3df6430b0 | Household Supplies (Entretien ménager) |
| 0f597ee1-16b8-4a4f-beb7-4654cfd0b45a | Pharmacy (Pharmacie)                   |
| c7a208f3-8f94-4460-b76c-3c75ae53417d | Liquor (Alcool)                        |
| 38d878d7-c3e0-4ea1-8e6c-d4759f307042 | Pets (Animaux de compagnie)            |
| 77c6067e-bd25-43a9-826c-7a4788581952 | Home and Garden (Maison et jardin)     |
| 0333d0e5-68be-4833-939f-3a20d0cc9806 | Fuel and Auto (Carburant et auto)      |
| 0cbc9203-2811-4eae-9dc1-68da692658b8 | Financial (Produits financiers)        |
| 0a889711-9ab8-4972-a0b5-b9eafc8fa312 | Retail (Articles de détail)            |
| 0387d04b-2736-48e4-b804-ac50f4222b79 | Office (Bureau)                        |
| 121fa2dd-1fab-4492-8c5d-fb34624f4dda | Travel (Voyage)                        |
| 17180943-4cef-4fb8-833d-9919e359e4f3 | Gift Cards (Cartes-cadeaux)            |
| e10dea41-3aee-4a24-bc8d-0d8de8c3e5ae | airmilesshops.ca (airmilesshops.ca)    |
| cc1b19d6-72e0-47bc-8b81-2b1fbb49d050 | Other (Autre)                          |

## SubCategories

| Id                                   | Name                                                       | Parent                                 |
| ------------------------------------ | ---------------------------------------------------------- | -------------------------------------- |
| 4683cd1f-bba8-4dd0-b960-4324a246960b | Baby (Bébé)                                                | Grocery (Épicerie)                     |
| 873c7bfa-745e-4bdd-afe2-55425821d721 | Bakery (Boulangerie)                                       | Grocery (Épicerie)                     |
| d614fed9-8c17-4c20-a179-2351e54679c1 | Beverages (Boissons)                                       | Grocery (Épicerie)                     |
| eab2662b-bfc5-4457-8cbf-33d9e10f9bb6 | Dairy and Eggs (Produits laitiers et œufs)                 | Grocery (Épicerie)                     |
| f1f75e01-8b8f-460c-bc89-8bdb40060e7c | Deli and Prepared Food (Charcuterie et plats préparés)     | Grocery (Épicerie)                     |
| da4cdf1b-e2f0-4b1c-99b7-c0ce4696292f | Frozen Foods (Produits surgelés)                           | Grocery (Épicerie)                     |
| 37485ad6-07ad-4145-80f1-fbecdd46a26a | Fruits and Vegetables (Fruits et légumes)                  | Grocery (Épicerie)                     |
| 0d3245ac-9812-4bc3-9020-16b1349f1531 | Meat and Seafood (Viande et produits de la mer)            | Grocery (Épicerie)                     |
| be58904e-4ae9-49fc-ac6d-0e966dc193bb | Pantry (Garde-manger)                                      | Grocery (Épicerie)                     |
| d295e726-8205-4a41-8207-a6e31714dde3 | Snacks (Collations)                                        | Grocery (Épicerie)                     |
| 53a177af-b923-43ea-ac7d-0230ea0db805 | Vegan and Vegetarian (Aliments végétariens et végétaliens) | Grocery (Épicerie)                     |
| 3c4b099b-061c-493b-8bc4-4ced119949c4 | Organic Groceries (Épicerie Biologique)                    | Grocery (Épicerie)                     |
| e9c77b08-a3e5-4846-90e5-9e9ad0f34de1 | Other Grocery (Autre Épicerie)                             | Grocery (Épicerie)                     |
| 279ecbd8-b81b-4d46-9726-8fdd4dee8af8 | Cleaning (Nettoyage)                                       | Household Supplies (Entretien ménager) |
| 43cf7742-601f-43ef-9934-3a461493eb4e | Laundry (Lessive)                                          | Household Supplies (Entretien ménager) |
| 0b9d187d-bbf2-4884-9e10-2df4c97bcd3f | Paper and Plastics (Papier et plastiques)                  | Household Supplies (Entretien ménager) |
| 4683cd1f-bba8-4dd0-b960-4324a246960b | Baby (Bébé)                                                | Pharmacy (Pharmacie)                   |
| dc725f55-1c70-4088-8108-cc199bd2ca32 | Beauty (Beauté)                                            | Pharmacy (Pharmacie)                   |
| ef1ff9bd-6032-4c14-8c51-8b1f43dd0970 | Medicine and Health (Médicaments et santé)                 | Pharmacy (Pharmacie)                   |
| d871a234-93f3-47f0-991a-b230cb797d74 | Personal Care (Soins personnels)                           | Pharmacy (Pharmacie)                   |
| 0dd2f52d-af4a-45f9-aa1c-c66cd13f16e6 | Beer and Cider (Bières et cidres)                          | Liquor (Alcool)                        |
| 6cad2024-2bc8-47f5-b563-53f97c1f7205 | Coolers (Coolers)                                          | Liquor (Alcool)                        |
| b3962476-bb50-47c2-af54-3bdec43fa499 | Spirits (Spiritueux)                                       | Liquor (Alcool)                        |
| 8b618c88-cd2c-47fa-9de7-677fe1d3737f | Wine (Vins)                                                | Liquor (Alcool)                        |



## Mapping from Offer Service

We can make a call to the Offer Service API to get these `extended_metedata` values. 

**Request**
```
/offers?region=ON&extended_metadata=true
```

**Response**
```json
{
    "offers":[],
    "metadata":{
        "total": N
        "partners": [...],
        "categories": [...],
    }
}

```

The field `partners` in the response has id and labels. *Region Dependent*

The field `categories` in the response contains id and labels for categories and subcategories. *Region agnostic*