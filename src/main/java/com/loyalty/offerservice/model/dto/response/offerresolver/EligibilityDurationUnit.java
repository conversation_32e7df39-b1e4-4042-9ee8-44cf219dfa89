package com.loyalty.offerservice.model.dto.response.offerresolver;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;


public enum EligibilityDurationUnit {

    @JsonProperty("days")
    DAYS("days"),

    @JsonProperty("hours")
    HOURS("hours"),

    @JsonProperty("minutes")
    MINUTES("minutes");

    private final String value;

    //Constructor that associates the value with each constant
    EligibilityDurationUnit(String value) {
        this.value = value;
    }

    //Method to get a value
    public String getValue() {
        return value;
    }
}