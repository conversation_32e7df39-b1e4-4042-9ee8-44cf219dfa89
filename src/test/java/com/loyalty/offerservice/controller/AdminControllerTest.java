package com.loyalty.offerservice.controller;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.loyalty.offerservice.exception.ResourceNotFoundException;
import com.loyalty.offerservice.model.dto.response.OfferServiceExtendedOfferResponseForOfferId;
import com.loyalty.offerservice.model.dto.response.offerdelivery.OfferDeliveryOfferObject;
import com.loyalty.offerservice.model.dto.response.offerresolver.OfferResolverResponseDTO;
import com.loyalty.offerservice.model.dto.response.partner.PartnerResponse;
import com.loyalty.offerservice.service.offerdelivery.OfferDeliveryInternalImpl;
import com.loyalty.offerservice.service.offerresolver.OfferResolverServiceImpl;
import com.loyalty.offerservice.service.partner.PartnerClient;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.util.ResourceUtils;

import java.io.File;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.header;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;


@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
public class AdminControllerTest {

    @Autowired
    ObjectMapper objectMapper;
    @MockBean
    PartnerClient partnerClient;
    @MockBean
    OfferDeliveryInternalImpl offerDeliveryService;
    @MockBean
    OfferResolverServiceImpl offerResolver;
    @Autowired
    private MockMvc mockMvc;

    @Test
    public void testMassOffer() throws Exception {

        // Set up the JSON file for response validation
        File jsonFile = ResourceUtils.getFile("classpath:admin_offers_flow.json");
        Map<String, Object> testingCriteriaMap = objectMapper.readValue(jsonFile, Map.class);
        Assertions.assertNotNull(testingCriteriaMap);

        // Mock the responses for the 'resolver', 'offerDelivery', 'partner' objects
        OfferResolverResponseDTO offerResolverResponse = objectMapper.convertValue(testingCriteriaMap.get("resolverResponseTestMass"), OfferResolverResponseDTO.class);
        PartnerResponse partnerResponse = objectMapper.convertValue(testingCriteriaMap.get("partnerResponse"), PartnerResponse.class);

        // Mock the service responses
        when(offerResolver.getOffer(any(), any())).thenReturn(offerResolverResponse);
        when(partnerClient.getPartner(any(), any())).thenReturn(partnerResponse);

        // Perform the controller call
        mockMvc.perform(get("/admin/offers/e90c79b6-eb53-4056-9cda-1ddf543b15f9?")
                        .header("X-Origin-Client", "client-origin")
                        .header("Collector-Number", "80000000110")
                        .header("Accept-Language", "en-US"))
                .andExpect(status().isOk()).andExpect(header().string("Cache-Control", "no-cache"));

        mockMvc.perform(get("/admin/offers/e90c79b6-eb53-4056-9cda-1ddf543b15f9?")
                        .header("X-Origin-Client", "client-origin")
                        .header("Accept-Language", "en-US"))
                .andExpect(status().isOk()).andExpect(header().string("Cache-Control", "public, max-age=300"));
    }

    @Test
    public void testHappyPathTargetedWithCollectorNumber() throws Exception {

        // Set up the JSON file for response validation
        File jsonFile = ResourceUtils.getFile("classpath:admin_offers_flow.json");
        Map<String, Object> testingCriteriaMap = objectMapper.readValue(jsonFile, Map.class);
        Assertions.assertNotNull(testingCriteriaMap);

        // Mock the responses for the 'resolver', 'offerDelivery', 'partner' objects
        OfferResolverResponseDTO offerResolverResponse = objectMapper.convertValue(testingCriteriaMap.get("resolverResponseTargeted"), OfferResolverResponseDTO.class);
        OfferDeliveryOfferObject offerDeliveryResponse = objectMapper.convertValue(testingCriteriaMap.get("offerDeliveryResponseTargeted"), OfferDeliveryOfferObject.class);
        PartnerResponse partnerResponse = objectMapper.convertValue(testingCriteriaMap.get("partnerResponse"), PartnerResponse.class);


        // Mock the service responses
        when(offerResolver.getOffer(any(), any())).thenReturn(offerResolverResponse);
        when(offerDeliveryService.getCollectorOfferByOfferId(any())).thenReturn(offerDeliveryResponse);
        when(partnerClient.getPartner(any(), any())).thenReturn(partnerResponse);

        // Perform the controller call
        mockMvc.perform(get("/admin/offers/2b1084a8-664b-4800-8c65-57773ec25073?")
                        .header("X-Origin-Client", "client-origin")
                        .header("Collector-Number", "80000000110")
                        .header("Accept-Language", "en-US"))
                .andExpect(status().isOk()).andExpect(header().string("Cache-Control", "no-cache"));
    }

    @Test
    public void testHappyPathTargetedWithCollectorNumber_NotTargetted() throws Exception {

        // Set up the JSON file for response validation
        File jsonFile = ResourceUtils.getFile("classpath:admin_offers_flow.json");
        Map<String, Object> testingCriteriaMap = objectMapper.readValue(jsonFile, Map.class);
        Assertions.assertNotNull(testingCriteriaMap);

        // Mock the responses for the 'resolver', 'offerDelivery', 'partner' objects
        OfferResolverResponseDTO offerResolverResponse = objectMapper.convertValue(testingCriteriaMap.get("resolverResponseTargeted"), OfferResolverResponseDTO.class);
        PartnerResponse partnerResponse = objectMapper.convertValue(testingCriteriaMap.get("partnerResponse"), PartnerResponse.class);


        // Mock the service responses
        when(offerResolver.getOffer(any(), any())).thenReturn(offerResolverResponse);
        when(offerDeliveryService.getCollectorOfferByOfferId(any())).thenThrow(new ResourceNotFoundException("err-exclusive", "Offer is Targeted (mass=false) AND Collector Number is not targeted for the offer"));
        when(partnerClient.getPartner(any(), any())).thenReturn(partnerResponse);

        // Perform the controller call
        MvcResult result = mockMvc.perform(get("/admin/offers/2b1084a8-664b-4800-8c65-57773ec25073?")
                        .header("X-Origin-Client", "client-origin")
                        .header("Collector-Number", "80000000110")
                        .header("Accept-Language", "en-US"))
                .andExpect(status().isOk()).andExpect(header().string("Cache-Control", "no-cache")).andReturn();

        OfferServiceExtendedOfferResponseForOfferId response = objectMapper.readValue(result.getResponse().getContentAsString(), OfferServiceExtendedOfferResponseForOfferId.class);

        Assertions.assertNotNull(response.getWarning());
    }

    @Test
    public void testHappyPathEBOWithCollectorNumber() throws Exception {

        // Set up the JSON file for response validation
        File jsonFile = ResourceUtils.getFile("classpath:admin_offers_flow.json");
        Map<String, Object> testingCriteriaMap = objectMapper.readValue(jsonFile, Map.class);
        Assertions.assertNotNull(testingCriteriaMap);

        // Mock the responses for the 'resolver', 'offerDelivery', 'partner' objects
        OfferResolverResponseDTO offerResolverResponse = objectMapper.convertValue(testingCriteriaMap.get("resolverResponseEBO"), OfferResolverResponseDTO.class);
        OfferDeliveryOfferObject offerDeliveryResponse = objectMapper.convertValue(testingCriteriaMap.get("offerDeliveryResponseEBO"), OfferDeliveryOfferObject.class);
        PartnerResponse partnerResponse = objectMapper.convertValue(testingCriteriaMap.get("partnerResponse"), PartnerResponse.class);


        // Mock the service responses
        when(offerResolver.getOffer(any(), any())).thenReturn(offerResolverResponse);
        when(offerDeliveryService.getCollectorOfferByOfferId(any())).thenReturn(offerDeliveryResponse);
        when(partnerClient.getPartner(any(), any())).thenReturn(partnerResponse);

        // Perform the controller call
        MvcResult result = mockMvc.perform(get("/admin/offers/90591b02-bf7c-445d-b87c-2a86cc5542c3?")
                        .header("X-Origin-Client", "client-origin")
                        .header("Collector-Number", "80000000110")
                        .header("Accept-Language", "en-US"))
                .andExpect(status().isOk()).andExpect(header().string("Cache-Control", "no-cache")).andReturn();

        OfferServiceExtendedOfferResponseForOfferId response = objectMapper.readValue(result.getResponse().getContentAsString(), OfferServiceExtendedOfferResponseForOfferId.class);
        Assertions.assertNotEquals(response.getOffer().getStartDate(), offerResolverResponse.getOffers().get(0).getStartDate());
        Assertions.assertNotEquals(response.getOffer().getEndDate(), offerResolverResponse.getOffers().get(0).getEndDate());
    }

    @Test
    public void testHappyPathEBOWithCollectorNumber_Warning() throws Exception {

        // Load test data from JSON file
        File jsonFile = ResourceUtils.getFile("classpath:admin_offers_flow.json");
        Map<String, Object> testingCriteriaMap = objectMapper.readValue(jsonFile, Map.class);
        Assertions.assertNotNull(testingCriteriaMap);

        // Mock resolver, offer delivery, and partner responses
        OfferResolverResponseDTO offerResolverResponse = objectMapper.convertValue(testingCriteriaMap.get("resolverResponseEBO"), OfferResolverResponseDTO.class);
        PartnerResponse partnerResponse = objectMapper.convertValue(testingCriteriaMap.get("partnerResponse"), PartnerResponse.class);


        // Mock service responses
        when(offerResolver.getOffer(any(), any())).thenReturn(offerResolverResponse);
        when(partnerClient.getPartner(any(), any())).thenReturn(partnerResponse);
        when(offerDeliveryService.getCollectorOfferByOfferId(any())).thenThrow(new ResourceNotFoundException("err-ebo-exclusive", "Offer is Targeted (mass=false) AND Collector Number is not targeted for the offer"));


        // Perform the controller call
        MvcResult result = mockMvc.perform(get("/admin/offers/90591b02-bf7c-445d-b87c-2a86cc5542c3?")
                        .header("X-Origin-Client", "client-origin")
                        .header("Collector-Number", "80000000110")
                        .header("Authorization", "e58ed763-928c-4155-bee9-fdbaaadc15f3")
                        .header("Accept-Language", "en-US"))
                .andExpect(status().isOk())
                .andExpect(header().string("Cache-Control", "no-cache"))
                .andReturn();

        OfferServiceExtendedOfferResponseForOfferId response = objectMapper.readValue(result.getResponse().getContentAsString(), OfferServiceExtendedOfferResponseForOfferId.class);
        Assertions.assertEquals(response.getOffer().getStartDate(), offerResolverResponse.getOffers().get(0).getStartDate());
        Assertions.assertEquals(response.getOffer().getEndDate(), offerResolverResponse.getOffers().get(0).getEndDate());
        Assertions.assertNotNull(response.getWarning());
    }
}