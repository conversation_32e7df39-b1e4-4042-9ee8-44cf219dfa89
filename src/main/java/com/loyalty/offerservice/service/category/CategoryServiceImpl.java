package com.loyalty.offerservice.service.category;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.loyalty.offerservice.model.dto.response.category.CategoriesResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStreamReader;

import static net.logstash.logback.marker.Markers.append;

@Service
@Slf4j
public class CategoryServiceImpl implements CategoryService {

    @Autowired
    ResourceLoader resourceLoader;

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * Get the Category/SubCategory structure from a JSON file
     *
     * @param locale - specify the language of the labels returned in the response
     * @return CategoriesResponse
     * @throws IOException - Throw with localizeMessage
     */
    @Override
    public CategoriesResponse getCategories(String locale) throws IOException {
        Resource resource = resourceLoader.getResource("classpath:mock/CategoryS3/Category_All.json");
        log.info(append("resource", resource.getURL()), "Resource URL");
        CategoriesResponse categoriesResponse = new CategoriesResponse(locale);
        try {
            categoriesResponse = objectMapper.readValue(new InputStreamReader(resource.getInputStream()),
                    CategoriesResponse.class);
        } catch (IOException e) {
            log.error("Unable to get Category/SubCategory data, error: {}", e.getLocalizedMessage());
            throw new IOException("Unable to get Category/SubCategory data, error: " + e.getLocalizedMessage());
        }
        return categoriesResponse;
    }
}
