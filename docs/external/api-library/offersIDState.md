---
title: PUT /offers/{id}/states
description: Used to update single offer state for a collector.
type: docs
weight: 20.3
---

# PUT /offers/{id}/states

API to update the state of an offer for a given collector. This API can only be used with a `Authorization` token, as thats how the API knows which collector should be associated with the action.

## Path Parameter

| Name | Rquired | Type                | Discription                             |
| ---- | ------- | ------------------- | --------------------------------------- |
| id   | ✔️       | UUID (single value) | Identify the offer to updated its state |

## Request Body

_required_

```json
{
  "states": [
    {
      "name": "name1",
      "value": "value1"
    }
  ]
}
```

The request body contains a `states: []` list which can contain multiple **state objects**. 

| Name  | Rquired | Type   | Discription                                                                                                                                                    |
| ----- | ------- | ------ | -------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| name  | ✔️       | String | This specifies the type of state to update, as of now we support `SAVE` and `OPT_IN`                                                                           |
| value | ✔️       | String | The values associated with the state to update. For name equals `SAVE` valid value is `SAVED` OR `UNSAVED`, for name equals `OPT_IN` valid value is `OPTED_IN` |

## Headers

| Name             | Rquired | Type           | Discription                                                                                                                              |
| ---------------- | ------- | -------------- | ---------------------------------------------------------------------------------------------------------------------------------------- |
| X-Correlation-Id |         | UUID           | This is used to track the request through the logs, if not provided it will be auto generated by API and returned in the header response |
| X-Origin-Client  | ✔️       | String         | This field is to let us know where the request is coming from. ie: `internal:amrp:postman`, `external:web:bmo`                           |
| Authorization    | ✔️       | Bearer {token} | Specifies a collector when making call to this api.                                                                                      |


## Response

As this is a `PUT` request there is no response body. Check for status code `204  No Content` for successful request.


# Sample

### Request

```HTTP
PUT /offers/e6528c38-6642-497d-85ca-f71cc5a74583/states HTTP/1.1
Host: cdn.airmilesapis.ca
Authorization: Bearer {JWT_TOKEN}
x-origin-client: internal:amrp:postman
Content-Type: application/json
Content-Length: 102

{
    "states": [
        {
            "name": "SAVE",
            "value": "SAVED"
        }
    ]
}
```

### Response 
#### Body

```json

```

#### Status Code

```HTTP
Status-Code: 204 No Content
```

#### Header

|                  |                                      |
| ---------------- | ------------------------------------ |
| X-Correlation-Id | a7b3b6a8-f033-4e78-804e-f1990f317515 |

For support its best to keep track of the `X-Correlation-Id` value, as that can be used to track down logs for this request.
