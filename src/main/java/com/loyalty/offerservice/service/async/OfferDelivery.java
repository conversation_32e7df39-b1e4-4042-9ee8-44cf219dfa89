package com.loyalty.offerservice.service.async;

import com.loyalty.offerservice.enums.ODSErrorCodes;
import com.loyalty.offerservice.exception.ResourceNotFoundException;
import com.loyalty.offerservice.model.dto.request.OfferRequest;
import com.loyalty.offerservice.model.dto.response.CommonMetadata;
import com.loyalty.offerservice.model.dto.response.Warning;
import com.loyalty.offerservice.model.dto.response.offerdelivery.OfferDeliveryOfferObject;
import com.loyalty.offerservice.model.dto.response.offerdelivery.OfferDeliveryResponse;
import com.loyalty.offerservice.service.offerdelivery.OfferDeliveryService;
import lombok.Getter;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

import static com.loyalty.offerservice.model.dto.response.buildresponse.BuildOfferServiceResponse.ODSWarningErrorCodes;

@Component
public class OfferDelivery {

    @Autowired
    OfferDeliveryService offerDeliveryService;

    @Getter
    CommonMetadata offerDeliveryMetadata;

    @Async
    public CompletableFuture<OfferDeliveryResponse> retrieveProcessAsync(OfferRequest request, Map<String, String> context) throws IOException, ExecutionException, InterruptedException {

        if (context != null) {
            MDC.setContextMap(context);
        }
        return CompletableFuture.completedFuture(retrieveProcess(request));
    }

    public OfferDeliveryResponse retrieveProcess(OfferRequest request) throws IOException, ExecutionException, InterruptedException {
        OfferDeliveryResponse offerDeliveryResponse;

        // Check for collector number (logged in, request)
        if (request.getCollectorNumber() == null) {
            offerDeliveryResponse = offerDeliveryService.getPublicOffers(request);
        } else {
            offerDeliveryResponse = offerDeliveryService.getCollectorOffers(request);
        }

        return offerDeliveryResponse;
    }

    @Async
    public CompletableFuture<OfferDeliveryOfferObject> retrieveProcessByIdAsync(OfferRequest request, Map<String, String> context) throws IOException, ResourceNotFoundException {
        if (context != null) {
            MDC.setContextMap(context);
        }
        return CompletableFuture.completedFuture(retrieveProcessById(request));
    }

    public OfferDeliveryOfferObject retrieveProcessById(OfferRequest request) throws IOException {
        OfferDeliveryOfferObject offerDeliveryObjectResponse;

        // Check for collector number (logged in, request)
        if (request.getCollectorNumber() == null) {
            offerDeliveryObjectResponse = offerDeliveryService.getPublicOfferByOfferId(request);
        } else {
            offerDeliveryObjectResponse = offerDeliveryService.getCollectorOfferByOfferId(request);
        }
        return offerDeliveryObjectResponse;
    }

    public Map<UUID, OfferDeliveryOfferObject> getOfferDeliveryDataMap(OfferDeliveryResponse offerDeliveryResponse){
        return offerDeliveryResponse.getData().stream().collect(
                Collectors.toMap(
                        OfferDeliveryOfferObject::getId,
                        offerObject -> offerObject,
                        (u,v) -> u ,
                        LinkedHashMap::new));
    }

    public CommonMetadata getOfferDeliveryMetadata(OfferDeliveryResponse offerDeliveryResponse){
        return offerDeliveryResponse.getMetadata();
    }
}
