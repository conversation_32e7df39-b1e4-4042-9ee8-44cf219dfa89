package com.loyalty.offerservice.service.offerresolver;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.loyalty.offerservice.model.dto.response.offerresolver.OfferDTO;
import com.loyalty.offerservice.model.dto.response.offerresolver.OfferResolverResponseDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.core.SdkBytes;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.lambda.LambdaClient;
import software.amazon.awssdk.services.lambda.model.InvokeRequest;
import software.amazon.awssdk.services.lambda.model.InvokeResponse;

import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

import static net.logstash.logback.marker.Markers.*;

@Component
@Slf4j
public class ResolverLambdaClient {

	@Value("${resolver-service.lambda-function}")
	String resolverFunctionName;
	LambdaClient awsLambda = LambdaClient.builder().region(Region.US_EAST_1).build();
	@Autowired
	ObjectMapper objectMapper;

	public OfferResolverResponseDTO getOffersByUUID(List<UUID> offersUUID, String offerRegion, String language) {
		InvokeResponse lambdaResponse = invokeFunction(offersUUID, offerRegion, resolverFunctionName, language);
		try {
			JsonNode jsonResponse = objectMapper.readTree(lambdaResponse.payload().asUtf8String());
			log.debug("Raw Response from Offer Resolver: " + jsonResponse.toPrettyString());
			OfferResolverResponseDTO response = objectMapper.readValue(jsonResponse.get("body").textValue(), OfferResolverResponseDTO.class);

			//Response logging
			if(lambdaResponse.statusCode() != 200) {
				log.warn(appendEntries(Map.of("body", lambdaResponse.payload().asUtf8String(), "status", lambdaResponse.statusCode(), "functionError", lambdaResponse.functionError())), "Response Details: Offer Resolver");
			} else {
				log.info(
						append("status", lambdaResponse.statusCode())
								.and(append("offerId", response.getOffers().stream().map(OfferDTO::getId).collect(Collectors.toList()))),
						"Response Details: Offer Resolver");

				log.debug(append("response", lambdaResponse.payload().asUtf8String()), "Response Details: Offer Resolver (Detailed)");
			}
			return response;
		} catch (JsonProcessingException e) {
			throw new RuntimeException(e);
		}
	}

	private InvokeResponse invokeFunction(List<UUID> offersUUID, String offerRegion, String functionName, String language) {
		ObjectNode bodyNode = objectMapper.createObjectNode();
		if (offerRegion != null) {
			bodyNode.put("region", offerRegion);
		}
		ArrayNode requestListId = bodyNode.putArray("listOfId");
		offersUUID.forEach(v -> requestListId.add(v.toString()));
		bodyNode.put("acceptedLanguage", language);

		ObjectNode requestRoot = objectMapper.createObjectNode();
		requestRoot.put("httpMethod", "POST");
		requestRoot.put("body", bodyNode.toString());

		String json = requestRoot.toString();
		// Need a SdkBytes instance for the payload.
		SdkBytes payload = SdkBytes.fromUtf8String(json);

		// Setup an InvokeRequest.
		InvokeRequest request = InvokeRequest.builder()
				.functionName(functionName)
				.payload(payload)
				.build();
		log.info(append("offerId", offersUUID), "Request Details: Offer Resolver");

        return awsLambda.invoke(request);
	}
}
