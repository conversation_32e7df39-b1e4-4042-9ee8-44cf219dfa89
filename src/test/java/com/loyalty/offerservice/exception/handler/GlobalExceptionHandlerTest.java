package com.loyalty.offerservice.exception.handler;

import com.loyalty.offerservice.exception.*;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.servlet.NoHandlerFoundException;

@SpringBootTest
@ExtendWith(MockitoExtension.class)
class GlobalExceptionHandlerTest {

    @Autowired
    private GlobalExceptionHandler globalExceptionHandler;

    @Test
    void handleInternalServerException_ReturnsErrorResponse() {
        InternalServerException exception = new InternalServerException(null);

        ResponseEntity<?> response = globalExceptionHandler.handleInternalServerException(exception);


        Assertions.assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        Assertions.assertNotNull(response.getBody());
    }

    @Test
    void handleIllegalArgumentException_ReturnsErrorResponse() {
        IllegalArgumentException exception = new IllegalArgumentException("illegal_argument");

        ResponseEntity<?> response = globalExceptionHandler.handleBadRequestException(exception);


        Assertions.assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        Assertions.assertNotNull(response.getBody());
    }

    @Test
    void handleBadRequestException_ReturnsErrorResponse() {
        BadRequestException exception = new BadRequestException("BAD_REQUEST", "Invalid request body");

        ResponseEntity<?> response = globalExceptionHandler.handleBadRequestException(exception);


        Assertions.assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        Assertions.assertNotNull(response.getBody());
    }

    @Test
    void handlePropagatedBadRequestException_ReturnsErrorResponse() {
        IllegalArgumentException illegalArgumentException = new IllegalArgumentException();
        BadRequestException exception = new BadRequestException("BAD_REQUEST", "Invalid request body", illegalArgumentException);

        ResponseEntity<?> response = globalExceptionHandler.handleBadRequestException(exception);


        Assertions.assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        Assertions.assertNotNull(response.getBody());
    }

    @Test
    void handleResourceNotFoundException_ReturnsErrorResponse() {
        ResourceNotFoundException exception = new ResourceNotFoundException("NOT_FOUND", "Resource not found");

        ResponseEntity<?> response = globalExceptionHandler.handleNotFoundException(exception);


        Assertions.assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
        Assertions.assertNotNull(response.getBody());
    }

    @Test
    void handleNotFoundException_ReturnsErrorResponse() {
        NoHandlerFoundException exception = new NoHandlerFoundException("GET", "test.com", null);

        ResponseEntity<?> response = globalExceptionHandler.handleNotFoundException(exception);


        Assertions.assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
        Assertions.assertNotNull(response.getBody());
    }

    @Test
    void handlePropagatedNotFoundException_ReturnsErrorResponse() {
        NullPointerException nullPointerException = new NullPointerException();
        ResourceNotFoundException exception = new ResourceNotFoundException("NOT_FOUND", "Resource not found", nullPointerException);

        ResponseEntity<?> response = globalExceptionHandler.handleNotFoundException(exception);


        Assertions.assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
        Assertions.assertNotNull(response.getBody());
    }

    @Test
    void handleMethodNotAllowedException_ReturnsErrorResponse() {
        HttpRequestMethodNotSupportedException exception = new HttpRequestMethodNotSupportedException("method not allowed");

        ResponseEntity<?> response = globalExceptionHandler.handleMethodNotSupportedException(exception);


        Assertions.assertEquals(HttpStatus.METHOD_NOT_ALLOWED, response.getStatusCode());
        Assertions.assertNotNull(response.getBody());
    }

    @Test
    void handleUnsupportedMediaTypeException_ReturnsErrorResponse() {
        HttpMediaTypeNotSupportedException exception = new HttpMediaTypeNotSupportedException("unsupported media type");

        ResponseEntity<?> response = globalExceptionHandler.handleMediaTypeNotSupportedException(exception);


        Assertions.assertEquals(HttpStatus.UNSUPPORTED_MEDIA_TYPE, response.getStatusCode());
        Assertions.assertNotNull(response.getBody());
    }

    @Test
    void handleServiceRuntimeException_ReturnsErrorResponse() {
        ServiceRuntimeException exception = new ServiceRuntimeException("RUNTIME", "Runtime exception");

        ResponseEntity<?> response = globalExceptionHandler.handleServiceRuntimeException(exception);


        Assertions.assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        Assertions.assertNotNull(response.getBody());
    }

    @Test
    void handlePropagatedServiceRuntimeException_ReturnsErrorResponse() {
        Exception genericException = new Exception();
        ServiceRuntimeException exception = new ServiceRuntimeException("RUNTIME", "Runtime exception", genericException);

        ResponseEntity<?> response = globalExceptionHandler.handleServiceRuntimeException(exception);


        Assertions.assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        Assertions.assertNotNull(response.getBody());
    }

    @Test
    void handleOffersNotFoundException_ReturnsErrorResponse() {
        OffersNotFoundException exception = new OffersNotFoundException("RUNTIME", "Runtime exception");

        ResponseEntity<?> response = globalExceptionHandler.handleInternalServerException(exception);


        Assertions.assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        Assertions.assertNotNull(response.getBody());
    }

}
