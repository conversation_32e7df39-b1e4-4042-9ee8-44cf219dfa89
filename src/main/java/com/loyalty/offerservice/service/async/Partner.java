package com.loyalty.offerservice.service.async;

import com.loyalty.offerservice.model.dto.request.OfferRequest;
import com.loyalty.offerservice.model.dto.response.offerdelivery.OfferDeliveryOfferObject;
import com.loyalty.offerservice.model.dto.response.partner.PartnerObject;
import com.loyalty.offerservice.model.dto.response.partner.PartnerResponse;
import com.loyalty.offerservice.service.partner.PartnerService;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Component
public class Partner {

    @Autowired
    PartnerService partnerService;

    @Async
    public CompletableFuture<Map<UUID, PartnerObject>> retrieveProcessAsync(List<UUID> partnerId, OfferRequest offerRequest, Map<String, String> context) throws IOException {

        if (context != null) {
            MDC.setContextMap(context);
        }

        return CompletableFuture.completedFuture(retrieveProcess(partnerId,offerRequest));
    }


    public Map<UUID, PartnerObject> retrieveProcess(List<UUID> partnerId, OfferRequest offerRequest) throws IOException {
        PartnerResponse partnerResponse = partnerService.getPartner(partnerId, offerRequest);

        //Convert the partnerResponse (list) to a Map (ID,Object)

        return partnerResponse.getResults().stream()
                .collect(Collectors.toMap(PartnerObject::getId, partner -> partner));
    }
}
