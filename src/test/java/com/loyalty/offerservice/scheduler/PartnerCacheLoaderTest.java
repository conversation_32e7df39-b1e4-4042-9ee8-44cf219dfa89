package com.loyalty.offerservice.scheduler;

import com.loyalty.offerservice.service.partner.PartnerService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.core.env.Environment;

import java.io.IOException;

import static org.mockito.Mockito.when;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.times;

public class PartnerCacheLoaderTest {

    @Mock
    private PartnerService partnerService;

    @Mock
    private Environment environment;

    @InjectMocks
    private PartnerCacheLoader partnerCacheLoader;

    @BeforeEach
    void setUp() {
    }

    @Test
    void onStartup() {
    }

    @Test
    void testOnStartup() throws IOException {

        MockitoAnnotations.openMocks(this);


        when(environment.getProperty("cache.interval")).thenReturn("0 0 0 * * *");


        partnerCacheLoader.onStartup();


        verify(partnerService, times(2)).loadPartners(any());
    }


    @Test
    void testLoadPartners() throws IOException {

        MockitoAnnotations.openMocks(this);


        when(environment.getProperty("cache.interval")).thenReturn("0 0 0 * * *");


        partnerCacheLoader.loadPartners();


        verify(partnerService, times(2)).loadPartners(any());
    }

    @Test
    void testLoadData() throws IOException {

        MockitoAnnotations.openMocks(this);


        partnerCacheLoader.loadData();


        verify(partnerService, times(2)).loadPartners(any());
    }
}