package com.loyalty.offerservice.service.offerdelivery;

import com.loyalty.offerservice.model.dto.request.OfferRequest;
import com.loyalty.offerservice.model.dto.response.offerdelivery.OfferDeliveryOfferObject;
import com.loyalty.offerservice.model.dto.response.offerdelivery.OfferDeliveryResponse;

public interface OfferDelivery {
    OfferDeliveryResponse getPublicOffers(OfferRequest request);
    OfferDeliveryResponse getCollectorOffers(OfferRequest request);
    OfferDeliveryOfferObject getPublicOfferByOfferId(OfferRequest offerRequest);
    OfferDeliveryOfferObject getCollectorOfferByOfferId(OfferRequest offerRequest);
}
