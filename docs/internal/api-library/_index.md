---
title: API Library
type: docs
weight: 20
no_list: true
---

This section provides information on the 3 API's provided by the Offer Service.

{{< children description="true" />}}


## Common Use Case

- _Get Offers By Province_
    | Request             | Response                      |
    | ------------------- | ----------------------------- |
    | `/offers?region=ON` | Get all offers in **Ontario** |
    ---


- _Get Offers By Partner_
    | Request             | Response                      |
    | ------------------- | ----------------------------- |
    | `/offers?region=ON&partner_id=ID_1,ID_2` | Get all offers related to partner with ID_1 **OR** ID_2, in Ontario |

    > ID_1 & ID_2 are UUID's for partners. To get partner id for a specific partner check `Mapping -> Partner` documentation
    ---

## Filter Combination

When multiple request parameters are passed, and/or multiple values are passed, the offers that satisfy the following logic are returned in the response.

```
(param1.valueA OR param1.valueB OR ...) AND (param2.valueX OR param2.valueY OR ...) AND (...) 
``` 

For example, if request:
- `partner_id` = A, B, C
- `category_id` = X

`/offers?region=ON&partner_id=A,B,C&category_id=X`

Then the `/offers` response will return offers that satisfy: 
Offers where (partner=A OR partner=B OR partner=C) AND (category=X). 