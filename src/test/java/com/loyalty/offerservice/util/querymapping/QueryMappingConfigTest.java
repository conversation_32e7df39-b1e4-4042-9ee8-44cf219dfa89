package com.loyalty.offerservice.util.querymapping;

import com.loyalty.offerservice.util.querymapping.QueryMappingConfig;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;

import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

public class QueryMappingConfigTest {

    private MockedStatic<QueryMappingConfig> queryMappingConfig;

    @BeforeEach
    public void setUp() {
        queryMappingConfig = mockStatic(QueryMappingConfig.class);
    }

    @AfterEach
    public void tearDown() {
        queryMappingConfig.close();
    }

    @Test
    public void testGetMappingLocationHappyPath() {
        when(QueryMappingConfig.getMappingLocation(any())).thenReturn("classpath:internal:test.json");

        String mappingLocation = QueryMappingConfig.getMappingLocation("internal:amrp:test");

        assertNotNull(mappingLocation);
        assertEquals(mappingLocation, "classpath:internal:test.json");
    }

    @Test
    public void testGetMappingLocationNull() {
        when(QueryMappingConfig.getMappingLocation(any())).thenReturn(null);

        String mappingLocation = QueryMappingConfig.getMappingLocation("internal:amrp:test");

        assertNull(mappingLocation);
    }
}