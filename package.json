{"name": "offer-service", "description": "", "version": "1.20.0", "scripts": {"release": "semantic-release"}, "release": {"tagFormat": "${version}", "branches": ["main"], "plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", ["@semantic-release/npm", {"npmPublish": false}], ["@semantic-release/git", {"assets": ["package.json"], "message": "chore(release): ${nextRelease.version} [skip ci]\n\n${nextRelease.notes}"}]]}, "dependencies": {"@semantic-release/git": "^10.0.0", "semantic-release": "^20.1.1"}, "devDependencies": {"standard": "^14.3.4"}}