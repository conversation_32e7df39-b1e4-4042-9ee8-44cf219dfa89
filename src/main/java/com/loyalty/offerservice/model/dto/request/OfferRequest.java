package com.loyalty.offerservice.model.dto.request;

import lombok.*;

import java.time.LocalDateTime;
import java.util.UUID;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
public class OfferRequest {

    private FilterRequest filters;
    private PaginationRequest pagination;
    private Boolean extendedMetadata;
    private LocalDateTime localTime;
    private UUID offerId;
    private Long collectorNumber;
    private String originClient;
    private UUID correlationId;
    private String language;
    private String origin;

    public OfferRequest(OfferRequest source) {
        this.filters = source.filters;
        this.pagination = source.pagination;
        this.extendedMetadata = source.extendedMetadata;
        this.localTime = source.localTime;
        this.offerId = source.offerId;
        this.collectorNumber = source.collectorNumber;
        this.originClient = source.originClient;
        this.correlationId = source.correlationId;
        this.language = source.language;
        this.origin = source.origin;
    }
}

