package com.loyalty.offerservice.service;

import com.loyalty.offerservice.model.dto.request.OfferRequest;
import com.loyalty.offerservice.model.dto.response.OfferServiceResponse;
import com.loyalty.offerservice.model.dto.response.offerdelivery.OfferDeliveryOfferObject;
import com.loyalty.offerservice.model.dto.response.offerresolver.OfferDTO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
public class OfferServiceImplTest {

    @Autowired
    OfferServiceImpl offerService;

    @Test
    void testResponseHeaders_givenAuthenticatedRequest() throws Exception{
        UUID correlationID = UUID.randomUUID();
        OfferRequest offerRequest = OfferRequest.builder().collectorNumber(1L).correlationId(correlationID).origin("http://127.0.0.1:4000").build();

        HttpHeaders httpHeaders = offerService.addResponseHeaders(offerRequest);

        List<String> cache = httpHeaders.get("Cache-Control");
        assertTrue(cache.contains("no-cache"));

        //List<String> origin = httpHeaders.get(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN);
        //assertTrue(origin.contains("http://127.0.0.1:4000"));
    }

    @Test
    void testResponseHeaders_givenUnAuthenticatedRequest_andNoOrigin() throws Exception{
        UUID correlationID = UUID.randomUUID();
        OfferRequest offerRequest = OfferRequest.builder().correlationId(correlationID).build();

        HttpHeaders httpHeaders = offerService.addResponseHeaders(offerRequest);

        List<String> cache = httpHeaders.get("Cache-Control");
        String cacheValue = cache.get(0);
        assertTrue(cacheValue.contains("public, max-age="));
    }

    @Test
    void testReplaceOfferDatesODS_withOfferServiceResponse_thenReturnLegalTextReplaced() {
        LocalDateTime time = LocalDateTime.now();

        OfferDeliveryOfferObject offerDeliveryOfferObject = OfferDeliveryOfferObject.builder()
                .eventBased(true)
                .startDate(time)
                .endDate(time)
                .displayDate(time)
                .build();

        OfferServiceResponse offerServiceResponse = OfferServiceResponse.builder()
                .legalText(OfferServiceImpl.COLLECTOR_START_DATE_PLACEHOLDER + ", " + OfferServiceImpl.COLLECTOR_END_DATE_PLACEHOLDER)
                .build();

        OfferRequest offerRequest = OfferRequest.builder()
                .language("fr-CA")
                .build();

        OfferServiceImpl.replaceOfferDatesODS(offerDeliveryOfferObject, offerServiceResponse, offerRequest);

        assertFalse(offerServiceResponse.getLegalText().contains(OfferServiceImpl.COLLECTOR_START_DATE_PLACEHOLDER));
        assertFalse(offerServiceResponse.getLegalText().contains(OfferServiceImpl.COLLECTOR_END_DATE_PLACEHOLDER));
        assertEquals(offerServiceResponse.getStartDate(), time);
        assertEquals(offerServiceResponse.getEndDate(), time);
        assertEquals(offerServiceResponse.getDisplayDate(), time);
    }

    @Test
    void testReplaceOfferDatesODS_withOfferDTO_thenReturnLegalTextReplaced() {
        LocalDateTime time = LocalDateTime.now();

        OfferDeliveryOfferObject offerDeliveryOfferObject = OfferDeliveryOfferObject.builder()
                .eventBased(true)
                .startDate(time)
                .endDate(time)
                .displayDate(time)
                .build();

        OfferDTO offerDTO = new OfferDTO();
        offerDTO.setLegalText(OfferServiceImpl.COLLECTOR_START_DATE_PLACEHOLDER + ", " + OfferServiceImpl.COLLECTOR_END_DATE_PLACEHOLDER);

        OfferRequest offerRequest = OfferRequest.builder()
                .language("fr-CA")
                .build();

        OfferServiceImpl.replaceOfferDatesODS(offerDeliveryOfferObject, offerDTO, offerRequest);

        assertFalse(offerDTO.getLegalText().contains(OfferServiceImpl.COLLECTOR_START_DATE_PLACEHOLDER));
        assertFalse(offerDTO.getLegalText().contains(OfferServiceImpl.COLLECTOR_END_DATE_PLACEHOLDER));
        assertEquals(offerDTO.getStartDate(), time);
        assertEquals(offerDTO.getEndDate(), time);
        assertEquals(offerDTO.getDisplayDate(), time);
    }
}
