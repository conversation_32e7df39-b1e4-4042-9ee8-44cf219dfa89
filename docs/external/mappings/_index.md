---
title: Mapping
linkTitle: Mapping
description: Refrence <PERSON> with partner, category and promotions.
type: docs
no_list: true
weight: 19
---

This document contains the mappings of UUID's to Partners, Categories, SubCategories, Promotions.
We can discuss later how Offer Service `/offers` API can be used to get some of these mappings aswell.

## Region

| Code | Name                      |
| ---- | ------------------------- |
| BC   | British Columbia          |
| AB   | Alberta                   |
| SK   | Saskatchewan              |
| MB   | Manitoba                  |
| ON   | Ontario                   |
| QC   | Quebec                    |
| TB   | Thunder Bay               |
| NB   | New Brunswick             |
| NS   | Nova Scotia               |
| PE   | Prince Edward Island      |
| NL   | Newfoundland and Labrador |
| NT   | Northwest Territories     |
| NU   | Nunavut                   |
| YT   | Yukon                     |
| ZZ   | for automated tests       |

## Promotion


| Id                                   | Name                    |
| ------------------------------------ | ----------------------- |
| 784bbe95-5299-475b-b03b-5fb274e206c6 | Our Top Picks           |
| 6ba6a652-b4d0-47bb-a431-146389cedb3b | New Partner             |
| 7058d38e-b8db-4fd4-b66b-9ab8cb73544f | THE MAGIC OF ONYX       |
| 8ccb04b6-e2fd-4eb6-8ca1-dc28652cca38 | Flash Offers            |
| 93ae1d3b-f9b0-44cd-92d3-f5bfa617ad59 | MORE WEEKEND MORE MILES |
| 1930dd14-d3a7-418f-847c-cc4e56c38387 | GOLDEN DAYS OF SUMMER   |
| 285b4858-3165-4041-a937-b01ee2e3e4ec | Unwrap the App          |
| 27924c2-0942-4483-ab42-3f0b1772c840  | TAKEOFF TO TASTES       |
| 905350da-27a4-11ec-9621-0242ac130002 | BONUS BOOM Boost        |
| 1f0d2275-b289-4f7f-b72d-da76650ba1cf | Offer Hub               |
| 26dd100d-9cb8-43cd-965e-b7b284ae8a92 | BAG-A-BONUS             |
| 924ad367-f930-439c-93eb-823952d4228d | Shop the Block          |
| 4db99143-1768-4f85-a44a-d6fa16011f7b | Mega Miles              |

## Partner

| Id                                   | Name                          |
| ------------------------------------ | ----------------------------- |
| 5058f22a-1f5c-4625-b8ff-ffe116756722 | TIMBER MART                   |
| 33d0cf88-5eae-4186-be03-05092061eed9 | HP                            |
| 63e8543d-fbf7-458e-8712-d25332da3614 | AIR MILES Travel              |
| 22a2cdfd-ff82-45f6-bc94-c14a3a533922 | BMO Bank of Montreal          |
| e914624c-9676-4287-8ce9-04de68dd0507 | airmilesshops.ca              |
| 87ab4dd0-65b5-41a3-acd2-b97540dc1aff | United Van Lines              |
| ef73e58a-db63-4b27-baae-bd1e1d2ae86c | Peter & Paul's Gifts          |
| 0c988dcd-b2a9-4391-b2c4-48f0294d5658 | Canadian Appliance Source     |
| fbdc481f-3fa4-40e9-b1ca-d4dab4c36c30 | All Purpose Realty            |
| 11892e84-a87e-48b0-bf13-fcd0be389588 | Additional AIR MILES Partners |
| c13f2e1b-f828-4781-9369-fde0581c400d | Onlia Insurance               |
| 19e1bb2b-88d4-4b37-be72-262a694ae58d | National Car Rental           |
| 4a755252-876b-478e-9440-42961525e307 | Metro                         |
| 1563e943-25fd-480b-b77d-94aedc7202af | LG Electronics                |
| cbfcd38f-b1c0-4443-881c-c342833649a0 | Fresh                         |
| 91adc45f-0037-4fa8-9424-2e6e8f999467 | SMASH + TESS                  |
| b90e694d-3510-4cc2-84be-f9b6f61bd31b | Shell Mobility                |
| 27568f1b-c48d-4c27-8d9b-7c67666308c2 | Dollarama                     |
| 98d43a31-1c04-4db7-bfd3-b9a4a0ced034 | Mayflower                     |
| 9ab6f70f-588e-4460-b14c-ad4ad8c9a524 | La Vie en Rose                |
| 1b36212c-87a6-448c-82a7-c2f28c8b6d52 | HelloFresh                    |
| 5a92c696-cddd-4255-920e-1f74b121e7ea | Kernels                       |
| 40786f15-10e1-490f-a4bf-38003770ad73 | redtag.ca                     |
| 38178928-9281-42aa-9f70-8c39c453baf1 | Alamo Rent a Car              |

> There are many partners not listed in this list.

## Mapping from Offer Service

We can make a call to the Offer Service API to get these `extended_metedata` values.

**Request**

```
/offers?region=ON&extended_metadata=true
```

**Response**

```json
{
    "offers":[],
    "metadata":{
        "total": N,
        "partners": [...],
        "categories": [...],
    }
}

```

The field `partners` in the response has id and labels. _Region Dependent_

The field `categories` in the response contains id and labels for categories and subcategories. _Region agnostic_
