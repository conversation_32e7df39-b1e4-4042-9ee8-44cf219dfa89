package com.loyalty.offerservice.controller;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/health")
public class HealthController {

    @Value("${spring.application.name}")
    private String appName;

    @GetMapping
    public Map<String, String> healthStatusRequest() {
        // initialize response
        HashMap<String, String> response = new HashMap<>();
        // set response based on application-{env} properties
        response.put("applicationName", appName);
        response.put("status", "Success");
        return response;
    }
}
