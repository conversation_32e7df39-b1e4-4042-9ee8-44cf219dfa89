package com.loyalty.offerservice.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.loyalty.offerservice.exception.InternalServerException;
import com.loyalty.offerservice.exception.ResourceNotFoundException;
import com.loyalty.offerservice.exception.BadRequestException;
import com.loyalty.offerservice.model.dto.request.ODSError;
import feign.Response;
import feign.codec.ErrorDecoder;

import java.io.IOException;
import java.io.InputStream;


public class FeingODSCustomErrorHandler implements ErrorDecoder{

    @Override
    public Exception decode(String methodKey, Response response) {
        ODSError error = null;
        try (InputStream bodyIs = response.body().asInputStream()) {
            ObjectMapper mapper = new ObjectMapper();
            error = mapper.readValue(bodyIs, ODSError.class);
        } catch (IOException e) {
            return new Exception(e.getMessage());
        }
        switch (response.status()){
            case 404:
                return new ResourceNotFoundException(error.getErrorCode(), error.getMessage());
            case 400:
                return new BadRequestException(error.getErrorCode(), error.getMessage());
            default:
                return new InternalServerException(String.format("Error response when calling downstream service",
                        error.getErrorCode(), error.getMessage()), null);
        }
    }
}
