package com.loyalty.offerservice.exception.handler;

import com.loyalty.offerservice.exception.*;
import com.loyalty.offerservice.exception.model.AbstractErrorResponse;
import com.loyalty.offerservice.exception.model.GenericErrorResponse;
import feign.FeignException;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.ServletRequestBindingException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.servlet.NoHandlerFoundException;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletionException;

import static net.logstash.logback.marker.Markers.appendEntries;

@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

	@Autowired
	GenericErrorResponse errorResponse;

	@ExceptionHandler({
			EmptyHeaderException.class,
			IllegalArgumentException.class,
			MethodArgumentNotValidException.class,
			MethodArgumentTypeMismatchException.class,
			MissingServletRequestParameterException.class,
			ConstraintViolationException.class,
			ServletRequestBindingException.class,
			BadRequestException.class,
			FeignException.BadRequest.class
	})
	public ResponseEntity<?> handleBadRequestException(Exception ex) {
		Map<String, Object> data = new HashMap<String, Object>();
		data.put("status", HttpStatus.BAD_REQUEST.value());
		if (ex instanceof BadRequestException badRequestException) {
			String errorCode = badRequestException.getErrorCode();
			String message = badRequestException.getMessage();

			data.put("errorCode", errorCode);
			data.put("message", message);
			log.error(appendEntries(data), ex.getMessage(), ex);
            return errorResponse.badRequest(errorCode, message);
		}

		data.put("message", ex.getMessage());
		log.error(appendEntries(data), "Handle Bad Request Error", ex);
		return errorResponse.badRequest(ex.getMessage());
	}

	@ExceptionHandler({
			InternalServerException.class,
			NullPointerException.class,
			OffersNotFoundException.class,
			CompletionException.class,
			Exception.class,
	})
	public ResponseEntity<?> handleInternalServerException(Exception ex) {
		Map<String, Object> data = new HashMap<String, Object>();
		data.put("status", HttpStatus.INTERNAL_SERVER_ERROR.value());

		if (ex instanceof InternalServerException || ex instanceof OffersNotFoundException) {
			String errorCode = ((AppRuntimeException) ex).getErrorCode();
			String message = ex.getMessage();

			data.put("errorCode", errorCode);
			data.put("message", message);
			log.error(appendEntries(data), ex.getMessage(), ex);
			return errorResponse.build(errorCode, message, HttpStatus.INTERNAL_SERVER_ERROR);
		}
		data.put("message", ex.getMessage());
		log.error(appendEntries(data), "Handle Internal Server Error", ex);
		return errorResponse.internalServer();
	}

	@ExceptionHandler({
			NoHandlerFoundException.class,
			ResourceNotFoundException.class,
			FeignException.NotFound.class
	})
	public ResponseEntity<?> handleNotFoundException(Exception ex) {
		Map<String, Object> data = new HashMap<String, Object>();
		data.put("status", HttpStatus.NOT_FOUND.value());
		if (ex instanceof ResourceNotFoundException resourceNotFoundException) {
			String errorCode = resourceNotFoundException.getErrorCode();
			String message = resourceNotFoundException.getMessage();

			data.put("errorCode", errorCode);
			data.put("message", message);
			log.error(appendEntries(data), ex.getMessage(), ex);
			return errorResponse.build(errorCode, message, HttpStatus.NOT_FOUND);
		}
		data.put("message", ex.getMessage());
		log.error(appendEntries(data), "Handle Resource Not Found Error", ex);
		return errorResponse.notFound(ex.getMessage());
	}

	@ExceptionHandler({
			FeignException.Unauthorized.class
	})
	public ResponseEntity<?> handleUnauthorized(Exception ex) {
		Map<String, Object> data = new HashMap<String, Object>();
		data.put("status", HttpStatus.NOT_FOUND.value());
		data.put("message", ex.getMessage());
		log.error(
				appendEntries(data),
				"Handle Unauthorized Request Exception", ex);
		return errorResponse.unauthorized(ex.getMessage());
	}

	@ExceptionHandler({
			FeignException.Forbidden.class
	})
	public ResponseEntity<?> handleForbidden(Exception ex) {
		Map<String, Object> data = new HashMap<String, Object>();
		data.put("status", HttpStatus.FORBIDDEN.value());
		data.put("message", ex.getMessage());
		log.error(
				appendEntries(data),
				"Handle Forbidden Request Exception", ex);
		return errorResponse.forbidden(ex.getMessage());
	}

	@ExceptionHandler({
			HttpRequestMethodNotSupportedException.class,
	})
	public ResponseEntity<?> handleMethodNotSupportedException(Exception ex) {
		log.error("Handle Method not allowed Error", ex);
		return errorResponse.methodNotAllowed(ex.getMessage());
	}

	@ExceptionHandler({ HttpMediaTypeNotSupportedException.class })
	public ResponseEntity<?> handleMediaTypeNotSupportedException(Exception ex) {
		log.error("Handle MediaType not supported Error", ex);
		return errorResponse.unsupportedMediaType(ex.getMessage());
	}

	@ExceptionHandler({ ServiceRuntimeException.class })
	public ResponseEntity<?> handleServiceRuntimeException(ServiceRuntimeException ex) {
		log.error("Service Class exception", ex);
		return errorResponse.build(ex.getErrorCode(), ex.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	}

}
