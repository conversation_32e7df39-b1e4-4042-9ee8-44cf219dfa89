package com.loyalty.offerservice.exception.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import static com.loyalty.offerservice.exception.utils.ExceptionConstantsUtils.*;

@Data
@Component
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class GenericErrorResponse extends AbstractErrorResponse {

    private String code;
    private String message;

    public ResponseEntity<?> internalServer() {
        return build(INTERNAL_SERVER_ERROR, INTERNAL_SERVER_ERROR_MESSAGE, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    public ResponseEntity<?> badRequest(String msg) {
        return build(BAD_REQUEST, msg, HttpStatus.BAD_REQUEST);
    }

    public ResponseEntity<?> badRequest(String errorCode, String msg) {
        return build(errorCode, msg, HttpStatus.BAD_REQUEST);
    }

    public ResponseEntity<?> unauthorized(String msg) {
        return build(UNAUTHORIZED, msg, HttpStatus.UNAUTHORIZED);
    }

    public ResponseEntity<?> forbidden(String msg) {
        return build(FORBIDDEN, msg, HttpStatus.FORBIDDEN);
    }

    public ResponseEntity<?> notFound(String msg) {
        return build(NOT_FOUND, msg, HttpStatus.NOT_FOUND);
    }

    public ResponseEntity<?> methodNotAllowed(String msg) {
        return build(METHOD_NOT_ALLOWED, msg, HttpStatus.METHOD_NOT_ALLOWED);
    }

    public ResponseEntity<?> unsupportedMediaType(String msg) {
        return build(UNSUPPORTED_MEDIA_TYPE, msg, HttpStatus.UNSUPPORTED_MEDIA_TYPE);
    }
}
