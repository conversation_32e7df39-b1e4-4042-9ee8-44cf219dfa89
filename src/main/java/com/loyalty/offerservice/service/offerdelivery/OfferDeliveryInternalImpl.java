package com.loyalty.offerservice.service.offerdelivery;

import com.loyalty.offerservice.model.dto.request.OfferRequest;
import com.loyalty.offerservice.model.dto.response.offerdelivery.OfferDeliveryOfferObject;
import com.loyalty.offerservice.model.dto.response.offerdelivery.OfferDeliveryResponse;
import com.loyalty.offerservice.util.offerdelivery.HeaderUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component("offerDeliveryInternalClientImpl")
public class OfferDeliveryInternalImpl extends HeaderUtil implements OfferDelivery {
    @Autowired
    private OfferDeliveryInternalClient offerDeliveryInternalClient;

    @Override
    public OfferDeliveryResponse getPublicOffers(OfferRequest request) {
        return offerDeliveryInternalClient.getPublicOffers(generateQueryParams(request), generateHeaders(request));
    }

    @Override
    public OfferDeliveryResponse getCollectorOffers(OfferRequest request) {
        return offerDeliveryInternalClient.getCollectorOffers(generateQueryParams(request), generateHeaders(request));
    }

    @Override
    public OfferDeliveryOfferObject getPublicOfferByOfferId(OfferRequest request) {
        return offerDeliveryInternalClient.getPublicOfferByOfferId(
                request.getOfferId(),
                request.getFilters().getRegion(),
                generateHeaders(request));
    }

    @Override
    public OfferDeliveryOfferObject getCollectorOfferByOfferId(OfferRequest request) {
        return offerDeliveryInternalClient.getCollectorOfferByOfferId(
                request.getOfferId(),
                request.getFilters().getRegion(),
                generateHeaders(request));
    }
}
