package com.loyalty.offerservice.util.querymapping;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.Map;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class QueryMappingConfig {

    private static final String extBMO = "ext_bmo.json";

    private static final Map<String, String> QUERY_MAPPINGS = Map.of(
    "external:bmo:mobile", extBMO,
    "external:bmo:web", extBMO,
    "external:web:bmo",extBMO,
    "external:mobile:bmo",extBMO
    );

    public static String getMappingLocation(String xOriginClient) {
        String mappingFile = QUERY_MAPPINGS.get(xOriginClient);

        return mappingFile != null ? "classpath:/query_mappings/" + mappingFile : null;
    }

    public static Boolean clientHasMapping(String xOriginClient) {
        return QUERY_MAPPINGS.containsKey(xOriginClient);
    }
}