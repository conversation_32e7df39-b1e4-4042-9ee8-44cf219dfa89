function fn() {

  var env = karate.env; // get system property 'karate.env'
    karate.log('************ *******  karate.env system property was:', env);
    if (!env) {
   env = 'local';
 }
  if (env == 'local') {
    var config = {
      environment:'local',
      apiUrl: 'http://localhost:8080/offer-service/offers',
      //apiUrl: 'https://dev.airmilesapis.ca/',
      id:'133b7d2c-9223-446e-a594-2c654b23fb50'
    }
  }
  if (env == 'dev') {
      var config = {
            environment:'dev',
            apiUrl:'https://offers.dev.api.loyalty.com:34075/offer-service/offers',
            adminUrl:'https://offers.dev.api.loyalty.com:34075/offer-service/admin/offers',
            token_url: 'https://oauth-dev.airmiles.ca/oauth/token',
            massoffer_id:'d27d183f-fe31-482c-9e94-8e6c0261171f',
            invalid_id:'75dd1c8a-5de3-4b39-9adf-d9e48b0ec3d',
            future_id:'a244b9c7-34fe-40ff-949e-ed19f4beed58',
            expired_id:'92cbbd37-298e-4eb2-ba72-d5c5442dbced',
            outofregion_id:'76f77bb4-46a3-4bfb-b373-beace524480d',
            targeted_id:'3688cfb3-6627-420c-aff6-b2930a7acc90',
            auth_targeted_id:'37fa4fb6-b20d-4758-ae02-cd23a19bcaa2',
            auth_future_id:'f608bcb9-821f-4947-8c65-dafe9d788af5',
            auth_notassigned_id:'3688cfb3-6627-420c-aff6-b2930a7acc90',
            auth_outofregion_id:'a78b5ee4-f968-41fe-a641-83f0df9cc75c',
            states_id:'1257581d-45cb-48b5-b4e7-bdfeb53cc5f6/states',
            eventBasedOffer_id:'a84d4f7a-e0f7-4b3c-a42a-353476f0274d'
          }
          }
            if (env == 'int') {
            //customize it
            }
            if  (env == 'uat') {
              //customize it
            }


  return config;

  karate.configure('report', { showLog: true, showAllSteps: true, enableCucumberJson: true, format: 'html' });
}