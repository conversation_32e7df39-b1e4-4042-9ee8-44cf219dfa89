{"timestamp": "2023-08-22T14:11:36.700+0000", "httpStatus": "BAD_REQUEST", "message": "Request contains non-parsable message", "cause": "Cannot deserialize value of type `java.util.UUID` from String \"142b7075-4ab8-4f82-b0f3-5351154f27f57\": UUID has to be represented by standard 36-char representation\n at [Source: UNKNOWN; line: -1, column: -1] (through reference chain: com.loyalty.nova.offerresolver.service.v1.model.OfferResolverRequestDTO[\"listOfId\"]->java.util.ArrayList[0])"}