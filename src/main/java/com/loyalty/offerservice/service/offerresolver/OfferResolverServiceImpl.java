package com.loyalty.offerservice.service.offerresolver;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.loyalty.offerservice.cache.CacheService;
import com.loyalty.offerservice.exception.OffersNotFoundException;
import com.loyalty.offerservice.model.dto.cache.OfferServiceCacheResponse;
import com.loyalty.offerservice.model.dto.request.OfferRequest;
import com.loyalty.offerservice.model.dto.response.offerresolver.OfferDTO;
import com.loyalty.offerservice.model.dto.response.offerresolver.OfferResolverResponseDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.client.circuitbreaker.CircuitBreaker;
import org.springframework.cloud.client.circuitbreaker.CircuitBreakerFactory;
import org.springframework.core.env.Environment;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.io.InputStreamReader;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static net.logstash.logback.marker.Markers.*;

@Service
@Slf4j
public class OfferResolverServiceImpl implements OfferResolverService {

    @Autowired
    ObjectMapper objectMapper;

    @Autowired
    ResourceLoader resourceLoader;

    @Autowired
    ResolverLambdaClient resolverClient;

    @Autowired
    private Environment environment;

    @Autowired
    private CacheService cacheService;

    private static final String cacheName = "offerResolver";
    private static final String cacheExpireValue = "cache-expire.offer-resolver.value";

    @Override
    public OfferResolverResponseDTO getOffer(List<UUID> offerIds, OfferRequest offerRequest) throws IOException {
        OfferResolverResponseDTO resolverResponse = new OfferResolverResponseDTO();
        if (Arrays.asList(environment.getActiveProfiles()).contains("local")
                || Arrays.asList(environment.getActiveProfiles()).contains("test")) {
            Resource resource = resourceLoader.getResource("classpath:offerResolverByID.json");
            if(offerIds.size() > 1){
                resource = resourceLoader.getResource("classpath:offerResolver.json");
            }
            resolverResponse = objectMapper.readValue(new InputStreamReader(resource.getInputStream()),
                    OfferResolverResponseDTO.class);
        } else {
            OfferServiceCacheResponse<OfferDTO, UUID> cacheResponse = cacheService.fetchFromCache(offerIds, offerRequest.getLanguage(), cacheName, OfferDTO.class);
            if (null != cacheResponse.getCachedObjects()) {
                resolverResponse.setOffersRequested(cacheResponse.getCachedObjects().size());
                resolverResponse.setOffers(cacheResponse.getCachedObjects());
                resolverResponse.setOffersNotFound(0);
            }

            if (null != cacheResponse.getMissingKeys() && !cacheResponse.getMissingKeys().isEmpty()) {
                try {
                    OfferResolverResponseDTO resolverResponseFromService = null;
                    try {
                        resolverResponseFromService = invokeClient(offerIds, offerRequest);
                    } catch (Exception e) {
                        offerResolverFallback(offerIds, offerRequest, e);
                    }

                    if (resolverResponseFromService != null) {
                        resolverResponse.getOffers().addAll(resolverResponseFromService.getOffers());
                        resolverResponse.setOffersNotFound(resolverResponseFromService.getOffersNotFound());
                        resolverResponse.setOffersRequested(resolverResponse.getOffersRequested() + resolverResponseFromService.getOffersRequested());

                        updateCache(resolverResponseFromService.getOffers());
                    }

                } catch (OffersNotFoundException e) {
                    if (CollectionUtils.isEmpty(resolverResponse.getOffers())) {
                        //fallback method was invoked and no offers were found in the cache
                        throw e;
                    }
                }
            }
        }
        return resolverResponse;
    }

    private void updateCache(List<OfferDTO> offerList) {
        long loadExpireValue = environment.getProperty(cacheExpireValue, Long.class);
        for (OfferDTO offer : offerList) {
            cacheService.updateCache(loadExpireValue, offer.getId().toString(), offer, cacheName);
        }
    }

    public OfferResolverResponseDTO invokeClient(List<UUID> offerIds, OfferRequest offerRequest){

        String regionString = offerRequest.getFilters().getRegion() != null ? offerRequest.getFilters().getRegion().toString() : null;

        return resolverClient.getOffersByUUID(
                    offerIds, regionString,
                    offerRequest.getLanguage());
    }

    private OfferResolverResponseDTO offerResolverFallback(List<UUID> offerIds, OfferRequest offerRequest, Throwable throwable) {
        var logMapData = Map.of("offerIds", offerIds.toString(), "offerRequest", offerRequest.toString());
        log.error(appendEntries(logMapData), "Unable to invoke Offer Resolver for request and offerIds on offerResolverFallback", throwable);
        throw new OffersNotFoundException("NO_OFFERS_FOUND", "No offers found, try again later");
    }
}