package com.loyalty.offerservice.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;
import java.util.Map;

@Getter
@AllArgsConstructor
@Deprecated
public enum StatesEnum {

    UNSAVE("UNSAVED"),
    SAVE("SAVED"),
    OPT_IN("OPTED_IN");

    private final String description;


    public String value() {
        return description;
    }

    public static StatesEnum getInstance(String value) {
        if (value == null || value.isEmpty() || value.trim().isEmpty())
            return null;

        StatesEnum validState = lookupByValue(value);

        if (validState == null) {
            throw new IllegalArgumentException(String.format("States cannot have the value of %s", value));
        }

        return validState;
    }

    static final Map<String, List<String>> statePairMapping = Map.of(
            "SAVE", List.of("SAVED", "UNSAVED"),
            "OPT_IN", List.of("OPTED_IN")
    );

    public static boolean stateValidator(String name, String value) throws NullPointerException {
        return statePairMapping.get(name).contains(value);
    }

    private static StatesEnum lookupByValue(String value) {
        for (StatesEnum statesEnum : StatesEnum.values()) {
            if (statesEnum.value().equals(value)) {
                return statesEnum;
            }
        }

        return null;
    }

}
