package com.loyalty.offerservice.service;

import com.loyalty.offerservice.model.dto.request.OfferRequest;
import com.loyalty.offerservice.model.dto.response.CommonMetadata;
import com.loyalty.offerservice.model.dto.response.OfferServiceExtendedOfferResponse;
import com.loyalty.offerservice.model.dto.response.OfferServiceExtendedOfferResponseForOfferId;
import com.loyalty.offerservice.model.dto.response.OfferServiceResponse;
import com.loyalty.offerservice.model.dto.response.offerdelivery.OfferDeliveryOfferObject;
import com.loyalty.offerservice.model.dto.response.offerdelivery.OfferDeliveryResponse;
import com.loyalty.offerservice.model.dto.response.offerresolver.OfferDTO;
import com.loyalty.offerservice.model.dto.response.offerresolver.OfferResolverResponseDTO;
import com.loyalty.offerservice.model.dto.response.partner.PartnerObject;
import org.springframework.http.HttpHeaders;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

public interface OfferService {

	OfferServiceExtendedOfferResponse getOffers(OfferRequest request) throws Exception;

	/**
	 * This method is to add response headers, which allows us to unlock features:
	 * - Adding cache header based on if the request is public or collector
	 *
	 * @param request
	 * @return HttpHeaders
	 */
	HttpHeaders addResponseHeaders(OfferRequest request);

	OfferServiceExtendedOfferResponseForOfferId getOfferByOfferId(OfferRequest request) throws GenericException, IOException;

	List<OfferServiceResponse> getOfferResponse(OfferResolverResponseDTO offerResolverResponse, OfferRequest offerRequest, Map<UUID, List<Map<String, Object>>> offerStateResponse) throws IOException;

	List<OfferServiceResponse> getOfferResponseAsync(CompletableFuture<OfferResolverResponseDTO> offerResolverResponse, OfferDeliveryResponse offerDeliveryResponse, OfferRequest offerRequest, CompletableFuture<Map<UUID, List<Map<String, Object>>>> offerStateResponse, List<UUID> offerDeliveryIdList) throws IOException;

	OfferServiceResponse getOfferResponseForOfferId(OfferResolverResponseDTO offerResolverResponse, OfferDeliveryOfferObject deliveryOfferObject, OfferRequest offerRequest, Map<UUID, List<Map<String, Object>>> offerStateResponse) throws IOException;
	OfferServiceExtendedOfferResponseForOfferId getOfferByOfferIdAdmin(OfferRequest request) throws IOException;
}
