{"offers": [{"id": "142b7075-4ab8-4f82-b0f3-5351154f27f5", "offerCode": "RMFNDNDD49GGA2TDH73M", "sysOfferId": "28c7a81f-7aa7-437f-b2ba-6e0aecdbf625", "displayDate": "2023-05-17T00:00:00", "startDate": "2023-05-17T00:00:00", "endDate": "2024-02-23T23:59:59", "displayPriority": 0, "programPriority": 0, "partnerId": "7f0a8705-8d25-4413-b868-0849aaeb2e0a", "partnerName": "Action Car and Truck Accessories", "baseCashRedemption": 95, "offerType": "spend", "qualifier": "category", "awardType": "flatMiles", "awardShort": "10 Bonus Miles", "qualifierShort": "Spend $10+ on 10 in-store*", "legalText": "* Offer valid from May 17, 2023 to February 23, 2024. Valid at participating Action Car and Truck Accessories locations in Alberta, British Columbia, Manitoba, New Brunswick, Newfoundland and Labrador, Nova Scotia, Ontario, Prince Edward Island, Quebec and Saskatchewan. Minimum eligible purchase must be spent in a single transaction. AIR MILES Card must be presented at the time of the purchase. Can be combined with other offers, and AIR MILES offers. ®™ Trademarks of AM Royalties Limited Partnership used under license by AIR MILES Loyalty Inc. Partner, Supplier and Retailer trademarks are owned by the respective Partner, Supplier or Retailer or authorized for their use in Canada.", "image": {"path": "https://s3.amazonaws.com/dev-l1-amrpwl-post-images/processed-images/6a7bb8c0-2dbb-4afb-99df-080ef3e73bc5"}, "regions": ["PE", "SK", "NB", "QC", "MB", "NS", "NL", "ON", "AB", "BC"], "tiers": [{"awardValue": 10.0, "qualifierValue": 10.0, "awardLong": "10 Bonus Miles", "qualifierLong": "Spend $10+ on 10 in-store*"}], "mechanisms": [{"mechanismType": "noAction"}], "availability": ["inStore"], "tags": [], "massOffer": true, "eventBasedOffer": false, "offerCategory1": "0f597ee1-16b8-4a4f-beb7-4654cfd0b45a", "offerCategory2": "dc725f55-1c70-4088-8108-cc199bd2ca32", "active": true, "offerCategory1Label": "Pharmacy", "offerCategory2Label": "Beauty"}, {"id": "5816e258-a3f5-4bd6-ab70-d0cd04b7dfbb", "offerCode": "RMFNDNDD49GGA2TDH73M", "sysOfferId": "28c7a81f-7aa7-437f-b2ba-6e0aecdbf625", "displayDate": "2023-08-01T00:00:00", "startDate": "2023-08-02T00:00:00", "endDate": "2024-09-01T23:59:59", "displayPriority": 250, "programPriority": 0, "partnerId": "7f0a8705-8d25-4413-b868-0849aaeb2e0a", "partnerName": "Action Car and Truck Accessories", "baseCashRedemption": 95, "offerType": "buy", "qualifier": "product", "awardType": "flatMiles", "awardShort": "2 Bonus Miles", "qualifierShort": "Buy 3 one in-store*", "legalText": "* Offer valid from August 2, 2023 to September 1, 2024. Valid at participating Action Car and Truck Accessories locations in Alberta, British Columbia, Manitoba, New Brunswick, Newfoundland and Labrador, Nova Scotia, Ontario, Prince Edward Island, Quebec and Saskatchewan. Minimum eligible purchase must be spent in a single transaction. While supplies last. Product availability may vary by store. We reserve the right to limit quantities. AIR MILES Card must be presented at the time of the purchase. Can be combined with other offers, and AIR MILES offers. ®™ Trademarks of AM Royalties Limited Partnership used under license by AIR MILES Loyalty Inc. Partner, Supplier and Retailer trademarks are owned by the respective Partner, Supplier or Retailer or authorized for their use in Canada.", "image": {"path": "https://s3.amazonaws.com/dev-l1-amrpwl-post-images/processed-images/32153aed-23e0-479a-9642-e02843139f22"}, "regions": ["PE", "ON", "NL", "MB", "QC", "NB", "BC", "NS", "AB", "SK"], "tiers": [{"awardValue": 2.0, "qualifierValue": 3.0, "awardLong": "2 Bonus Miles", "qualifierLong": "Buy 3 one in-store*"}], "mechanisms": [{"mechanismType": "noAction"}], "availability": ["inStore"], "tags": ["4db99143-1768-4f85-a44a-d6fa16011f7b"], "massOffer": true, "eventBasedOffer": false, "offerCategory1": "22b666ae-97c2-4e57-a437-e977de6beef4", "offerCategory2": "4683cd1f-bba8-4dd0-b960-4324a246960b", "active": true, "offerCategory1Label": "Grocery", "offerCategory2Label": "Baby", "promotionLabel": "Mega Miles"}], "offersRequested": 2, "offersNotFound": 0}