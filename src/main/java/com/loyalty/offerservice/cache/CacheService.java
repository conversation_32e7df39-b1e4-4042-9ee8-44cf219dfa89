package com.loyalty.offerservice.cache;

import com.loyalty.offerservice.model.dto.cache.OfferServiceCacheResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static com.linecorp.armeria.internal.shaded.reflections.Reflections.log;
import static net.logstash.logback.marker.Markers.append;
import static net.logstash.logback.marker.Markers.appendEntries;

@Service
@Slf4j
public class CacheService {

    @Autowired
    CaffeineCacheManager cacheManager;

    @Autowired
    private Environment environment;

    public <T extends Cacheable, G> OfferServiceCacheResponse fetchFromCache(List<G> keys, String local, String cacheName, Class<T> cacheType) {
        if (null == local || local.isEmpty()) {
            local = environment.getProperty("cache-expire.default-local");
        }
        Cache cache = cacheManager.getCache(cacheName + "_" + local);
        OfferServiceCacheResponse<T, G> cacheResponse = new OfferServiceCacheResponse<>();
        List<T> cachedObjectList = new ArrayList<>();
        List<G> missingKeys = new ArrayList<>();
        for (G key : keys) {
            T cachedObject = cache.get(key.toString() + "_" + local, cacheType);
            if (cachedObject != null) {
                cachedObjectList.add(cachedObject);
            } else {
                missingKeys.add(key);
            }
        }
        cacheResponse.setCachedObjects(cachedObjectList);
        cacheResponse.setMissingKeys(missingKeys);
        if(!missingKeys.isEmpty()){
            
            log.info(appendEntries(
                Map.of("missedKeys", missingKeys, 
                "cacheName", cacheName, 
                "local", local)), "Cache Missed Keys");
        }
        
        return cacheResponse;
    }

    public <T extends Cacheable> void updateCache(long cacheExpireValue, String key, T cacheObject, String cacheName) {
        Cache cache = cacheManager.getCache(cacheName);

        cacheObject.setExpireValue(cacheExpireValue);
        cache.put(key, cacheObject);
    }

    public <T extends Cacheable> void updateCache(long cacheExpireValue, UUID key, T cacheObject, String cacheName, String local) {
        if (null == local || local.isEmpty()) {
            local = environment.getProperty("cache-expire.default-local");
        }

        Cache cache = cacheManager.getCache(cacheName + "_" + local);
        cacheObject.setExpireValue(cacheExpireValue);
        cache.put(key + "_" + local, cacheObject);
    }

    public void removeCache(String cacheName) {
        Cache cache = cacheManager.getCache(cacheName);
        cache.invalidate();
    }

    public void removeCache(String cacheName, String local) {
        if (null == local || local.isEmpty()) {
            local = environment.getProperty("cache-expire.default-local");
        }

        Cache cache = cacheManager.getCache(cacheName + "_" + local);
        cache.invalidate();
    }
}

