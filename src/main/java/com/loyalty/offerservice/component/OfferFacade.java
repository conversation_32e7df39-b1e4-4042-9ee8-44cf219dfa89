package com.loyalty.offerservice.component;

import com.loyalty.offerservice.enums.LanguageCode;
import com.loyalty.offerservice.model.dto.request.FilterRequest;
import com.loyalty.offerservice.model.dto.request.OfferRequest;
import com.loyalty.offerservice.util.TimestampLocalizationUtil;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.UUID;

import static net.logstash.logback.marker.Markers.append;

@Component
@Slf4j
public class OfferFacade {

    public void configureMDC(UUID correlationId, String originClient, String origin, UUID offerId,
                             Long collectorNumber) {
        MDC.put("correlationId", correlationId.toString());
        MDC.put("originClient", originClient);
        MDC.put("origin", origin);
        MDC.put("uri", String.format("/admin/offers/%s", offerId));
        MDC.put("requestType", "GET");
        MDC.put("collectorNumber", collectorNumber != null ? collectorNumber.toString() : "null");
        MDC.put("offerId", offerId.toString());
    }

    public void closeMDC() {
        MDC.clear();
    }

    public OfferRequest buildOfferRequest(UUID offerId, LocalDateTime localDate, UUID correlationId,
                                          String originClient,
                                          Long collectorNumber, LanguageCode acceptLanguage,
                                          String origin) {

        FilterRequest filterRequest = FilterRequest.builder().build();
        log.info(append("filterRequest", filterRequest), "GET Offer By OfferId for Admin: Request Received");

        return OfferRequest.builder().correlationId(correlationId).filters(filterRequest)
                .offerId(offerId).localTime(TimestampLocalizationUtil.getTimeStamp(localDate))
                .correlationId(correlationId).originClient(originClient)
                .collectorNumber(collectorNumber).language(acceptLanguage.getCode()).origin(origin).build();
    }
}
