package com.loyalty.offerservice.util.offerdelivery;

import com.loyalty.offerservice.model.dto.request.OfferRequest;
import com.loyalty.offerservice.model.dto.request.OfferDeliveryQueryParams;
import org.springframework.http.HttpHeaders;

public class HeaderUtil {

    public OfferDeliveryQueryParams generateQueryParams(OfferRequest request) {
        return OfferDeliveryQueryParams
                .builder()
                .region(request.getFilters().getRegion())
                .promotionId(request.getFilters().getPromotionId())
                .partnerId(request.getFilters().getPartnerId())
                .categoryId(request.getFilters().getCategoryId())
                .subCategoryId(request.getFilters().getSubCategoryId())
                .type(request.getFilters().getType())
                .programType(request.getFilters().getProgramType())
                .limit(request.getPagination().getLimit())
                .offset(request.getPagination().getOffset())
                .sort(request.getPagination().getSort())
                .massOffer(request.getFilters().getMassOffer())
                .eventBased(request.getFilters().getEventBased())
                .experiment(request.getFilters().getExperiment())
                .excludePartnerId(request.getFilters().getExcludePartnerId()).build();
    }

    public HttpHeaders generateHeaders(OfferRequest request) {
        HttpHeaders headers = new HttpHeaders();
        headers.set("X-Correlation-Id", String.valueOf(request.getCorrelationId()));
        headers.set("X-Origin-Client", request.getOriginClient());
        headers.set("Collector-Number", String.valueOf(request.getCollectorNumber()));
        headers.set("X-Timestamp", String.valueOf(request.getLocalTime()));
        return headers;
    }

}
