package com.loyalty.offerservice.service.offerdelivery;

import com.loyalty.offerservice.model.dto.request.OfferRequest;
import com.loyalty.offerservice.model.dto.response.offerdelivery.OfferDeliveryOfferObject;
import com.loyalty.offerservice.model.dto.response.offerdelivery.OfferDeliveryResponse;
import com.loyalty.offerservice.service.GenericException;

import java.io.IOException;

public interface OfferDeliveryService {

    /**
     * Call offer delivery service for getting public offers (m2m token)
     *
     * @return
     * @throws IOException
     */
    OfferDeliveryResponse getPublicOffers(OfferRequest request) throws IOException;

    /**
     * Call Offer delivery service for getting collector offers (member token)
     *
     * @return
     * @throws IOException
     */
    OfferDeliveryResponse getCollectorOffers(OfferRequest request) throws IOException;

    OfferDeliveryOfferObject getPublicOfferByOfferId(OfferRequest offerRequest) throws GenericException,IOException;

    OfferDeliveryOfferObject getCollectorOfferByOfferId(OfferRequest offerRequest) throws GenericException, IOException;
}
