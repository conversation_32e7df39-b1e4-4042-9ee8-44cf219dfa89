package com.loyalty.offerservice.service.offerdelivery;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.loyalty.offerservice.enums.RegionEnum;
import com.loyalty.offerservice.exception.OffersNotFoundException;
import com.loyalty.offerservice.model.dto.request.FilterRequest;
import com.loyalty.offerservice.model.dto.request.OfferRequest;
import com.loyalty.offerservice.model.dto.request.PaginationRequest;
import com.loyalty.offerservice.model.dto.response.offerdelivery.OfferDeliveryResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.env.Environment;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;

import java.io.IOException;
import java.io.InputStreamReader;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class OfferDeliveryServiceImplTest {

    @Autowired
    ObjectMapper objectMapper;

    @Autowired
    ResourceLoader resourceLoader;

    @Mock
    OfferDeliveryInternalClient offerDeliveryInternalClient;

    @Autowired
    @Spy
    private OfferDeliveryServiceImpl offerDeliveryService;

    @Autowired
    @InjectMocks
    private OfferDeliveryInternalImpl offerDeliveryInternal;

    @Mock
    Environment environment;

    private OfferDeliveryResponse offerResponse;

    private OfferRequest request;

    @BeforeEach
    public void setup() throws IOException {
        MockitoAnnotations.openMocks(this);
        Resource resource = resourceLoader.getResource("classpath:offerDeliveryTest.json");
        offerResponse = objectMapper.readValue(new InputStreamReader(resource.getInputStream()),
                OfferDeliveryResponse.class);

        request = new OfferRequest();
        request.setCollectorNumber(12345678L);
        request.setCorrelationId(UUID.fromString("22a2cdfd-ff82-45f6-bc94-c14a3a533922"));
        request.setOriginClient("web");
        request.setFilters(new FilterRequest(RegionEnum.ON, null, null, null, null, null, null, null, null, null, null, null, null));
        request.setPagination(new PaginationRequest(List.of("promotionId", "massOffer", "-displayPriority", "endDate"), 100, 0));

        when(environment.getActiveProfiles()).thenReturn(new String[]{"fakeEnv"});
    }

    @Test
    void getPublicOffersInternal() throws IOException {
        when(offerDeliveryInternalClient.getPublicOffers(any(), any())).thenReturn(offerResponse);
        OfferDeliveryResponse publicOffers = offerDeliveryService.getPublicOffers(request);
        assertEquals(1, publicOffers.getData().size());
        assertEquals(offerResponse.getData(), publicOffers.getData());
    }

    @Test
    void getPublicOffers() throws IOException {
        when(offerDeliveryInternalClient.getPublicOffers(any(), any())).thenReturn(offerResponse);
        OfferDeliveryResponse publicOffers = offerDeliveryService.getPublicOffers(request);
        assertEquals(1, publicOffers.getData().size());
        assertEquals(offerResponse.getData(), publicOffers.getData());
    }

    @Test
    void getCollectorOffersInternal() throws IOException {
        when(offerDeliveryInternalClient.getCollectorOffers(any(), any())).thenReturn(offerResponse);
        OfferDeliveryResponse collectorOffers = offerDeliveryService.getCollectorOffers(request);
        assertEquals(1, collectorOffers.getData().size());
        assertEquals(offerResponse.getData(), collectorOffers.getData());
    }

    @Test
    void getCollectorOffers() throws IOException {
        when(offerDeliveryInternalClient.getCollectorOffers(any(), any())).thenReturn(offerResponse);
        OfferDeliveryResponse collectorOffers = offerDeliveryService.getCollectorOffers(request);
        assertEquals(1, collectorOffers.getData().size());
        assertEquals(offerResponse.getData(), collectorOffers.getData());
    }

    @Test
    void getPublicOffersThrowsException() {

        doThrow(new OffersNotFoundException("NO_OFFERS_FOUND", "No offers found, try again later")).when(offerDeliveryService).invokeClientForPublicOffers(request);
        OffersNotFoundException exception = assertThrows(OffersNotFoundException.class, () -> {
            offerDeliveryService.getPublicOffers(request);
        });

        assertEquals("NO_OFFERS_FOUND", exception.getErrorCode());
        assertEquals("No offers found, try again later", exception.getMessage());
    }

    @Test
    void getCollectorOffersThrowsException() {

        doThrow(new OffersNotFoundException("NO_OFFERS_FOUND", "No offers found, try again later")).when(offerDeliveryService).invokeClientForCollectorOffers(request);
        OffersNotFoundException exception = assertThrows(OffersNotFoundException.class, () -> {
            offerDeliveryService.getCollectorOffers(request);
        });

        assertEquals("NO_OFFERS_FOUND", exception.getErrorCode());
        assertEquals("No offers found, try again later", exception.getMessage());
    }
}