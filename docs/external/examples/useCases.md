---
title: Offer API Use Case Examples
type: docs
weight: 20.1
no_list: true
---

This document will dicuss specific use cases for the Offer API. How to make a request and what the response will look like. 

## Our Top Picks

This is a simple request to get the top offers for a specific region. This is not paginated and will return the first 20 offers. To limit the number of offers you can use the `limit` query parameter. The `limit` query parameter can be used to limit the number of offers returned. The max value for `limit` is 96. The default value is 20. 

Promotion `Our Top Picks` is a special promotion that is used to surface the top offers for a specific region. This promotion is not available in the `Mapping -> Promotions` documentation. 

We will use our *default* sorting which is defined as `promotionId,massOffer,partnerId,-displayPriority,endDate`

### Request

In this flow `Authorization` header is not required. But if given it will give back targetted offers aswell for that collector, also it gives us the oppertunity to use the `collectorrelevance` sort.

```http
GET /offers?sort=promotionId,massOffer,partnerId,-displayPriority,endDate&region=ON&promotion_id=784bbe95-5299-475b-b03b-5fb274e206c6 HTTP/1.1
Host: cdn.airmilesapis.ca
X-Origin-Client: <REPLACE_WITH_CLIENT_ORIGIN>
Accept-Language: en-US
```

## Card Linked Offers

We can use the filter `program_type` to choose the card linked offers. The value for `program_type` is `cardlinked`.

### Request

In this flow `Authorization` header is not required. But if given it will give back targetted offers aswell for that collector, also it gives us the oppertunity to use the `collectorrelevance` sort.

```http
GET /offers?sort=collectorrelevance&region=ON&program_type=cardlinked HTTP/1.1
Host: cdn.airmilesapis.ca
X-Origin-Client: <REPLACE_WITH_CLIENT_ORIGIN>
Authorization: Bearer <REPLACE_WITH_TOKEN>
Accept-Language: en-US
```

## Offer for you

This use case is for showing the offers most relevant to the specific collector. This will use the `collectorrelevance` sort.

### Request

```http
GET /offers?sort=collectorrelevance&region=ON HTTP/1.1
Host: cdn.airmilesapis.ca
X-Origin-Client: <REPLACE_WITH_CLIENT_ORIGIN>
Authorization: Bearer <REPLACE_WITH_TOKEN>
Accept-Language: en-US
```