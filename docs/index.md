# Offer Service

> The purpose of this document is to prvide a guide on how to integrate with the Offer Service API. This documentation will include **use cases**, **request/response**

## High level

The Offer Service allows for a client to get offers available on the AIR MILES platform. There are two streams the Offer Service can flow, **Authenticated** and **Public**. Depending on the flow new functionality is provided by the Offer Service.

These are the API's avaliable to clients from the Offer Service:
| x                  | Authenticated | Public |
| ------------------ | ------------- | ------ |
| /offers            | ✅             | ✅      |
| /offers/{id}       | ✅             | ✅      |
| /offers/{id}/state | ✅             | ➖      |

> There are more differences between **Authenticated** and **Public**, which we will get to when going more in-depth about each of the above API

## API Overview

### GET /offers
GET a list of offers, with applying filters, sorting, pagination and limits. The Offer API will return all the offers details that are needed to display the offer to the collector.

### GET /offers/{id}
GET offer details for a specific offer, same offer object retuned as /offers. This API will also return the offer details for targeted offers without authorization as well as offers not in region.

### PUT /offers/{id}/state
PUT offer states at collector-level, which tracking when a collector saved or unsaved an offer, or when a collector opted in to an offer.

### Auth vs. Public

Two ways to integrate with any Offer Service API, depending on header.
1. `Authentication` - This allows for partners to request offers that are avaliable to specific collectors (targeted offers), also allows for showing collector their state (saved/opted) for an offers, and allows collector to update the state of an offer.
2. **NO** `Authentication`- This allows viewing of public offers (mass offers), also allows to get details of public offers.