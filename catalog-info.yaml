---
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: offer-service
  description: "Offer Service is an API application which serves up both public and targeted offers to unauthenticated
    and authenticated users. This application also allows to save or opt-in of an offer for those authenticated users 
    Application connects to various downstream services and pulling data from all of them it can create an offer response
    with multiple or single offer and gives extra details about total offers, partners, categories and so on. The services
    it connects to are the following: Offer delivery service, Offer resolver, Offer state service, Partner service, 
    Offer facets (categories) - For now the response of this service is hardcoded as a json file"
  annotations:
    github.com/project-slug: AirMilesLoyaltyInc/offer-service
    github.com/team-slug: AirmilesLoyaltyInc/teamthings
    sonarqube.org/project-key: offer-service
    backstage.io/techdocs-ref: dir:.
    jenkins.loyalty.com/job-full-name: job/offer-service
  tags:
    - business-critical
    - groovy
    - java
    - springboot
    - ecs
    - fargate

  links:
    - url: https://amrp.atlassian.net/wiki/spaces/BKS/overview
      title: Confluence
      icon: LibraryBooks
spec:
  type: service
  owner: teamthings
  lifecycle: production
  definition:
    $text: ./docs/api-docs/internal/OfferAPI-API.yml

  
