# Run my awesome CI
FROM 277983268692.dkr.ecr.us-east-1.amazonaws.com/java:17-amazoncorretto-jdk AS base
ENV APP_HOME=/opt/app/
ENV JAVA_TOOL_OPTIONS="-Dfile.encoding=UTF-8"
ENV LANG=en_US.UTF-8
ENV LC_ALL=en_US.UTF-8
RUN mkdir -p $APP_HOME/src/main/java
WORKDIR $APP_HOME
COPY build.gradle gradlew gradlew.bat $APP_HOME
COPY gradle $APP_HOME/gradle
# CACHE
# Download dependencies, This step using dockers fs layers cache
# https://docs.docker.com/develop/develop-images/dockerfile_best-practices/
RUN ls
RUN ./gradlew dependencies
COPY . .
RUN ./gradlew cleanTest test jacocoTestReport --no-build-cache
RUN ./gradlew build -x test -x integrationTest
# zip build results for sonarqube
RUN apk update && apk add zip  icu-libs
RUN cd build && zip -r build.zip *

# Export Release Artifact For SonarQube To Scan
FROM scratch AS sonarqube
COPY --from=base /opt/app/build/build.zip .


# Package artifact into smallest image
FROM 277983268692.dkr.ecr.us-east-1.amazonaws.com/java:17-amazoncorretto-jdk
ENV APP_HOME=/opt/app
WORKDIR $APP_HOME
COPY --from=base $APP_HOME/build/libs/app.jar $APP_HOME/app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/opt/app/app.jar"]
