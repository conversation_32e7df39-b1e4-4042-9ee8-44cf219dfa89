package com.loyalty.offerservice.service.offerdelivery;

import com.loyalty.offerservice.config.FeignLoggingConfiguration;
import com.loyalty.offerservice.config.FeingODSCustomErrorHandler;
import com.loyalty.offerservice.enums.RegionEnum;
import com.loyalty.offerservice.model.dto.request.OfferDeliveryQueryParams;
import com.loyalty.offerservice.model.dto.response.offerdelivery.OfferDeliveryOfferObject;
import com.loyalty.offerservice.model.dto.response.offerdelivery.OfferDeliveryResponse;
import org.springframework.cloud.openfeign.CollectionFormat;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.UUID;

@FeignClient(name = "OfferDeliveryInternalClient", url = "${offer-delivery.internal}", configuration = {FeignLoggingConfiguration.class, FeingODSCustomErrorHandler.class})
@CollectionFormat(feign.CollectionFormat.CSV)
public interface OfferDeliveryInternalClient {

    @GetMapping(value = "/offer-delivery-service/public/offers", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    OfferDeliveryResponse getPublicOffers(
            @SpringQueryMap OfferDeliveryQueryParams offerDeliveryQueryParams,
            @RequestHeader HttpHeaders headers);

    @GetMapping(value = "/offer-delivery-service/collector/offers", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    OfferDeliveryResponse getCollectorOffers(
            @SpringQueryMap OfferDeliveryQueryParams offerDeliveryQueryParams,
            @RequestHeader HttpHeaders headers);

    @GetMapping(value = "/offer-delivery-service/public/offers/{offerId}", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    OfferDeliveryOfferObject getPublicOfferByOfferId(
            @PathVariable("offerId") UUID offerId,
            @RequestParam RegionEnum region,
            @RequestHeader HttpHeaders headers);

    @GetMapping(value = "/offer-delivery-service/collector/offers/{offerId}", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    OfferDeliveryOfferObject getCollectorOfferByOfferId(
            @PathVariable("offerId") UUID offerId,
            @RequestParam RegionEnum region,
            @RequestHeader HttpHeaders headers);
}
