package com.loyalty.offerservice.service.offerdelivery;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.loyalty.offerservice.exception.BadRequestException;
import com.loyalty.offerservice.exception.OffersNotFoundException;
import com.loyalty.offerservice.exception.ResourceNotFoundException;
import com.loyalty.offerservice.model.dto.request.OfferRequest;
import com.loyalty.offerservice.model.dto.response.offerdelivery.OfferDeliveryOfferObject;
import com.loyalty.offerservice.model.dto.response.offerdelivery.OfferDeliveryResponse;

import com.loyalty.offerservice.util.offerdelivery.HeaderUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;

import java.io.IOException;

@Service
@Slf4j
public class OfferDeliveryServiceImpl extends HeaderUtil implements OfferDeliveryService {

    @Autowired
    ObjectMapper objectMapper;

    @Autowired
    ResourceLoader resourceLoader;

    @Autowired
    OfferDeliveryInternalImpl offerDeliveryInternalClient;

    @Override
    public OfferDeliveryResponse getPublicOffers(OfferRequest request) throws IOException {

        OfferDeliveryResponse offerResponse = null;
        try {
            offerResponse = invokeClientForPublicOffers(request);
        } catch (Exception e) {
            offerDeliveryFallback(request, e);
        }
        return offerResponse;
    }

    @Override
    public OfferDeliveryResponse getCollectorOffers(OfferRequest request) throws IOException {

        OfferDeliveryResponse offerResponse = null;
        try {
            offerResponse = invokeClientForCollectorOffers(request);
        } catch (Exception e) {
            offerDeliveryFallback(request, e);
        }
        return offerResponse;
    }

    @Override
    public OfferDeliveryOfferObject getPublicOfferByOfferId(OfferRequest offerRequest) throws IOException {
        OfferDeliveryOfferObject offerResponse = null;
        try{
            offerResponse = invokeClientForPublicOfferById(offerRequest);
        } catch (Exception e) {
            offerDeliveryFallbackForSingleOffer(offerRequest, e);
        }
        return offerResponse;
    }

    @Override
    public OfferDeliveryOfferObject getCollectorOfferByOfferId(OfferRequest offerRequest) throws  IOException {
        OfferDeliveryOfferObject offerResponse = null;
        try{
            offerResponse = invokeClientForCollectorOfferByOfferId(offerRequest);
        } catch (Exception e) {
            offerDeliveryFallbackForSingleOffer(offerRequest, e);
        }
        return offerResponse;
    }

    public OfferDeliveryResponse invokeClientForCollectorOffers(OfferRequest request){
        return offerDeliveryInternalClient.getCollectorOffers(request);
    }

    public OfferDeliveryResponse invokeClientForPublicOffers(OfferRequest request){
        return offerDeliveryInternalClient.getPublicOffers(request);
    }

    private OfferDeliveryOfferObject invokeClientForPublicOfferById(OfferRequest request){
        return offerDeliveryInternalClient.getPublicOfferByOfferId(request);
    }

    private OfferDeliveryOfferObject invokeClientForCollectorOfferByOfferId(OfferRequest request){
        return offerDeliveryInternalClient.getCollectorOfferByOfferId(request);
    }

    private OfferDeliveryResponse offerDeliveryFallback(OfferRequest offerRequest, Throwable throwable) {
        handleOfferDeliveryFallback(offerRequest, throwable);
        return null;
    }

    private OfferDeliveryOfferObject offerDeliveryFallbackForSingleOffer(OfferRequest offerRequest, Throwable throwable){
        handleOfferDeliveryFallback(offerRequest, throwable);
        return null;
    }

    private void handleOfferDeliveryFallback(Object offerRequest, Throwable throwable){
        log.error("[offerDeliveryFallback] An error occurred when trying to invoke Offer Delivery. Client error is: {}", throwable);
        log.error("[offerDeliveryFallback] Unable to invoke Offer Delivery for request: {}. Throwing OffersNotFoundException",
                offerRequest);
        if(throwable instanceof ResourceNotFoundException){
            throw (ResourceNotFoundException) throwable;
        } else if (throwable instanceof BadRequestException) {
            throw (BadRequestException) throwable;
        }
        throw new OffersNotFoundException("NO_OFFERS_FOUND", "No offers found, try again later");
    }
}
