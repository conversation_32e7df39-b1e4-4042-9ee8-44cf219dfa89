package com.loyalty.offerservice;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.spockframework.util.IoUtil;

import java.io.IOException;
import java.io.InputStream;

public class TestHelper {
    private static ObjectMapper objectMapper = new ObjectMapper();

    public static <T> T deserializeJson(String modelPath, Class<T> modelType) throws IOException {
        InputStream modelInputStream = getInputStream(modelPath);
        return objectMapper.readValue(modelInputStream, modelType);
    }

    private static InputStream getInputStream(String modelPath) {
        ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
        return classLoader.getResourceAsStream(modelPath);
    }

    public static String getTextFromFile(String filePath) throws IOException {
        return IoUtil.getText(getInputStream(filePath));
    }
}
