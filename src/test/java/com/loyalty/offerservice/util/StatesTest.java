package com.loyalty.offerservice.util;

import com.loyalty.offerservice.model.dto.request.OfferState;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

public class StatesTest {

    @Test
    public void testGetInstanceForNullValue() {
        OfferState states = States.getInstance(null);

        Assertions.assertNull(states);
    }

    @Test
    public void testGetInstanceForEmptyValue() {
        OfferState states = States.getInstance("");

        Assertions.assertNull(states);
    }

    @Test
    public void testGetInstanceForTrimEmptyValue() {
        OfferState states = States.getInstance("   ");

        Assertions.assertNull(states);
    }

    @Test
    public void testGetInstanceForWrongValue() {
        IllegalArgumentException thrown = Assertions.assertThrows(
                IllegalArgumentException.class,
                () -> States.getInstance("WRONG")
        );

        Assertions.assertTrue(thrown.getMessage().contains("States cannot have the value of"));
    }

    @Test
    public void testGetInstanceForLowerCaseValue() {
        IllegalArgumentException thrown = Assertions.assertThrows(
                IllegalArgumentException.class,
                () -> States.getInstance("saved")
        );

        Assertions.assertTrue(thrown.getMessage().contains("States cannot have the value of"));
    }

    @Test
    public void testGetInstanceForSavedValue() {
        OfferState states = States.getInstance("SAVED");

        Assertions.assertEquals(states, States.SAVED);
    }

    @Test
    public void testGetInstanceForUnsavedValue() {
        OfferState states = States.getInstance("UNSAVED");

        Assertions.assertEquals(states, States.UNSAVED);
    }

    @Test
    public void testGetInstanceForOptedInValue() {
        OfferState states = States.getInstance("OPTED_IN");

        Assertions.assertEquals(states, States.OPT_IN);
    }

    @Test
    public void testStateValidatorForSaved() {
        Assertions.assertTrue(States.stateValidator("SAVE", "SAVED"));
    }

    @Test
    public void testStateValidatorForUnsaved() {
        Assertions.assertTrue(States.stateValidator("SAVE", "UNSAVED"));
    }

    @Test
    public void testStateValidatorForOptedIn() {
        Assertions.assertTrue(States.stateValidator("OPT_IN", "OPTED_IN"));
    }

    @Test
    public void testStateValidatorForWrongCombination() {
        Assertions.assertFalse(States.stateValidator("OPT_IN", "SAVED"));
    }

}
