@Library('jenkins-shared-lib-v2') _

def gitTag = new Date().format('yyyyMMddHHmmss')
def repoName, region, releaseVersion

def newRelease = false

// This job is triggered when a PR is merged into main/master.

pipeline {
  agent { label 'aws-ec2' }
  stages {
    stage ('Build artifact') {
      when { 
        expression { !gitUtils.isJenkinsCommit() }
      }
      steps {
        script {
          def appMeta = readProperties(file: 'meta.config')
          repoName = appMeta.APP_ID
          region = appMeta.REGION
                    
          if(region){
            dockerUtils.buildAndPublishV2(appMeta.APP_ID, [gitTag, 'latest'], '-f dockerfiles/Dockerfile-Release .', region) 
          }else{     
            dockerUtils.buildAndPublishV2(appMeta.APP_ID, [gitTag, 'latest'], '-f dockerfiles/Dockerfile-Release .')       
          }
          
          gitUtils.pushTag(gitTag)   
                    
        }
      }
    }
    // Please make sure commit messages are properly formatted 
    // according to the semantic release standards mentioned in the README of the repo
    stage ('Semantic Release and promote build') {
      agent {
        docker {
          image '277983268692.dkr.ecr.us-east-1.amazonaws.com/ubuntu-22-slim'
        }
      }
      when {
        expression { gitUtils.isReleasable() }
      }
      steps {
        script {
          gitTag = 'latest'

          (releaseVersion, newRelease) = releaseUtils.semanticRelease()
          println "Release Version is " + releaseVersion
          
          // Tag the 'latest' tagged ECR image with the new semantic version
          if (newRelease) {
            docker.withRegistry("https://277983268692.dkr.ecr.${region}.amazonaws.com") {
            sh """
              MANIFEST=\$(aws ecr batch-get-image --region ${region} --repository-name ${repoName} --image-ids imageTag=${gitTag} --query 'images[].imageManifest' --output text)
              aws ecr put-image --region ${region} --repository-name ${repoName} --image-tag ${releaseVersion} --image-manifest "\$MANIFEST"
              aws ecr describe-images --region ${region} --repository-name ${repoName} 
              """
            }
          }
        }
      }
    }

    stage ('Build techdocs') {
      agent {
        docker {
          image '277983268692.dkr.ecr.us-east-1.amazonaws.com/ubuntu-22-slim'
        }
      }
      steps {
        script {
          backstage.generateTechDocs()
          backstage.publishTechDocs()
        }
      }
    }
        
      stage ('deploy to dev') {
      when { 
        expression { gitUtils.isReleasable() }
      }    
      steps {
        script {
          def envType = 'dev'
          println "Deploying ECS Service Application"
          awsUtils.deployAndTestECS(envType, releaseVersion, "echo dev test")
        }
      }
    }

    stage("DEV Integration Test") {
      agent {
        docker {
          image '277983268692.dkr.ecr.us-east-1.amazonaws.com/java:17-amazoncorretto-jdk'
        }
      }
      when {
        expression { newRelease }
      }
      steps {

        catchError(buildResult: 'SUCCESS', stageResult: 'FAILURE') {
          script {
            println "Dev Test"
            withCredentials([[$class       : "AmazonWebServicesCredentialsBinding",
                              credentialsId: "nonprod-amrpif-aws-deployer"]]) {
              sh(script: './gradlew integrationTest -Dkarate.env="dev" -Dkarate.options="--tags @unauthenticated,@authenticated"')
            }
          }
        }

      }
      post {
        always {
          cucumber buildStatus: null,
                  fileIncludePattern: '**/build/karate-reports/*.json,',
                  trendsLimit: 10
        }
        failure {
          echo "Failed Dev integration Test"
        }
      }
    }

    stage ('deploy to int') {
      when {
        expression { newRelease }
      }
      steps {
        script {
          def envType = 'int'
          println "Deploying ECS Service Application"
          awsUtils.deployAndTestECS(envType, releaseVersion, "echo int test")
        }
      }
    }

    stage ('deploy to uat') {
      when {
        expression { newRelease }
      }
      steps {
        script {
          def envType = 'uat'
          println "Deploying ECS Service Application"
          awsUtils.deployAndTestECS(envType, releaseVersion, "echo uat test")
        }
      }
    }

/*
    stage ('deploy to load') {
      when { 
        expression { newRelease }
      }    
      steps {
        script {
          def envType = 'load'
          //#### Please update the resources.yaml before uncommenting this code for the infrastructure to be deployed.          
          println "Deploying infrastructure"
          awsUtils.deployInfra(
            "${envType}-${repoName}-infra",
            envType,
            "file://aws/cfn/infrastructure/resources.yaml",
            "file://env/${envType}-infra.params.json",
            "file://env/${envType}-infra.tags.json",
            releaseVersion,
            "")
          println "Deploying ECS Service Application"
          awsUtils.deployAndTestECS(envType, releaseVersion, "echo load test")
        }
      }
    }
*/
    stage ('run checkmarx') {
      when {
        expression { newRelease }
      }
      steps {
        script {
          checkmarx.scan()              
        }
      }
    }

    stage('Trigger cd pipeline?') {
      when {
        expression { newRelease }
      }
      steps {
        script {
          input(
          message: 'Continue to build cd pipeline?', 
          submitter: "OS-AWS-JenkinsDeveloper",
          ok: 'Trigger cd pipeline')
        }
      }
    }
       
    stage ('Trigger CD') {
      when { 
        expression { newRelease }
      }
      steps {
        script {
          build job: "${repoName}/cd", wait: false,
            parameters: [
              string(name: 'releaseVersion', value: releaseVersion),
              string(name: 'repoName', value: repoName),
            ]
        }
      }
    }
  }
  post {
    cleanup {
      deleteDir()
      dir("${workspace}@tmp") {
        deleteDir()
      }
      dir("${workspace}@script") {
        deleteDir()
      }
    }      
  }
}