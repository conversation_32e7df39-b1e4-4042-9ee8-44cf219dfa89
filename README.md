Offer-Service
=============

A REST API for getting Offers information with public and Collectors access.
This application also allows to save or opt-in of an offer for those authenticated users
Application connects to various downstream services and pulling data from all of them it can create an offer response
with multiple or single offer and gives extra details about total offers, partners, categories and so on. The services
it connects to are the following: Offer delivery service, Offer resolver, Offer state service, Partner service,
Offer facets (categories) - For now the response of this service is hardcoded as a json file.

This project is based in SpringBoot 3 and build with `gradle`.

## Repository Description

   ### Project Structure
    .
    ├── .github                # Github infra
    ├── aws                    # AWS Infrastructure derfinition for EC2
    ├── dockerfiles            # Docker files definitions.
    ├── docs                   # API Documentation
    ├── env                    # Configuration files by environment. 
    ├── gradle                 # gradle wrapper
    ├── jenkins                # Jenkins pipelines definitions. 
    ├── mock                   # Wiremock
    └── src

   ### Environment Configuration

   Environment specific variables are stored in files in the `env/` folder.

   Each file is prefixed with the short name of the environment.  For example dev, int, uat, load, cert, prod

   One set of three files is required for each environment.

   Environment Files
   Name | Contents
   ---- | -----------
   [env].env | a list of environment variables which will be passed to the ECS container on startup
   [env].params.json | values for parameters in the CF deployment templates
   [env].tags.json | a list of tags to apply to the CF stack.

   ### CI/CD
   
   Jenkins jobs for pr checks, build and deploy are defined in `jenkins/` folder.
   
   Jenkins jobs definitions are based in the [Jenkins Shared library](https://github.com/LoyaltyOne/jenkins-shared-lib-v2),
   for more details: https://amrp.atlassian.net/wiki/spaces/jenkins/pages/68618476/Jenkins+Shared+Library

   
   Every jenkins file define a job in [Jenkins for offer-service](https://jenkinstein.loyalty.com/job/offer-service/) project and deployment is done to EC2 based on AWS YAML file definition, (see below)

   ### Infrastructure

   Offer-service is deployed to AWS EC2 and infrastructure definition is done through a CloudFormation YAML file in `aws/cfn/`.

   For local development a Wiremock server has been configured to simulate responses and behavior in any downstream service,
   these definitions of downstream services responses are located on `mock/files/` and `mock/mappings/`

   ### API Contract
   
   Main endpoints:

    - GET /offers (public)
    - GET /offers (for Collectors)
    - PUT /offers/{offerId}/states (for Collectors)

   API Contract definition could be found in swaqgger format at `docs/api-docs/`

## Local setup

<details>
   <summary>Expand</summary>

   To start the application locally esure you have all required prerequisties

   - Install Java17 (You can install Java using [homebrew](https://brew.sh/) and manage using [jenv](https://github.com/jenv/jenv) for Mac) [install guide for windows](https://docs.oracle.com/en/java/javase/17/install/installation-jdk-microsoft-windows-platforms.html#GUID-A7E27B90-A28D-4237-9383-A58B416071CA)
   - Install [Docker Desktop](https://www.docker.com/products/docker-desktop) to run the dockerfile
   
   - Clone this repo and change directory to the root of the repo.
   ```
      git clone https://github.com/LoyaltyOne/offer-service.git
      OR
      <NAME_EMAIL>:LoyaltyOne/offer-service.git

      cd /offer-service
   ```
  
   - Compile and start the application 
   ```
   ./gradlew bootRun --args='--spring.profiles.active=local'
   ```
   - Test the API (open a new terminal window)
      ```
      curl -sb -H "Accept: application/json" "http://localhost:8080/offer-service/health"
      ...
      {"applicationName":"offer-service","status":"Success"}  
      ```
     
   - When you are done testing you can close the application session
   ```
   Mac: Ctrl + C      
   Windows:Ctrl + C
   ```

   ### Compile the application
   ```
   Linux/Mac: ./gradlew build
   Windows: .\gradlew.bat build
   ```
   The application will be build under
   ```
   build/libs/
   ```

   ### Execute unit test
   ```
   Linux/Mac: ./gradlew test
   Windows: .\gradlew.bat test
   ```

   ### Start Mock server
   ```
   docker compose -f mock/docker-compose.yml -p mock up -d
   ```

   ### Build with Docker
   ```
   docker build --tag myapi:latest
   ```

   ### Run Docker image
   ```
   docker run -it --rm -p 8080:8080 --name api myapi
   ```
   ### Stop the container
   ```
   Ctrl + c
   ```
   or

   ```
   docker stop $(docker ps -qf name=api)
   ```

</details>

## Design Considerations

Not any authorization validation is done inside offer-service, assumption is all the call to this service are done through API Gateway and security is in place for that service.

Caching is required for this service in order to store information coming from downstream service, Partners information is eager loaded when application is started for caching purposes.

Cache implementation based on Spring CacheManager for [Caffeine](https://github.com/ben-manes/caffeine)

CircuitBreaker implementation based on [resillence4j](https://github.com/resilience4j/resilience4j)

Exception handling for REST Controllers is centralized on `GlobalExceptionHandler`

## Useful links

   - [Runbook](https://amrp.atlassian.net/wiki/spaces/OfferService/pages/*********/Offer+Service+Runbook)
   - Monitoring: [Runscope](https://www.runscope.com/radar/wo6bgsnjln30)
   - Code Quality: [SonarQube](https://sonarqube.loyalty.com/dashboard?id=offer-service)

## Configuration Changes for CD

<details>
   <summary>Expand</summary>

   ```
   Modify env\[env].params.json, env\[env].tags.json, aws\cfn\service.yaml, env\service.json
   ```
   aws\cfn\services.yaml and env\service.json

      - NetworkConfiguration: Ensure Imported Subnet Naming matches the account you are deploying to (App Subnet).
      - SecurityGroupIngress: Ensure the imported CIDR IPs are matching with the account (LoadBalancer Subnet).
      - Check out this guide: https://confluence.loyalty.com/display/AWS/Find+Network+Stack+Information.
   
   env\[env]-infra.params.json

      - Parameters to deploy the infra resources
   
   env\[env].tags.json

      - set tags for app resources

   env\[env].params.json

      - set import parameters for services.yaml(json) CFN template
      - set scheduler to shut dowrn the services in nonprod environments after office hours. For more details: https://loyaltyone.atlassian.net/wiki/spaces/AA/pages/********/AWS+Tagging+Standard

</details>


## Versioning 📦

<details>
   <summary>Expand</summary>

   Versioning is fully automated and managed by [semantic-release](https://github.com/semantic-release/semantic-release).

   ### How does it work?

   #### Commit message format

   **semantic-release** uses the commit messages to determine the type of changes in the codebase. Following formalized conventions for commit messages, **semantic-release** automatically determines the next [semantic version](https://semver.org) number, generates a changelog and publishes the release.

   By default **semantic-release** uses [Angular Commit Message Conventions](https://github.com/angular/angular.js/blob/master/DEVELOPERS.md#-git-commit-guidelines). The commit message format can be changed with the [`preset` or `config` options](docs/usage/configuration.md#options) of the [@semantic-release/commit-analyzer](https://github.com/semantic-release/commit-analyzer#options) and [@semantic-release/release-notes-generator](https://github.com/semantic-release/release-notes-generator#options) plugins.

   Tools such as [commitizen](https://github.com/commitizen/cz-cli) or [commitlint](https://github.com/conventional-changelog/commitlint) can be used to help contributors and enforce valid commit messages.

   Here is an example of the release type that will be done based on a commit messages:

   | Commit message                                                                                                                                                                                   | Release type               |
   | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | -------------------------- |
   | `fix(pencil): stop graphite breaking when too much pressure applied`                                                                                                                             | Patch Release              |
   | `feat(pencil): add 'graphiteWidth' option`                                                                                                                                                       | ~~Minor~~ Feature Release  |
   | `perf(pencil): remove graphiteWidth option`<br><br>`BREAKING CHANGE: The graphiteWidth option has been removed.`<br>`The default graphite width of 10mm is always used for performance reasons.` | ~~Major~~ Breaking Release |

</details>


## Scorecard
[scorecard.md](scorecard.md)


## Enable / Disable Dependabot

- Please see following link for enable / disable dependabot.
  https://docs.github.com/en/code-security/dependabot/dependabot-security-updates/configuring-dependabot-security-updates


## Skip CERT stage deployment

To bypass CERT stage deployment, please set `ENV_CERT=false` in file meta.config. The default value is `ENV_CERT=true`, enabling CERT stage deployment.


## Tips
   
   - Please make sure commit messages are properly formatted according to the semantic release standards mentioned above.
   - Windows Users: highly recommend installing gitbash to have a similar experience to Linux/ Mac Users.
   - See https://spring.io/guides/gs/spring-boot/#scratch for more information.
   - In order to be better stewards of this pipeline, please ensure that any modifications to your Jenkinsfiles involving adding 'input' steps (a.k.a. Approval Gates) do not consume any agents/executors in Jenkins. Failing to do this will add uneccessary congestion to Jenkins, and dramatically increase lead deployment time into production for other teams. 
