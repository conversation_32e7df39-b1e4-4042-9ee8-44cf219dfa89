package aws;
import com.amazonaws.services.secretsmanager.AWSSecretsManager;
import com.amazonaws.services.secretsmanager.AWSSecretsManagerClientBuilder;
import com.amazonaws.services.secretsmanager.model.GetSecretValueRequest;
import com.amazonaws.services.secretsmanager.model.GetSecretValueResult;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.Map;

public class SecretManagerConnection  {

    public String getAWSSecretClientid() {
        try {
            String secretId = "dev-offer-service-integration-test-jwt-secret";
            String region = "ca-central-1";

            AWSSecretsManager secret = AWSSecretsManagerClientBuilder.standard().withRegion(region).build();
            GetSecretValueRequest request = new GetSecretValueRequest();
            request.setSecretId(secretId);
            GetSecretValueResult secretValue;
            secretValue = secret.getSecretValue(request);

            ObjectMapper objectMapper = new ObjectMapper();
            String secretBinaryString = secretValue.getSecretString();
            TypeReference<Map<String, String>> typeReference = new TypeReference<Map<String, String>>() {};
            Map<String, String> secretMap = objectMapper.readValue(secretBinaryString, typeReference);
            String clientId = secretMap.get("clientId");
            return clientId;

        } catch (Exception ex) {
            return ex.getMessage();
        }
    }

    public String getAWSSecretClientSecret() {
        try {
            String secretId = "dev-offer-service-integration-test-jwt-secret";
            String region = "ca-central-1";

            AWSSecretsManager secret = AWSSecretsManagerClientBuilder.standard().withRegion(region).build();
            GetSecretValueRequest request = new GetSecretValueRequest();
            request.setSecretId(secretId);
            GetSecretValueResult secretValue = new GetSecretValueResult();
            secretValue = secret.getSecretValue(request);

            ObjectMapper objectMapper = new ObjectMapper();
            String secretBinaryString = secretValue.getSecretString();
            TypeReference<Map<String, String>> typeReference = new TypeReference<Map<String, String>>() {};
            Map<String, String> secretMap = objectMapper.readValue(secretBinaryString, typeReference);
            String clientSecret = secretMap.get("clientSecret");
            return clientSecret;
        } catch (Exception ex) {
            return null;
        }

    }
}
