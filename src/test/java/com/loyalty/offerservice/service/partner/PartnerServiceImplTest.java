package com.loyalty.offerservice.service.partner;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.loyalty.offerservice.cache.CacheService;
import com.loyalty.offerservice.model.dto.request.OfferRequest;
import com.loyalty.offerservice.model.dto.response.partner.PartnerObject;
import com.loyalty.offerservice.model.dto.response.partner.PartnerResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cache.Cache;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.cloud.client.circuitbreaker.CircuitBreaker;
import org.springframework.cloud.client.circuitbreaker.CircuitBreakerFactory;
import org.springframework.core.env.Environment;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;

import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class PartnerServiceImplTest {
    private static final String tokenValue = "***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
    @Mock
    PartnerClient partnerClient;

    @Mock
    Environment environment;

    @Spy
    CaffeineCacheManager cacheManager;

    @Autowired
    ObjectMapper objectMapper1;

    @Autowired
    ResourceLoader resourceLoader1;

    @Spy @InjectMocks
    CacheService cacheService;

    @InjectMocks
    private PartnerServiceImpl partnerService;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getPartner() throws IOException {
        List<UUID> partners = new ArrayList<>(List.of(UUID.fromString("22a2cdfd-ff82-45f6-bc94-c14a3a533922"), UUID.fromString("22a2cdfd-ff82-45f6-bc94-c14a3a533924")));
        OfferRequest request = new OfferRequest();

        Resource resource = resourceLoader1.getResource("classpath:partnersResponse1.json");
        PartnerResponse partnerResponse1 = objectMapper1.readValue(new InputStreamReader(resource.getInputStream()),
                PartnerResponse.class);

        Resource resource2 = resourceLoader1.getResource("classpath:partnersResponse2.json");
        PartnerResponse partnerResponse2 = objectMapper1.readValue(new InputStreamReader(resource2.getInputStream()),
                PartnerResponse.class);

        
        request.setCollectorNumber(12345678L);
        request.setCorrelationId(UUID.fromString("22a2cdfd-ff82-45f6-bc94-c14a3a533922"));
        request.setOriginClient("web");
        request.setLanguage("en-CA");

        when(environment.getActiveProfiles()).thenReturn(new String[]{"test"});
        when(partnerClient.getPartner(any(), any())).thenReturn(partnerResponse1, partnerResponse2, partnerResponse2);
        when(environment.getProperty("cache-expire.partner.value", Long.class)).thenReturn(3L);
        when(environment.getProperty(any())).thenReturn("3");
        when(partnerService.invokeClient(partners, request)).thenReturn(partnerResponse1, partnerResponse2, partnerResponse2);
        partnerService.getPartner(partners, request);
        PartnerResponse finalPartnerResponse2 = partnerService.getPartner(partners, request);
        assertEquals(2, finalPartnerResponse2.getResults().size());
        assertEquals("cache1", finalPartnerResponse2.getResults().get(0).getName());

        cacheManager.getCache("partners_en-CA").invalidate();
        PartnerResponse finalPartnerResponse3 = partnerService.getPartner(partners, request);
        assertEquals(4, finalPartnerResponse3.getResults().size());
        assertEquals("cache2", finalPartnerResponse3.getResults().get(0).getName());
    }

    @Test
    void getPartners() throws IOException {
        OfferRequest request = new OfferRequest();

        Resource resource = resourceLoader1.getResource("classpath:partnersResponse1_en.json");
        PartnerResponse partnerResponse1 = objectMapper1.readValue(new InputStreamReader(resource.getInputStream()),
                PartnerResponse.class);

        Resource resource2 = resourceLoader1.getResource("classpath:partnersResponse2_en.json");
        PartnerResponse partnerResponse2 = objectMapper1.readValue(new InputStreamReader(resource2.getInputStream()),
                PartnerResponse.class);

        Resource resource_fr = resourceLoader1.getResource("classpath:partnersResponse1_fr.json");
        PartnerResponse partnerResponse1_fr = objectMapper1.readValue(new InputStreamReader(resource_fr.getInputStream()),
                PartnerResponse.class);

        Resource resource2_fr = resourceLoader1.getResource("classpath:partnersResponse2_fr.json");
        PartnerResponse partnerResponse2_fr = objectMapper1.readValue(new InputStreamReader(resource2_fr.getInputStream()),
                PartnerResponse.class);

        
        request.setCollectorNumber(12345678L);
        request.setCorrelationId(UUID.fromString("22a2cdfd-ff82-45f6-bc94-c14a3a533922"));
        request.setOriginClient("web");
        request.setLanguage("en-CA");

        when(environment.getActiveProfiles()).thenReturn(new String[]{"test"});
        when(partnerClient.getPartners(any(), anyInt(), any())).thenReturn(partnerResponse1, partnerResponse2, partnerResponse1_fr, partnerResponse2_fr);
        when(environment.getProperty("cache-expire.partner.value", Long.class)).thenReturn(50000000L);
        partnerService.loadPartners(request);
        request.setLanguage("fr-CA");
        partnerService.loadPartners(request);

        Collection<String> names = cacheManager.getCacheNames();
        assertTrue(names.contains("partners_en-CA"));
        assertTrue(names.contains("partners_fr-CA"));
        Cache enPartners = cacheManager.getCache("partners_en-CA");
        Cache frPartners = cacheManager.getCache("partners_fr-CA");
        PartnerObject partner = enPartners.get("22a2cdfd-ff82-45f6-bc94-c14a3a533922_en-CA", PartnerObject.class);
        assertEquals("cache1", partner.getName());
        assertEquals("en-CA", partner.getLocale());
        assertNotNull(partner.getTerms());

        assertEquals( "cache1",enPartners.get("22a2cdfd-ff82-45f6-bc94-c14a3a533924_en-CA", PartnerObject.class).getName());
        assertEquals( "cache1",enPartners.get("22a2cdfd-ff82-45f6-bc94-c14a3a533925_en-CA", PartnerObject.class).getName());
        assertEquals( "cache1",enPartners.get("22a2cdfd-ff82-45f6-bc94-c14a3a533926_en-CA", PartnerObject.class).getName());
        assertEquals( "cache2",enPartners.get("22a2cdfd-ff82-45f6-bc94-c14a3a533927_en-CA", PartnerObject.class).getName());
        assertEquals( "cache2",enPartners.get("22a2cdfd-ff82-45f6-bc94-c14a3a533928_en-CA", PartnerObject.class).getName());
        assertEquals( "cache2",enPartners.get("22a2cdfd-ff82-45f6-bc94-c14a3a533929_en-CA", PartnerObject.class).getName());
        assertEquals( "cache2",enPartners.get("22a2cdfd-ff82-45f6-bc94-c14a3a533921_en-CA", PartnerObject.class).getName());

        assertEquals( "cache1_fr",frPartners.get("22a2cdfd-ff82-45f6-bc94-c14a3a533924_fr-CA", PartnerObject.class).getName());
        assertEquals( "cache1_fr",frPartners.get("22a2cdfd-ff82-45f6-bc94-c14a3a533925_fr-CA", PartnerObject.class).getName());
        assertEquals( "cache1_fr",frPartners.get("22a2cdfd-ff82-45f6-bc94-c14a3a533926_fr-CA", PartnerObject.class).getName());
        assertEquals( "cache2_fr",frPartners.get("22a2cdfd-ff82-45f6-bc94-c14a3a533927_fr-CA", PartnerObject.class).getName());
        assertEquals( "cache2_fr",frPartners.get("22a2cdfd-ff82-45f6-bc94-c14a3a533928_fr-CA", PartnerObject.class).getName());
        assertEquals( "cache2_fr",frPartners.get("22a2cdfd-ff82-45f6-bc94-c14a3a533929_fr-CA", PartnerObject.class).getName());
        assertEquals( "cache2_fr",frPartners.get("22a2cdfd-ff82-45f6-bc94-c14a3a533921_fr-CA", PartnerObject.class).getName());

    }
}