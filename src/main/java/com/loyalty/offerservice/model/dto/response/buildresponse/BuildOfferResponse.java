package com.loyalty.offerservice.model.dto.response.buildresponse;


import com.loyalty.offerservice.model.dto.response.*;
import com.loyalty.offerservice.model.dto.response.offerresolver.Image;
import com.loyalty.offerservice.model.dto.response.offerresolver.OfferDTO;
import com.loyalty.offerservice.model.dto.response.partner.PartnerObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import java.util.*;
import static net.logstash.logback.marker.Markers.*;

@Component
@Slf4j
public class BuildOfferResponse {

    @Value("${partner.logo.url}")
    private String partnerLogoURLHost;

    @Value("${offer.image.url}")
    private String offerImageURL;

    /**
     * Function for creating the offer object for the response
     *
     * @param offerResolverObject OfferDTO
     * @param partnerObject       PartnerObject
     * @return OfferServiceResponse
     */
    public OfferServiceResponse createOfferServiceResponseObject(OfferDTO offerResolverObject, PartnerObject partnerObject) {
        OfferServiceResponse offerServiceResponseObject = new OfferServiceResponse();

        // Data Fill from Partner
        offerServiceResponseObject.setPartnerId(partnerObject.getId());
        offerServiceResponseObject.setPartnerLabel(partnerObject.getName());
        offerServiceResponseObject.setPartnerProfileURL(partnerObject.getPageUrl());

        OfferServicePartnerLogo offerServicePartnerLogo = new OfferServicePartnerLogo();
        offerServicePartnerLogo.setUrl(partnerObject.getFullLogo().get(0).getFile().getUrl().replace("//images.ctfassets.net", partnerLogoURLHost));
        offerServiceResponseObject.setPartnerLogo(offerServicePartnerLogo);

        // Data Fill from Offer Resolver
        offerServiceResponseObject.setId(offerResolverObject.getId());

        offerServiceResponseObject.setCategoryId(offerResolverObject.getOfferCategory1());
        offerServiceResponseObject.setCategoryLabel(offerResolverObject.getOfferCategory1Label());

        offerServiceResponseObject.setSubCategoryId(offerResolverObject.getOfferCategory2());
        offerServiceResponseObject.setSubCategoryLabel(offerResolverObject.getOfferCategory2Label());

        if (offerResolverObject.getTags().size() > 0) {
            offerServiceResponseObject.setPromotionId(UUID.fromString(offerResolverObject.getTags().get(0)));
            offerServiceResponseObject.setPromotionLabel(offerResolverObject.getPromotionLabel());
        }

        offerServiceResponseObject.setAwardShort(offerResolverObject.getAwardShort());
        offerServiceResponseObject.setQualifierShort(offerResolverObject.getQualifierShort());

        Image resolverImage = offerResolverObject.getImage();
        resolverImage.setUrl(resolverImage.getUrl().replaceAll("https://s3\\.amazonaws\\.com/[^/]+/", offerImageURL));
        offerServiceResponseObject.setImage(resolverImage);

        offerServiceResponseObject.setDisplayDate(offerResolverObject.getDisplayDate());
        offerServiceResponseObject.setStartDate(offerResolverObject.getStartDate());
        offerServiceResponseObject.setEndDate(offerResolverObject.getEndDate());

        offerServiceResponseObject.setDescription(offerResolverObject.getDescription());
        offerServiceResponseObject.setMassOffer(offerResolverObject.getMassOffer());
        offerServiceResponseObject.setDisplayPriority(offerResolverObject.getDisplayPriority());

        offerServiceResponseObject.setTiers(offerResolverObject.getTiers());
        offerServiceResponseObject.setMechanisms(offerResolverObject.getMechanisms());
        offerServiceResponseObject.setLegalText(offerResolverObject.getLegalText());

        offerServiceResponseObject.setProgramType(offerResolverObject.getProgramType());

        offerServiceResponseObject.setCardType(offerResolverObject.getCardType());

        offerServiceResponseObject.setEventBasedOffer(offerResolverObject.getEventBasedOffer());
        offerServiceResponseObject.setEligibilityDuration(offerResolverObject.getEligibilityDuration());
        offerServiceResponseObject.setFirstQualificationDate(offerResolverObject.getFirstQualificationDate());
        offerServiceResponseObject.setLastQualificationDate(offerResolverObject.getLastQualificationDate());
        offerServiceResponseObject.setEligibilityDurationUnit(offerResolverObject.getEligibilityDurationUnit());

        offerServiceResponseObject.setCtaLabel(offerResolverObject.getCtaLabel());
        offerServiceResponseObject.setCtaUrl(offerResolverObject.getCtaUrl());

        return offerServiceResponseObject;
    }

    public void createOfferServiceResponseMetadata(CommonMetadata offerDeliveryMetadata, Map<UUID, PartnerObject> partnerResponse) {
        if (offerDeliveryMetadata != null && offerDeliveryMetadata.getPartners() != null) {

            offerDeliveryMetadata.getPartners().forEach(partner -> {
                // Get the partner object from map that matches the partner.id
                // get name from that object and set partner label
                try {
                    partner.setLabel(partnerResponse.get(partner.getId()).getName());
                } catch (NullPointerException e) {
                    log.error(append("partnerId", partner.getId()), "Unable to retrieve details about partner", e);
                }

            });
        }
    }

    public List<UUID> createPartnerIdList(CommonMetadata offerDeliveryMetadata) {
        //Build out list of partner id that is needed by the offer delivery metadata
        Set<UUID> partnerIds = new HashSet<UUID>();
        offerDeliveryMetadata.getPartners().forEach(partner -> {
            partnerIds.add(partner.getId());
        });

        return partnerIds.stream().toList();
    }


    public List<UUID> createPartnerIdList(List<OfferDTO> offerResolverList) {
        //Build out list of partner id that is needed by the offer delivery metadata
        Set<UUID> partnerIds = new HashSet<UUID>();

        offerResolverList.forEach(offerResolverObject -> {
            partnerIds.add(offerResolverObject.getPartnerId());
        });

        return partnerIds.stream().toList();
    }
}
