package com.loyalty.offerservice.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.loyalty.offerservice.enums.LanguageCode;
import com.loyalty.offerservice.exception.ResourceNotFoundException;
import com.loyalty.offerservice.model.dto.response.OfferServiceExtendedOfferResponse;
import com.loyalty.offerservice.model.dto.response.category.CategoriesResponse;
import com.loyalty.offerservice.model.dto.response.offerdelivery.OfferDeliveryOfferObject;
import com.loyalty.offerservice.model.dto.response.offerdelivery.OfferDeliveryResponse;
import com.loyalty.offerservice.model.dto.response.offerresolver.OfferResolverResponseDTO;
import com.loyalty.offerservice.model.dto.response.offerstate.CountDTO;
import com.loyalty.offerservice.model.dto.response.offerstate.FindCollectorOffersDTO;
import com.loyalty.offerservice.model.dto.response.offerstate.GetCollectorOffersDTO;
import com.loyalty.offerservice.model.dto.response.partner.PartnerResponse;
import com.loyalty.offerservice.service.category.CategoryServiceImpl;
import com.loyalty.offerservice.service.offerdelivery.*;
import com.loyalty.offerservice.service.offerresolver.ResolverLambdaClient;
import com.loyalty.offerservice.service.offerstate.OfferStateClient;
import com.loyalty.offerservice.service.partner.PartnerClient;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.core5.http.message.BasicHeader;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.JSONCompareMode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.ResultActions;

import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.text.Normalizer;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
public class OffersControllerTest {

    @Autowired
    ObjectMapper objectMapper;

    @Autowired
    ResourceLoader resourceLoader;

    @Autowired
    private TestRestTemplate restTemplate;

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    PartnerClient partnerClient;

    @MockBean
    OfferDeliveryInternalClient offerDeliveryInternalClient;

    @Spy
    @Autowired
    @InjectMocks
    private OfferDeliveryInternalImpl offerDeliveryInternalClientImpl;

    @MockBean
    ResolverLambdaClient resolverClient;

    @MockBean
    OfferStateClient offerStateClient;


    @Test
    void testHappyPathForPublicOffers() throws Exception {
        Resource offerDeliveryPublicOffers = resourceLoader.getResource("classpath:offerDeliveryPublicOffers.json");
        OfferDeliveryResponse offerDeliveryResponse = objectMapper.readValue(
                new InputStreamReader(offerDeliveryPublicOffers.getInputStream()),
                OfferDeliveryResponse.class);

        when(offerDeliveryInternalClient.getPublicOffers(any(), any())).thenReturn(offerDeliveryResponse);

        Resource partnersPublicOffers = resourceLoader.getResource("classpath:partnersPublicOffers.json");
        PartnerResponse partnerResponse = objectMapper.readValue(
                new InputStreamReader(partnersPublicOffers.getInputStream()),
                PartnerResponse.class);
        when(partnerClient.getPartner(any(), any())).thenReturn(partnerResponse);

        Resource expectedResponse = resourceLoader.getResource("classpath:testHappyPathForPublicOffers.json");

        MvcResult result = mockMvc.perform(get("/offers?region=BC&sort=promotionId,massOffer,-displayPriority,endDate")
                        .header("X-Timestamp", "2023-10-16T10:15:30")
                        .header("X-Correlation-Id", "e58ed763-928c-4155-bee9-fdbaaadc15f3")
                        .header("X-Origin-Client", "origin-client")
                        .header("Authorization", "e58ed763-928c-4155-bee9-fdbaaadc15f3"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andReturn();

        String expectedJson = expectedResponse.getContentAsString(StandardCharsets.UTF_8);
        String actualJson = result.getResponse().getContentAsString(StandardCharsets.UTF_8);

        JSONAssert.assertEquals(
                normalize(expectedJson),
                normalize(actualJson),
                JSONCompareMode.LENIENT
        );
    }

    @Test
    void testHappyPathForCollectorOffers() throws Exception {
        Resource offerDeliveryCollectorOffers = resourceLoader.getResource("classpath:offerDeliveryCollectorOffers.json");
        OfferDeliveryResponse offerDeliveryResponse = objectMapper.readValue(
                new InputStreamReader(offerDeliveryCollectorOffers.getInputStream()),
                OfferDeliveryResponse.class);

        when(offerDeliveryInternalClient.getCollectorOffers(any(), any())).thenReturn(offerDeliveryResponse);

        Resource partnersCollectorOffers = resourceLoader.getResource("classpath:partnersPublicOffers.json");
        PartnerResponse partnerResponse = objectMapper.readValue(
                new InputStreamReader(partnersCollectorOffers.getInputStream()),
                PartnerResponse.class);
        when(partnerClient.getPartner(any(), any())).thenReturn(partnerResponse);

        Resource offerStateOffers = resourceLoader.getResource("classpath:offerStateCollectorOffers.json");
        GetCollectorOffersDTO offerStateResponse = objectMapper.readValue(
                new InputStreamReader(offerStateOffers.getInputStream()),
                GetCollectorOffersDTO.class);
        when(offerStateClient.getOffersState(any(), any(), any())).thenReturn(offerStateResponse);

        Resource expectedResponse = resourceLoader.getResource("classpath:testHappyPathForCollectorOffers.json");

        MvcResult result = mockMvc.perform(get("/offers?region=BC")
                        .header("X-Timestamp", "2023-10-16T10:15:30")
                        .header("X-Correlation-Id", "e58ed763-928c-4155-bee9-fdbaaadc15f3")
                        .header("X-Origin-Client", "origin-client")
                        .header("Authorization", "e58ed763-928c-4155-bee9-fdbaaadc15f3")
                        .header("Collector-Number", "80000000110"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andReturn();

        String expectedJson = expectedResponse.getContentAsString(StandardCharsets.UTF_8);
        String actualJson = result.getResponse().getContentAsString(StandardCharsets.UTF_8);

        JSONAssert.assertEquals(
                normalize(expectedJson),
                normalize(actualJson),
                JSONCompareMode.LENIENT
        );
    }

    String normalize(String input) {
        return Normalizer.normalize(input, Normalizer.Form.NFKC).replaceAll("[^\\x00-\\x7F]", "");
    }

    @Test
    void testHappyPathForStateOffers() throws Exception {
        Resource partnersCollectorOffers = resourceLoader.getResource("classpath:partnersPublicOffers.json");
        PartnerResponse partnerResponse = objectMapper.readValue(
                new InputStreamReader(partnersCollectorOffers.getInputStream()),
                PartnerResponse.class);
        when(partnerClient.getPartner(any(), any())).thenReturn(partnerResponse);

        Resource offerStateOffers = resourceLoader.getResource("classpath:offerStateFindOffers.json");
        FindCollectorOffersDTO offerStateFindResponse = objectMapper.readValue(
                new InputStreamReader(offerStateOffers.getInputStream()),
                FindCollectorOffersDTO.class);
        when(offerStateClient.findOfferState(any(), any(), any(), any(), any(), any(), any())).thenReturn(offerStateFindResponse);

        Resource offerStateCount = resourceLoader.getResource("classpath:offerStateCount.json");
        CountDTO offerStateCountResponse = objectMapper.readValue(
                new InputStreamReader(offerStateCount.getInputStream()),
                CountDTO.class);
        when(offerStateClient.findOfferStateCount(any(), any(), any(), any(), any())).thenReturn(offerStateCountResponse);

        Resource expectedResponse = resourceLoader.getResource("classpath:testHappyPathForStateOffers.json");

        MvcResult result = mockMvc.perform(get("/offers?region=BC")
                        .header("X-Timestamp", "2023-10-16T10:15:30")
                        .header("X-Correlation-Id", "e58ed763-928c-4155-bee9-fdbaaadc15f3")
                        .header("X-Origin-Client", "origin-client")
                        .header("Authorization", "e58ed763-928c-4155-bee9-fdbaaadc15f3")
                        .header("Collector-Number", "80000000110")
                        .param("states", "SAVED"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andReturn();

        String expectedJson = expectedResponse.getContentAsString(StandardCharsets.UTF_8);
        String actualJson = result.getResponse().getContentAsString(StandardCharsets.UTF_8);

        JSONAssert.assertEquals(
                normalize(expectedJson),
                normalize(actualJson),
                JSONCompareMode.LENIENT
        );
    }

    @Test
    void testHappyPathForStateEmpty() throws Exception {
        Resource offerDeliveryPublicOffers = resourceLoader.getResource("classpath:offerDeliveryPublicOffers.json");
        OfferDeliveryResponse offerDeliveryResponse = objectMapper.readValue(
                new InputStreamReader(offerDeliveryPublicOffers.getInputStream()),
                OfferDeliveryResponse.class);

        when(offerDeliveryInternalClient.getPublicOffers(any(), any())).thenReturn(offerDeliveryResponse);

        Resource partnersPublicOffers = resourceLoader.getResource("classpath:partnersPublicOffers.json");
        PartnerResponse partnerResponse = objectMapper.readValue(
                new InputStreamReader(partnersPublicOffers.getInputStream()),
                PartnerResponse.class);
        when(partnerClient.getPartner(any(), any())).thenReturn(partnerResponse);

        Resource expectedResponse = resourceLoader.getResource("classpath:testHappyPathForPublicOffers.json");

        mockMvc.perform(get("/offers?region=BC")
                        .header("X-Timestamp", "2023-10-16T10:15:30")
                        .header("X-Correlation-Id", "e58ed763-928c-4155-bee9-fdbaaadc15f3")
                        .header("X-Origin-Client", "origin-client")
                        .header("Authorization", "e58ed763-928c-4155-bee9-fdbaaadc15f3")
                        .param("states", ""))
                .andExpectAll(status().is(HttpStatus.OK.value()),
                        content().contentType(MediaType.APPLICATION_JSON));
    }

    @Test
    void testHappyPathForStateTrimEmpty() throws Exception {
        Resource offerDeliveryPublicOffers = resourceLoader.getResource("classpath:offerDeliveryPublicOffers.json");
        OfferDeliveryResponse offerDeliveryResponse = objectMapper.readValue(
                new InputStreamReader(offerDeliveryPublicOffers.getInputStream()),
                OfferDeliveryResponse.class);

        when(offerDeliveryInternalClient.getPublicOffers(any(), any())).thenReturn(offerDeliveryResponse);

        Resource partnersPublicOffers = resourceLoader.getResource("classpath:partnersPublicOffers.json");
        PartnerResponse partnerResponse = objectMapper.readValue(
                new InputStreamReader(partnersPublicOffers.getInputStream()),
                PartnerResponse.class);
        when(partnerClient.getPartner(any(), any())).thenReturn(partnerResponse);

        Resource expectedResponse = resourceLoader.getResource("classpath:testHappyPathForPublicOffers.json");

        mockMvc.perform(get("/offers?region=BC")
                        .header("X-Timestamp", "2023-10-16T10:15:30")
                        .header("X-Correlation-Id", "e58ed763-928c-4155-bee9-fdbaaadc15f3")
                        .header("X-Origin-Client", "origin-client")
                        .header("Authorization", "e58ed763-928c-4155-bee9-fdbaaadc15f3")
                        .param("states", "   "))
                .andExpectAll(status().is(HttpStatus.OK.value()),
                        content().contentType(MediaType.APPLICATION_JSON));
    }

    @Test
    void testIncorrectStateForOffers() throws Exception {
        Resource offerDeliveryPublicOffers = resourceLoader.getResource("classpath:offerDeliveryPublicOffers.json");
        OfferDeliveryResponse offerDeliveryResponse = objectMapper.readValue(
                new InputStreamReader(offerDeliveryPublicOffers.getInputStream()),
                OfferDeliveryResponse.class);

        when(offerDeliveryInternalClient.getPublicOffers(any(), any())).thenReturn(offerDeliveryResponse);

        Resource partnersPublicOffers = resourceLoader.getResource("classpath:partnersPublicOffers.json");
        PartnerResponse partnerResponse = objectMapper.readValue(
                new InputStreamReader(partnersPublicOffers.getInputStream()),
                PartnerResponse.class);
        when(partnerClient.getPartner(any(), any())).thenReturn(partnerResponse);

        mockMvc.perform(get("/offers?region=BC")
                        .header("X-Timestamp", "2023-10-16T10:15:30")
                        .header("X-Correlation-Id", "e58ed763-928c-4155-bee9-fdbaaadc15f3")
                        .header("X-Origin-Client", "origin-client")
                        .header("Authorization", "e58ed763-928c-4155-bee9-fdbaaadc15f3")
                        .param("states", "saved"))
                .andExpectAll(status().is(HttpStatus.BAD_REQUEST.value()),
                        content().contentType(MediaType.APPLICATION_JSON));
    }

    @Test
    void testHappyPathWithExperiment() throws Exception {
        Resource offerDeliveryPublicOffers = resourceLoader.getResource("classpath:offerDeliveryPublicOffers.json");
        OfferDeliveryResponse offerDeliveryResponse = objectMapper.readValue(
                new InputStreamReader(offerDeliveryPublicOffers.getInputStream()),
                OfferDeliveryResponse.class);

        when(offerDeliveryInternalClient.getPublicOffers(any(), any())).thenReturn(offerDeliveryResponse);

        Resource partnersPublicOffers = resourceLoader.getResource("classpath:partnersPublicOffers.json");
        PartnerResponse partnerResponse = objectMapper.readValue(
                new InputStreamReader(partnersPublicOffers.getInputStream()),
                PartnerResponse.class);
        when(partnerClient.getPartner(any(), any())).thenReturn(partnerResponse);

        Resource expectedResponse = resourceLoader.getResource("classpath:testHappyPathForPublicOffers.json");

        MvcResult result = mockMvc.perform(get("/offers?region=BC")
                        .header("X-Timestamp", "2023-10-16T10:15:30")
                        .header("X-Correlation-Id", "e58ed763-928c-4155-bee9-fdbaaadc15f3")
                        .header("X-Origin-Client", "origin-client")
                        .header("Authorization", "e58ed763-928c-4155-bee9-fdbaaadc15f3")
                        .param("experiment", "0194baa1-29da-417f-ac59-a38872dd7dba"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andReturn();

        String expectedJson = expectedResponse.getContentAsString(StandardCharsets.UTF_8);
        String actualJson = result.getResponse().getContentAsString(StandardCharsets.UTF_8);

        JSONAssert.assertEquals(
                normalize(expectedJson),
                normalize(actualJson),
                JSONCompareMode.LENIENT
        );
    }

    // Failing
//    @Test
    void doGetCollectorNumber() throws Exception {
        Resource resourceFinal = resourceLoader.getResource("classpath:OfferResponse.json");
        Resource resourceODS = resourceLoader.getResource("classpath:offerDeliveryTest.json");
        Resource resourceResolver = resourceLoader.getResource("classpath:offerResolverTest.json");
        Resource resource = resourceLoader.getResource("classpath:partnerResponseTest.json");

        OfferDeliveryResponse offerResponse = objectMapper.readValue(
                new InputStreamReader(resourceODS.getInputStream()),
                OfferDeliveryResponse.class);

        OfferResolverResponseDTO resolverResponse = objectMapper.readValue(
                new InputStreamReader(resourceResolver.getInputStream()),
                OfferResolverResponseDTO.class);
        when(resolverClient.getOffersByUUID(any(), any(), any())).thenReturn(resolverResponse);

        PartnerResponse partnerResponse = objectMapper.readValue(
                new InputStreamReader(resource.getInputStream()),
                PartnerResponse.class);
        when(partnerClient.getPartner(any(), any())).thenReturn(partnerResponse);

        mockMvc.perform(get("/offers?region=BC")
                        .header("X-Timestamp", "2023-10-04T01:30:00.000-05:00")
                        .header("X-Correlation-Id", "e58ed763-928c-4155-bee9-fdbaaadc15f3")
                        .header("X-Origin-Client", "origin-client")
                        .header("Authorization", "e58ed763-928c-4155-bee9-fdbaaadc15f3"))
                .andExpectAll(status().is(HttpStatus.OK.value()),
                        content().contentType(MediaType.APPLICATION_JSON),
                        content().json(resourceFinal
                                .getContentAsString(StandardCharsets.UTF_8)));
    }

    @Test
    public void testHappyPathForPublicDeepLinkWithMockClients() throws IOException {

        Stream<String[]> headersTimestamp = Stream.of(new String[][]{
                {"X-Timestamp", "2023-10-04T01:30:00.000-05:00"},
                {"X-Correlation-Id", "e58ed763-928c-4155-bee9-fdbaaadc15f3"},
                {"X-Origin-Client", "origin-client"},
                {"Authorization", "e58ed763-928c-4155-bee9-fdbaaadc15f3"}
        });

        CloseableHttpClient httpClient = HttpClients.custom()
                .setDefaultHeaders(headersTimestamp.map(v -> new BasicHeader(v[0], v[1]))
                        .collect(Collectors.toList()))
                .build();
        restTemplate.getRestTemplate()
                .setRequestFactory(new HttpComponentsClientHttpRequestFactory(httpClient));

        Resource resourceODS = resourceLoader.getResource("classpath:offerDeliveryPublicDeepLinkTest.json");
        OfferDeliveryOfferObject offerResponse = objectMapper.readValue(
                new InputStreamReader(resourceODS.getInputStream()),
                OfferDeliveryOfferObject.class);

        Resource resourceResolver = resourceLoader.getResource("classpath:offerResolverTest.json");
        OfferResolverResponseDTO resolverResponse = objectMapper.readValue(
                new InputStreamReader(resourceResolver.getInputStream()),
                OfferResolverResponseDTO.class);
        when(resolverClient.getOffersByUUID(any(), any(), any())).thenReturn(resolverResponse);

        Resource resource = resourceLoader.getResource("classpath:partnerResponseTest.json");
        PartnerResponse partnerResponse = objectMapper.readValue(
                new InputStreamReader(resource.getInputStream()),
                PartnerResponse.class);
        when(partnerClient.getPartner(any(), any())).thenReturn(partnerResponse);

        ResponseEntity<String> result = restTemplate
                .getForEntity("/offers/133b7d2c-9223-446e-a594-2c654b23fb50?region=BC", String.class);

        // TODO activate after fixing Jackson issue parsing date fields
        // Assert.assertEquals(result.getBody(),
        // "{\"offers\":[{\"id\":\"71d603c0-2b0e-4a54-b4c4-97df0436c165\",\"partnerId\":\"ffdfc361-38c9-44e6-bc9c-afb097a3b76d\",\"categoryId\":\"22b666ae-97c2-4e57-a437-e977de6beef4\",\"subCategoryId\":\"4683cd1f-bba8-4dd0-b960-4324a246960b\",\"promotionId\":null,\"massOffer\":true,\"active\":true,\"region\":[\"NT\",\"PE\",\"TB\",\"NB\",\"SK\",\"NU\",\"QC\",\"AB\",\"NS\",\"ON\",\"NL\",\"BC\",\"YT\",\"MB\"],\"priority\":{\"display\":0},\"type\":\"spend\",\"awardType\":\"flatDiscount\",\"qualifier\":\"product\",\"displayDate\":\"2023-08-15T00:00:00\",\"startDate\":\"2023-08-15T00:00:00\",\"endDate\":\"2023-08-15T23:59:59\",\"baseCashRedemption\":95,\"image\":{\"path\":\"https://s3.amazonaws.com/dev-l1-amrpwl-post-images/processed-images/9102032e-5b3b-494b-b2b4-97d013d91c23\"},\"qualifierShort\":\"Buy
        // 50 Water Bottles in-store*\",\"awardShort\":\"Save
        // $5\",\"tiers\":[{\"awardValue\":5.0,\"qualifierValue\":50.0,\"awardLong\":\"Save
        // $5\",\"qualifierLong\":\"Buy 50 Water Bottles
        // in-store*\"}],\"mechanisms\":[{\"mechanismType\":\"button\",\"mechanismLabel\":\"ABC\",\"mechanismValue\":\"https://xyz\"}],\"legalText\":\"*
        // Offer valid on August 15, 2023. Valid at participating BMO Bank of Montreal
        // locations in Alberta, British Columbia, Manitoba, New Brunswick, Newfoundland
        // and Labrador, Northwest Territories, Nova Scotia, Nunavut, Ontario, Prince
        // Edward Island, Quebec, Saskatchewan, Thunder Bay and Yukon. Minimum eligible
        // purchase must be spent in a single transaction. While supplies last. Product
        // availability may vary by store. We reserve the right to limit quantities. AIR
        // MILES Card must be presented at the time of the purchase. Can be combined
        // with other offers, and AIR MILES offers. ®™ Trademarks of AM Royalties
        // Limited Partnership used under license by AIR MILES Loyalty Inc. Partner,
        // Supplier and Retailer trademarks are owned by the respective Partner,
        // Supplier or Retailer or authorized for their use in
        // Canada.\"}],\"metadata\":null}");
    }

    @Test
    public void testHappyPathForCollectorDeepLinkWithMockClients() throws IOException {

        Stream<String[]> headersTimestamp = Stream.of(new String[][]{
                {"X-Timestamp", "2023-10-04T01:30:00.000-05:00"},
                {"X-Correlation-Id", "e58ed763-928c-4155-bee9-fdbaaadc15f3"},
                {"X-Origin-Client", "origin-client"},
                {"Authorization", "e58ed763-928c-4155-bee9-fdbaaadc15f3"},
                {"Collector-Number", "84151103400"}
        });

        CloseableHttpClient httpClient = HttpClients.custom()
                .setDefaultHeaders(headersTimestamp.map(v -> new BasicHeader(v[0], v[1]))
                        .collect(Collectors.toList()))
                .build();
        restTemplate.getRestTemplate()
                .setRequestFactory(new HttpComponentsClientHttpRequestFactory(httpClient));

        Resource resourceODS = resourceLoader.getResource("classpath:offerDeliveryCollectorDeepLinkTest.json");
        OfferDeliveryOfferObject offerResponse = objectMapper.readValue(
                new InputStreamReader(resourceODS.getInputStream()),
                OfferDeliveryOfferObject.class);

        Resource resourceResolver = resourceLoader.getResource("classpath:offerResolverTest.json");
        OfferResolverResponseDTO resolverResponse = objectMapper.readValue(
                new InputStreamReader(resourceResolver.getInputStream()),
                OfferResolverResponseDTO.class);
        when(resolverClient.getOffersByUUID(any(), any(), any())).thenReturn(resolverResponse);

        Resource resource = resourceLoader.getResource("classpath:partnerResponseTest.json");
        PartnerResponse partnerResponse = objectMapper.readValue(
                new InputStreamReader(resource.getInputStream()),
                PartnerResponse.class);
        when(partnerClient.getPartner(any(), any())).thenReturn(partnerResponse);

        ResponseEntity<String> result = restTemplate
                .getForEntity("/offers/133b7d2c-9223-446e-a594-2c654b23fb50?region=BC", String.class);

        // TODO activate after fixing Jackson issue parsing date fields
        // Assert.assertEquals(result.getBody(),
        // "{\"offers\":[{\"id\":\"71d603c0-2b0e-4a54-b4c4-97df0436c165\",\"partnerId\":\"ffdfc361-38c9-44e6-bc9c-afb097a3b76d\",\"categoryId\":\"22b666ae-97c2-4e57-a437-e977de6beef4\",\"subCategoryId\":\"4683cd1f-bba8-4dd0-b960-4324a246960b\",\"promotionId\":null,\"massOffer\":true,\"active\":true,\"region\":[\"NT\",\"PE\",\"TB\",\"NB\",\"SK\",\"NU\",\"QC\",\"AB\",\"NS\",\"ON\",\"NL\",\"BC\",\"YT\",\"MB\"],\"priority\":{\"display\":0},\"type\":\"spend\",\"awardType\":\"flatDiscount\",\"qualifier\":\"product\",\"displayDate\":\"2023-08-15T00:00:00\",\"startDate\":\"2023-08-15T00:00:00\",\"endDate\":\"2023-08-15T23:59:59\",\"baseCashRedemption\":95,\"image\":{\"path\":\"https://s3.amazonaws.com/dev-l1-amrpwl-post-images/processed-images/9102032e-5b3b-494b-b2b4-97d013d91c23\"},\"qualifierShort\":\"Buy
        // 50 Water Bottles in-store*\",\"awardShort\":\"Save
        // $5\",\"tiers\":[{\"awardValue\":5.0,\"qualifierValue\":50.0,\"awardLong\":\"Save
        // $5\",\"qualifierLong\":\"Buy 50 Water Bottles
        // in-store*\"}],\"mechanisms\":[{\"mechanismType\":\"button\",\"mechanismLabel\":\"ABC\",\"mechanismValue\":\"https://xyz\"}],\"legalText\":\"*
        // Offer valid on August 15, 2023. Valid at participating BMO Bank of Montreal
        // locations in Alberta, British Columbia, Manitoba, New Brunswick, Newfoundland
        // and Labrador, Northwest Territories, Nova Scotia, Nunavut, Ontario, Prince
        // Edward Island, Quebec, Saskatchewan, Thunder Bay and Yukon. Minimum eligible
        // purchase must be spent in a single transaction. While supplies last. Product
        // availability may vary by store. We reserve the right to limit quantities. AIR
        // MILES Card must be presented at the time of the purchase. Can be combined
        // with other offers, and AIR MILES offers. ®™ Trademarks of AM Royalties
        // Limited Partnership used under license by AIR MILES Loyalty Inc. Partner,
        // Supplier and Retailer trademarks are owned by the respective Partner,
        // Supplier or Retailer or authorized for their use in
        // Canada.\"}],\"metadata\":null}");
    }

    @Test
    void doODSErrorCheck() throws Exception {
        Resource resource = resourceLoader.getResource("classpath:partnerResponseTest.json");

        PartnerResponse partnerResponse = objectMapper.readValue(
                new InputStreamReader(resource.getInputStream()),
                PartnerResponse.class);
        when(partnerClient.getPartner(any(), any())).thenReturn(partnerResponse);

        when(offerDeliveryInternalClient.getPublicOfferByOfferId(any(), any(), any())).thenThrow(new ResourceNotFoundException(
                "err-not-found", "Offer Id does not exist, Offer Id not valid, Offer is not active"));

        mockMvc.perform(get("/offers/e408f393-fbdd-4847-8fd2-f0850871695a?region=ON")
                        .header("X-Timestamp", "2023-10-16T10:15:30")
                        .header("X-Correlation-Id", "e58ed763-928c-4155-bee9-fdbaaadc15f3")
                        .header("X-Origin-Client", "origin-client")
                        .header("Authorization", "e58ed763-928c-4155-bee9-fdbaaadc15f3"))
                .andExpectAll(status().is(HttpStatus.NOT_FOUND.value()),
                        content().contentType(MediaType.APPLICATION_JSON),
                        content().json(
                                "{\"code\": \"err-not-found\", \"message\": \"Offer Id does not exist, Offer Id not valid, Offer is not active\"}"));
    }

    @Test
    void doLanguageCodeErrorCheck() throws Exception {
        Resource resource = resourceLoader.getResource("classpath:partnerResponseTest.json");

        PartnerResponse partnerResponse = objectMapper.readValue(
                new InputStreamReader(resource.getInputStream()),
                PartnerResponse.class);
        when(partnerClient.getPartner(any(), any())).thenReturn(partnerResponse);

        when(offerDeliveryInternalClient.getPublicOfferByOfferId(any(), any(), any())).thenThrow(new ResourceNotFoundException(
                "err-not-found", "Offer Id does not exist, Offer Id not valid, Offer is not active"));

        try (MockedStatic<LanguageCode> mockedStatic = mockStatic(LanguageCode.class, Mockito.CALLS_REAL_METHODS)) {
            mockedStatic.when(() -> LanguageCode.fromValue(anyString())).thenThrow(new RuntimeException());

            mockMvc.perform(get("/offers/e408f393-fbdd-4847-8fd2-f0850871695a?region=ON")
                            .header("X-Timestamp", "2023-10-16T10:15:30")
                            .header("X-Correlation-Id", "e58ed763-928c-4155-bee9-fdbaaadc15f3")
                            .header("X-Origin-Client", "origin-client")
                            .header("Authorization", "e58ed763-928c-4155-bee9-fdbaaadc15f3"))
                    .andExpectAll(status().is(HttpStatus.NOT_FOUND.value()),
                            content().contentType(MediaType.APPLICATION_JSON),
                            content().json(
                                    "{\"code\": \"err-not-found\", \"message\": \"Offer Id does not exist, Offer Id not valid, Offer is not active\"}"));
        }
    }

    @Test
    public void testEmptyHeaderExceptionForCollectorNumberHeaderNull() throws Exception {
        mockMvc.perform(get("/offers?region=BC")
                        .header("X-Timestamp", "2023-10-16T10:15:30")
                        .header("X-Correlation-Id", "e58ed763-928c-4155-bee9-fdbaaadc15f3")
                        .header("X-Origin-Client", "origin-client")
                        .header("Authorization", "e58ed763-928c-4155-bee9-fdbaaadc15f3")
                        .param("states", "SAVED"))
                .andExpectAll(status().is(HttpStatus.BAD_REQUEST.value()),
                        content().contentType(MediaType.APPLICATION_JSON),
                        content().json(
                                "{\"code\": \"BAD_REQUEST\", \"message\": \"Could not determine 'Collector-Number' from the request.\"}"));
    }

    @Test
    public void testEmptyHeaderExceptionForCollectorNumberHeaderEmpty() throws Exception {
        mockMvc.perform(get("/offers?region=BC")
                        .header("X-Timestamp", "2023-10-16T10:15:30")
                        .header("X-Correlation-Id", "e58ed763-928c-4155-bee9-fdbaaadc15f3")
                        .header("X-Origin-Client", "origin-client")
                        .header("Authorization", "e58ed763-928c-4155-bee9-fdbaaadc15f3")
                        .header("Collector-Number", "")
                        .param("states", "SAVED"))
                .andExpectAll(status().is(HttpStatus.BAD_REQUEST.value()),
                        content().contentType(MediaType.APPLICATION_JSON),
                        content().json(
                                "{\"code\": \"BAD_REQUEST\", \"message\": \"Could not determine 'Collector-Number' from the request.\"}"));
    }

    @Test
    public void testEmptyHeaderExceptionForCollectorNumberHeaderTrimEmpty() throws Exception {
        mockMvc.perform(get("/offers?region=BC")
                        .header("X-Timestamp", "2023-10-16T10:15:30")
                        .header("X-Correlation-Id", "e58ed763-928c-4155-bee9-fdbaaadc15f3")
                        .header("X-Origin-Client", "origin-client")
                        .header("Authorization", "e58ed763-928c-4155-bee9-fdbaaadc15f3")
                        .header("Collector-Number", "   ")
                        .param("states", "SAVED"))
                .andExpectAll(status().is(HttpStatus.BAD_REQUEST.value()),
                        content().contentType(MediaType.APPLICATION_JSON),
                        content().json(
                                "{\"code\": \"BAD_REQUEST\", \"message\": \"Could not determine 'Collector-Number' from the request.\"}"));
    }

    //Fail
//    @Test
    void doODSErrorWithOfferCheck() throws Exception {
        Resource resource = resourceLoader.getResource("classpath:partnerResponseTest.json");
        Resource expectedResult = resourceLoader.getResource("classpath:OfferResponseByID.json");

        PartnerResponse partnerResponse = objectMapper.readValue(
                new InputStreamReader(resource.getInputStream()),
                PartnerResponse.class);
        when(partnerClient.getPartner(any(), any())).thenReturn(partnerResponse);

        when(offerDeliveryInternalClient.getPublicOfferByOfferId(any(), any(), any()))
                .thenThrow(new ResourceNotFoundException("err-region",
                        "Offer Id does not belong to user's region"));

        Resource resourceResolver = resourceLoader.getResource("classpath:offerResolverTest.json");
        OfferResolverResponseDTO resolverResponse = objectMapper.readValue(
                new InputStreamReader(resourceResolver.getInputStream()),
                OfferResolverResponseDTO.class);
        when(resolverClient.getOffersByUUID(any(), any(), any())).thenReturn(resolverResponse);

        ResultActions result = mockMvc.perform(get("/offers/e408f393-fbdd-4847-8fd2-f0850871695a?region=ON")
                        .header("X-Timestamp", "2023-10-16T10:15:30")
                        .header("X-Correlation-Id", "e58ed763-928c-4155-bee9-fdbaaadc15f3")
                        .header("X-Origin-Client", "origin-client")
                        .header("Authorization", "e58ed763-928c-4155-bee9-fdbaaadc15f3"))
                .andExpectAll(status().is(HttpStatus.OK.value()),
                        content().contentType(MediaType.APPLICATION_JSON),
                        content().json(expectedResult
                                .getContentAsString(StandardCharsets.UTF_8)));

    }

    //Fail
//    @Test
    void testGetOffersByOfferStateHappyPath() throws Exception {
        Resource resourceFinal = resourceLoader.getResource("classpath:OfferResponse.json");
        Resource offerState = resourceLoader.getResource("classpath:OfferStateFindTest.json");
        Resource resource = resourceLoader.getResource("classpath:partnerResponseTest.json");
        Resource offerStateCount = resourceLoader.getResource("classpath:OfferStateCountTest.json");

        FindCollectorOffersDTO findCollectorOffersDTO = objectMapper.readValue(
                new InputStreamReader(offerState.getInputStream()),
                FindCollectorOffersDTO.class);

        when(offerStateClient.findOfferState(any(), any(), any(), any(), any(), any(), any()))
                .thenReturn(findCollectorOffersDTO);

        CountDTO countDTO = objectMapper.readValue(new InputStreamReader(offerStateCount.getInputStream()),
                CountDTO.class);
        when(offerStateClient.findOfferStateCount(any(), any(), any(), any(), any())).thenReturn(countDTO);

        PartnerResponse partnerResponse = objectMapper.readValue(
                new InputStreamReader(resource.getInputStream()),
                PartnerResponse.class);
        when(partnerClient.getPartner(any(), any())).thenReturn(partnerResponse);

        mockMvc.perform(get("/offers?region=BC&states=SAVED&limit=0&offset=0")
                        .header("X-Timestamp", "2023-10-04T01:30:00.000-05:00")
                        .header("X-Correlation-Id", "e58ed763-928c-4155-bee9-fdbaaadc15f3")
                        .header("X-Origin-Client", "origin-client")
                        .header("Authorization", "e58ed763-928c-4155-bee9-fdbaaadc15f3"))
                .andExpectAll(status().is(HttpStatus.OK.value()),
                        content().contentType(MediaType.APPLICATION_JSON),
                        content().json(resourceFinal
                                .getContentAsString(StandardCharsets.UTF_8)));
    }

    @MockBean
    CategoryServiceImpl categoryService;

    //     @Test
    void publicOffers_extendedMetadata_getDetailedMetadata() throws Exception {
        Resource resourceFinal = resourceLoader.getResource("classpath:OfferResponse.json");
        Resource deliveryResource = resourceLoader.getResource("classpath:offerDeliveryTest.json");
        Resource partnerResource = resourceLoader.getResource("classpath:partnerResponseTest.json");
        Resource categoryResource = resourceLoader.getResource("classpath:categoryResponseTest.json");

        OfferDeliveryResponse offerResponse = objectMapper.readValue(
                new InputStreamReader(deliveryResource.getInputStream()),
                OfferDeliveryResponse.class);
        when(offerDeliveryInternalClient.getPublicOffers(any(), any())).thenReturn(offerResponse);

        PartnerResponse partnerResponse = objectMapper.readValue(
                new InputStreamReader(partnerResource.getInputStream()),
                PartnerResponse.class);
        when(partnerClient.getPartner(any(), any())).thenReturn(partnerResponse);


        CategoriesResponse categoriesResponse = objectMapper.readValue(
                new InputStreamReader(categoryResource.getInputStream()),
                CategoriesResponse.class
        );
        when(categoryService.getCategories(any())).thenReturn(categoriesResponse);

        MvcResult mvcResult = mockMvc.perform(get("/offers?region=ON")
                        .queryParam("extended_metadata", "true")
                        .header("X-Timestamp", "2023-10-04T01:30:00.000-05:00")
                        .header("X-Correlation-Id", "e58ed763-928c-4155-bee9-fdbaaadc15f3")
                        .header("X-Origin-Client", "origin-client"))
                .andExpectAll(status().is(HttpStatus.OK.value()))
                .andReturn();

        OfferServiceExtendedOfferResponse response = objectMapper.readValue(
                mvcResult.getResponse().getContentAsString(), OfferServiceExtendedOfferResponse.class);

        List<String> expectedLabels = List.of("Retail", "Personal Care");

        // Category verification
        // SubCategory verification
        response.getMetadata().getCategories().stream().flatMap(this::categoryFlatMapHelper).forEach(
                data -> {
                    assertEquals(1, data.getCount());
                    assertTrue(expectedLabels.contains(data.getLabel()));
                });

    }

    /**
     * This helper method takes the category subcategory tree and flattens it out
     * with only the objects that
     * have a count of more than 0
     *
     * @param data - Category.class
     * @return Stream of Category objects
     */
    Stream<CategoriesResponse.Category> categoryFlatMapHelper(CategoriesResponse.Category data) {
        if (data.getCount() > 0) {
            return Stream.of(data);
        }

        if (data.getSubCategories() != null) {
            return data.getSubCategories().stream().flatMap(this::categoryFlatMapHelper);
        }
        return null;
    }
}