package com.loyalty.offerservice.model.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.loyalty.offerservice.model.dto.response.offerresolver.*;
import com.loyalty.offerservice.model.dto.response.offerresolver.Image;
import com.loyalty.offerservice.model.dto.response.offerresolver.Tier;
import lombok.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
@ToString

@JsonInclude(JsonInclude.Include.NON_NULL)
public class OfferServiceResponse {

    private UUID id;

    private UUID partnerId;

    private String partnerLabel;

    private OfferServicePartnerLogo partnerLogo;

    private String partnerProfileURL;

    private UUID categoryId;

    private String categoryLabel;

    private UUID subCategoryId;

    private String subCategoryLabel;

    private UUID promotionId;

    private String promotionLabel;

    private String awardShort;

    private String qualifierShort;

    private Image image;

    private LocalDateTime displayDate;

    private LocalDateTime startDate;

    private LocalDateTime endDate;

    private String description;

    private String programType;

    private Boolean massOffer;

    private Integer displayPriority;

    private List<Tier> tiers;

    private List<Mechanism> mechanisms;

    private List<Map<String,Object>> states;

    private String legalText;

    private List<CardType> cardType;

    private Boolean eventBasedOffer;

    private Integer eligibilityDuration;

    private LocalDateTime firstQualificationDate;

    private LocalDateTime lastQualificationDate;

    private EligibilityDurationUnit eligibilityDurationUnit;

    private Map<String, String> ctaLabel;

    private Map<String, String> ctaUrl;
}
