package com.loyalty.offerservice.service.offerstate;

import com.loyalty.offerservice.model.dto.request.OfferRequest;
import com.loyalty.offerservice.model.dto.request.OfferStateUpdateRequest;
import com.loyalty.offerservice.model.dto.response.offerstate.CountDTO;
import com.loyalty.offerservice.model.dto.request.OfferStates;
import com.loyalty.offerservice.model.dto.request.OfferStatesServiceRequest;
import com.loyalty.offerservice.model.dto.response.offerstate.FindCollectorOffersDTO;
import com.loyalty.offerservice.model.dto.response.offerstate.GetCollectorOffersDTO;

import java.io.IOException;
import java.util.List;
import java.util.UUID;

public interface OfferStateService {
    /**
     * Call offer state service for getting state of the offers
     *
     * @return
     * @throws IOException
     */
    GetCollectorOffersDTO getOfferState(OfferRequest request, Long collectorID, List<UUID> offerId) throws IOException;

    void updateOfferState(OfferStates request, OfferStateUpdateRequest offerStateUpdateRequest);

    /**
     * Call offer state service for finding offers of the state
     *
     * @return
     * @throws IOException
     */

    FindCollectorOffersDTO findOfferState(OfferRequest request, Long collectorId) throws IOException;

    /**
     *
     * Call offer state service for finding the count of state of the offers
     * @param
     * @param
     * @return
     * @throws IOException
     */
    CountDTO findOfferStateCount(OfferRequest request, Long collectorId) throws IOException;
}
