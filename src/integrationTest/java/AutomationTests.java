import static org.junit.jupiter.api.Assertions.assertEquals;

import net.masterthought.cucumber.ReportBuilder;
import org.apache.commons.io.FileUtils;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import com.intuit.karate.KarateOptions;
import com.intuit.karate.Results;
import com.intuit.karate.Runner;
import java.io.File;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import net.masterthought.cucumber.Configuration;
import com.intuit.karate.junit5.Karate;

public class AutomationTests {

    @Test
    @DisplayName("Automated TCs for Offer Services")
    void testOfferService(){
        System.out.println("*** TESTING ****");
        Results results = Runner.path("classpath:features/OfferService.feature")
                .tags("@unauthenticated")
                .karateEnv("dev")
                .outputCucumberJson(true)
                .parallel(1);

        System.out.println("*** RESULTADOS ***" + results.getFailCount() + "*******" + results.getErrorMessages());
        generateReport(results.getReportDir());
        assertEquals(0,results.getFailCount(),results.getErrorMessages());
    }

    public static void generateReport(String karateOutputPath) {
        Collection<File> jsonFiles = FileUtils.listFiles(new File(karateOutputPath), new String[] {"json"}, true);
        List<String> jsonPaths = new ArrayList<>(jsonFiles.size());
        jsonFiles.forEach(file -> jsonPaths.add(file.getAbsolutePath()));
        Configuration config = new Configuration(new File("target"), "demo");
        ReportBuilder reportBuilder = new ReportBuilder(jsonPaths, config);
        reportBuilder.generateReports();
    }

}
