package com.loyalty.offerservice.model.dto.request;

import com.loyalty.offerservice.enums.ProgramTypeEnum;
import com.loyalty.offerservice.enums.RegionEnum;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.UUID;

@Data
@Builder
public class OfferDeliveryQueryParams {
    private RegionEnum region;
    private List<UUID> partnerId;
    private List<UUID> categoryId;
    private List<UUID> subCategoryId;
    private UUID promotionId;
    private List<String> type;
    private List<ProgramTypeEnum> programType;
    private Integer limit;
    private Integer offset;
    private List<String> sort;
    private Boolean massOffer;
    private Boolean eventBased;
    private UUID experiment;
    private List<UUID> excludePartnerId;
}

