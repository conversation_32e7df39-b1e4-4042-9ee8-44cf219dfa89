@Library('jenkins-shared-lib-v2') _

def repoName = params.repoName
def releaseVersion = params.releaseVersion

pipeline {

  agent none

  //if manually trigger pipeline, need to select release version to deploy
  parameters {
    gitParameter  branch: '', \
                branchFilter: '.*', \
                defaultValue: '1.0.0', \
                name: 'releaseVersion', \
                quickFilterEnabled: true, \
                selectedValue: 'NONE', \
                sortMode: 'DESCENDING', \
                tagFilter: '*.*', \
                type: 'PT_TAG'
  }

  stages {
    stage('Print Release Info') {
      agent { label 'aws-ec2' }
      steps {
        script {
          appMeta = readProperties(file: 'meta.config')
          println "Release Version is " + releaseVersion
          println "Repo is " + repoName
        }
      }
    }
      
    stage('Create CR') {
      steps {
        script {
          println "Create CR..  "
          input(
          message: 'Has CR been approved?', 
          submitter: "OS-AWS-JenkinsDeveloper",
          ok: 'CR is approved!')
        }
      }
    }
    
//    stage('deploy to cert') {
//      agent { label 'aws-ec2' }
//      when {
//        expression { appMeta.ENV_CERT == "true" }
//      }
//      steps {
//        script {
//          println "Deploy to Cert... "
//
//          def envType = 'cert'
//          //#### Please update the resources.yaml before uncommenting this code for the infrastructure to be deployed.
//          // println "Deploying infrastructure"
//          // awsUtils.deployInfra(
//          //   "${envType}-${repoName}-infra",
//          //   envType,
//          //   "file://aws/cfn/infrastructure/resources.yaml",
//          //   "file://env/${envType}-infra.params.json",
//          //   "file://env/${envType}-infra.tags.json",
//          //   releaseVersion,
//          //   "")
//          println "Deploying ECS Service Application"
//          awsUtils.deployAndTestECS(envType, releaseVersion, "echo cert test")
//
//          // close cert implementation and test tasks
//        }
//      }
//      post {
//        cleanup {
//          deleteDir()
//          dir("${workspace}@tmp") {
//            deleteDir()
//          }
//          dir("${workspace}@script") {
//            deleteDir()
//          }
//        }
//      }
//    }
    
    stage('deploy to prod') {
      agent { label 'aws-ec2' }
      steps {
        script {
          println "Deploy to Prod... "

          def envType = 'prod'
          //#### Please update the resources.yaml before uncommenting this code for the infrastructure to be deployed.          
          // println "Deploying infrastructure"
          // awsUtils.deployInfra(
          //   "${envType}-${repoName}-infra",
          //   envType,
          //   "file://aws/cfn/infrastructure/resources.yaml",
          //   "file://env/${envType}-infra.params.json",
          //   "file://env/${envType}-infra.tags.json",
          //   releaseVersion,
          //   "")
          println "Deploying ECS Service Application"
          awsUtils.deployAndTestECS(envType, releaseVersion, "echo prod test")

          // close prod implementation and test tasks
        }
      }	 
      post {
        cleanup {
          deleteDir()
          dir("${workspace}@tmp") {
            deleteDir()
          }
          dir("${workspace}@script") {
            deleteDir()
          }
        }    
      }   
    }       
  }
}
