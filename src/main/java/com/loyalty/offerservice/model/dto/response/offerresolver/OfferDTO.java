package com.loyalty.offerservice.model.dto.response.offerresolver;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.loyalty.offerservice.cache.Cacheable;
import com.loyalty.offerservice.enums.RegionEnum;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OfferDTO extends Cacheable {

    private UUID id;

    private String offerCode;

    private String sysOfferId;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime displayDate;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime startDate;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime endDate;

    private Integer displayPriority;

    private Integer programPriority;

    private UUID partnerId;

    private String partnerName;

    private Integer partnerBaseEarnRate;

    private Integer baseCashRedemption;

    private OfferType offerType;

    private Qualifier qualifier;

    private AwardType awardType;

    private String awardShort;

    private String qualifierShort;

    private String legalText;

    private Image image;

    private List<RegionEnum> regions;

    private String cashierInstruction;

    private List<Tier> tiers;

    private List<Mechanism> mechanisms;

    private String description;

    private List<Availability> availability;

    private List<String> tags;

    private Boolean massOffer;

    private UUID offerCategory1;

    private UUID offerCategory2;

    private String offerCategory3;

    private String productName;

    private String productBrand;

    private Boolean active;

    private String offerCategory1Label;

    private String offerCategory2Label;

    private String offerCategory3Label;

    private String promotionLabel;

    private String programType;

    private List<CardType> cardType;

    private Boolean eventBasedOffer;

    private Integer eligibilityDuration;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime firstQualificationDate;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime lastQualificationDate;

    private EligibilityDurationUnit eligibilityDurationUnit;

    private Map<String, String> ctaLabel;

    private Map<String, String> ctaUrl;
}