Feature: Auth Token Test

  @Authtokenflow
  Scenario: Offer  service endpoint -Token generation for an authenticated user.
    * def AWSConnection = Java.type('aws.SecretManagerConnection');
    * def clientId = new AWSConnection().getAWSSecretClientid();
    * def clientSecret = new AWSConnection().getAWSSecretClientSecret();
    * def token_details =
"""
  {
 "grant_type":"password",
 "client_id":"#(clientId)",
 "audience":"https://members.loyalty.com",
 "username":84151103400,
 "password":"1111",
 "client_secret":"#(clientSecret)"
  }
   """
    Given url token_url
    And form fields token_details
    When method POST
    Then status 200
    * print response
