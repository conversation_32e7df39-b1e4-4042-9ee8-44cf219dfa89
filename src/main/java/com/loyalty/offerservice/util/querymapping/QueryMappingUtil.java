package com.loyalty.offerservice.util.querymapping;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.loyalty.offerservice.model.dto.request.FilterRequest;
import com.loyalty.offerservice.model.dto.request.PaginationRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStreamReader;

@Component
public class QueryMappingUtil {

    @Autowired
    ResourceLoader resourceLoader;

    @Autowired
    ObjectMapper objectMapper;

    public FilterRequest getFeedFilterMapping(String originClient) throws IOException {
        String mappingFileLocation = QueryMappingConfig.getMappingLocation(originClient);

        FilterRequest feedFilterRequest = null;

        if (mappingFileLocation != null) {
            Resource resource = resourceLoader.getResource(mappingFileLocation);
            JsonNode rootNode = objectMapper.readTree(new InputStreamReader(resource.getInputStream()));

            // Extract the filter request from the mapping file
            JsonNode filterRequestNode = rootNode.get("filterRequest");
            if (filterRequestNode != null) {
                feedFilterRequest = objectMapper.treeToValue(filterRequestNode, FilterRequest.class);
            }
        }

        return feedFilterRequest;
    }

    public PaginationRequest getFeedPaginationMapping(String originClient) throws IOException {
        String mappingFileLocation = QueryMappingConfig.getMappingLocation(originClient);

        PaginationRequest feedPaginationRequest = null;

        if (mappingFileLocation != null) {
            Resource resource = resourceLoader.getResource(mappingFileLocation);
            JsonNode rootNode = objectMapper.readTree(new InputStreamReader(resource.getInputStream()));

            // Extract the pagination request from the mapping file
            JsonNode paginationRequestNode = rootNode.get("paginationRequest");
            if (paginationRequestNode != null) {
                feedPaginationRequest = objectMapper.treeToValue(paginationRequestNode, PaginationRequest.class);
            }
        }

        return feedPaginationRequest;
    }
}
