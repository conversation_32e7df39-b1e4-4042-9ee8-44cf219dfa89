package com.loyalty.offerservice.service.async;

import com.loyalty.offerservice.model.dto.request.OfferRequest;
import com.loyalty.offerservice.model.dto.response.offerresolver.OfferResolverResponseDTO;
import com.loyalty.offerservice.service.offerresolver.OfferResolverService;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

@Component
public class OfferResolver {

    @Autowired
    OfferResolverService offerResolverService;

    @Async
    public CompletableFuture<OfferResolverResponseDTO> retrieveProcessAsync(List<UUID> offerIds, OfferRequest request,
            Map<String, String> context) throws IOException {
                
        if (context != null) {
            MDC.setContextMap(context);
        }
        return CompletableFuture.completedFuture(retrieveProcess(offerIds, request));
    }

    public OfferResolverResponseDTO retrieveProcess(List<UUID> offerIds, OfferRequest request) throws IOException {
        return offerResolverService.getOffer(offerIds, request);
    }
}
