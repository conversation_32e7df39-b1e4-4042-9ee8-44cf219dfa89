package com.loyalty.offerservice.model.dto.response.offerresolver;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;


public enum CardType {

    @JsonProperty("NonBmoMastercard")
    NonBmoMastercard("NonBmoMastercard"),

    @<PERSON>sonProperty("BmoMastercard")
    BmoMastercard("BmoMastercard"),

    @JsonProperty("BmoDebit")
    BmoDebit("BmoDebit");

    private final String value;

    //Constructor that associates the value with each constant
    CardType(String value) {
        this.value = value;
    }

    //Method to get a value
    public String getValue() {
        return value;
    }
}