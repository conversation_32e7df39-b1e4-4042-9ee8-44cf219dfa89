sonar.projectName=offer-service
sonar.projectKey=offer-service
sonar.junit.reportPaths=build/test-results/test
sonar.dynamicAnalysis=reuseReports
sonar.tests=src/test
sonar.sources=src/main
sonar.java.binaries=build/classes
sonar.java.libraries=build/libs/app.jar
sonar.java.test.libraries=build/libs/app.jar
sonar.language=java
sonar.java.coveragePlugin=jacoco
sonar.exclusions=src/main/java/com/loyalty/offerservice/Application.java,\
  src/main/java/com/loyalty/offerservice/util/OfferServiceRequestInterceptor.java,\
  src/main/java/com/loyalty/offerservice/config/**/*,\
  src/main/java/com/loyalty/offerservice/service/async/**/*,\
  src/main/java/com/loyalty/offerservice/model/dto/**/*,\
  src/main/java/com/loyalty/offerservice/service/offerresolver/ResolverLambdaClient.java,\
  src/main/java/com/loyalty/offerservice/exception/model/GenericErrorResponse.java,\
  src/main/java/com/loyalty/offerservice/service/offerdelivery/ODSError.java,\
  src/main/java/com/loyalty/offerservice/service/offerdelivery/OfferDeliveryQueryParams.java