package com.loyalty.offerservice.service.partner;

import com.loyalty.offerservice.config.FeignLoggingConfiguration;
import com.loyalty.offerservice.model.dto.response.partner.PartnerResponse;
import feign.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Bean;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.UUID;

@FeignClient(name = "PartnerAPIClient", url = "${partner-service.base}", configuration = FeignLoggingConfiguration.class)
public interface PartnerClient {

    @GetMapping(
            value = "partners/v1/partners/{ids}",
            consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE
    )
    PartnerResponse getPartner(@PathVariable List<UUID> ids,
                               @RequestHeader HttpHeaders headers);

    @GetMapping(
            value = "partners/v1/partners",
            consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE
    )
    PartnerResponse getPartners(@RequestHeader HttpHeaders headers, @RequestParam int offset,  @RequestParam String type);

}
