package com.loyalty.offerservice.model.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CommonMetadata {

    private Integer total;
    private List<MetadataCounts> partners;
    private List<MetadataCounts> categories;
    private List<MetadataCounts> subCategories;

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class MetadataCounts {

        private UUID id;
        private Long count;
        private String label;
    }
}
