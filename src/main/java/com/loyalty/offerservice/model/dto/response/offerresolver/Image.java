package com.loyalty.offerservice.model.dto.response.offerresolver;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter

@JsonInclude(JsonInclude.Include.NON_NULL)
public class Image {
    private String url;

    @JsonCreator
    public Image(@JsonProperty("path") String path) {
        this.url = path;
    }

    @JsonProperty("url")
    public String getUrl() {
        return url;
    }
}
