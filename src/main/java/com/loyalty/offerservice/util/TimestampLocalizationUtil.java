package com.loyalty.offerservice.util;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Objects;

public class TimestampLocalizationUtil {

    public static String getFormattedLocalTimestamp(final String region) {
        if (region != null && Province.isValid(region.toUpperCase())) {
            Province province = Province.valueOf(region.toUpperCase());
            String timeZoneId = getTimezoneByProvince(province);
            ZonedDateTime timeStamp = Instant.now().atZone(ZoneId.of(timeZoneId));
            return timeStamp.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        }

        ZonedDateTime timeStamp = Instant.now().atZone(ZoneId.systemDefault());
        return timeStamp.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
    }

    private static String getTimezoneByProvince(Province province) {
        return switch (province) {
            case NL -> "America/St_Johns";
            case AB, NT -> "America/Edmonton";
            case BC, YT -> "America/Vancouver";
            case NU, ON, QC, TB -> "America/Toronto";
            case NB -> "America/Moncton";
            case PE, NS -> "America/Halifax";
            case SK, MB -> "America/Winnipeg";
        };
    }

    protected enum Province {
        AB, BC, MB, NB, NL, NS, NT, NU, ON, PE, QC, SK, YT, TB;

        static boolean isValid(String region) {
            return Arrays.stream(Province.values()).anyMatch(p -> p.name().equals(region));
        }
    }
    public static LocalDateTime getTimeStamp(LocalDateTime localDateTime) {
        return Objects.requireNonNullElseGet(localDateTime, () -> LocalDateTime.parse(TimestampLocalizationUtil.getFormattedLocalTimestamp(null)));
    }
}
