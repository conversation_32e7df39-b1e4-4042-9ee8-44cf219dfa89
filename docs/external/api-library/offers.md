---
title: GET /offers
description: Used to get multiple offers based on parametes.
type: docs
weight: 20.1
---

# GET /offers

This is the core API for Offer Service. API to get all details for displaying offers to collectors and the general public. This API has multiple query paramenter and headers that can change the response of the offers returned in varios ways.

## Query Parameters

| Name           | Rquired | Type                            | Discription                                                                                               |
| -------------- | ------- | ------------------------------- |-----------------------------------------------------------------------------------------------------------|
| region         | ✔️      | String (single value)           | Will only show offers from a single region                                                                |
| partner_id     |         | UUID (multiple value)           | Will return offers with only the provided parters                                                         |
| category_id    |         | UUID (multiple value)           | Will return offers with only the provided category                                                        |
| subcategory_id |         | UUID (multiple value)           | Will return offers with only the provided subcategory                                                     |
| promotion_id   |         | UUID (single value)             | Will return offers with only the provided promotion                                                       |
| type           |         | String (multiple value)         | Specify offer types. ie: `buy`, `spend`                                   |
| program_type   |         | String (multiple value)         | Specify offer program types. allowed values: `traditionalcore`, `cardlinked`, `airmilesshops`, `bmopreapp` |
| availability   |         | String (multiple value)         | Specify where offer is avaliable. allowed values: `online`, `in-store`                                    |
| sort           |         | String (multiple value)         | Specify the order of the offers returned, check allowed values list.                                      |
| offset         |         | Number (int)                    | Specify how many offers to skip at the begining and then start showing the offers.                        |
| limit          |         | Number (int, min = 1, max = 96) | Specify the number of offers to return                                                                    |

### Authenticated

When calling /offers with a token (as a collector), a few more query paramaters are available to use in addition to all the ones metioned above.

| Name       | Rquired | Type                  | Discription                                                                                                |
| ---------- | ------- | --------------------- | ---------------------------------------------------------------------------------------------------------- |
| states     |         | String (single value) | Specify offers that have been interacted in some way by the collector. allowed values: `SAVED`, `OPTED_IN` |
| mass_offer |         | Boolean               | Specify to show offers that are **targeted**, when mass_offer = `false`                                    |

### Sort Value and Behaviours

| Behaviour           | Sort Value                  | Need Authentication? | Discription                                                                                                                                                                                          |
| ------------------- | --------------------------- | -------------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| Default             | [promotionId,massOffer,...] |                      | Offers are grouped and sorted by promotion id, mass offer, and then ranked by partner priority, and then sorted by offer priority. This is the default sort used on the AirMiles website and mobile. |
| Ending soon         | [..]                        |                      | Offers are sorted by End Date first, grouped and sorted by partner priority second, and finally sorted by offer priority.                                                                            |
| Collector Relevance | [collectorRelevance]        | ✔️                   | This is sorting based on Collector prefrences powered by magic.                                                                                                                                      |
| Region Relevance    | [regionRelevance]           |                      | This is sorting based on most popular offers in the region powered by more magic.                                                                                                                    |

## Headers

| Name             | Rquired | Type            | Discription                                                                                                                              |
| ---------------- | ------- | --------------- | ---------------------------------------------------------------------------------------------------------------------------------------- |
| X-Correlation-Id |         | UUID            | This is used to track the request through the logs, if not provided it will be auto generated by API and returned in the header response |
| X-Origin-Client  | ✔️      | String          | This field is to let us know where the request is coming from. ie: `internal:amrp:postman`, `external:web:bmo`                           |
| Accept-Language  |         | String - Locale | This is let API know if you want the response back in some supported language. Allowed value: `en-US`, `fr-CA`                           |

### Authenticated

To make authenticated request you need an additional header. The tokens that are supported are `member` tokens, these tokens have an associated collecter specification.

| Name          | Rquired | Type           | Discription                                                                                                                                            |
| ------------- | ------- | -------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------ |
| Authorization |         | Bearer {token} | Specifies a collector when making call to this api. This will give the ability to see the offers tarrgeted for that collector and the states of offers |

## Response

The response has two main section `offers` and `metadata`. The latter is very simple just containing total count of offers that match the given filters in the query parameter.

lets focus on the `offers:[]` list which is a list of offer objects, lets talk about some key information thats returned to us in these offer objects (for full list of values retuned, check out [Sample Response](Sample-Response)) :

| Name           | Type          | Discription                                                                                                              |
| -------------- | ------------- | ------------------------------------------------------------------------------------------------------------------------ |
| id             | UUID          | Offer object ID                                                                                                          |
| partnerId      | UUID          | Associated partner id                                                                                                    |
| categoryId     | UUID          | Associated category id                                                                                                   |
| awardShort     | String        | description of the award associated with offer                                                                           |
| qualifierShort | String        | Short description of what is needed to qualify for offer award                                                           |
| displayDate    | LocalDateTime | When can this offer show up for collectors                                                                               |
| startDate      | LocalDateTime | When can this offer be avalilable for use by collectors. Usually same as **displayDate**                                 |
| endDate        | LocalDateTime | When can this offer be marked as expired so that no more collectors are able to see/use it                               |
| massOffer      | Boolean       | `true` = Offer is publicly avaliable, `false` = Offer is targeted to specific collectors.                                |
| tiers          | List\<tier>   | Offers can have multiple qualifers and each can have a different award                                                   |
| mechanisms     | List          | How to qualify for the offer                                                                                             |
| states         | List\<States>  | **Authenticated Request Only** Will give a list of states which are object showing the name, value and when it was updated |
| legalText      | String        | Legal copy for offer                                                                                                     |


# Sample

### Request

```HTTP
GET /offers?region=ON&limit=1 HTTP/1.1
Host: cdn.airmilesapis.ca
X-Origin-Client: internal:amrp:postman
Authorization: Bearer `TOKEN`
```

### Response Body

```json
{
  "offers": [
    {
      "id": "4a80ad4a-de28-4c77-8180-0a89a71f4a55",
      "partnerId": "3267588b-791d-49bc-a321-d85d5f818480",
      "partnerLabel": "AIR MILES",
      "partnerLogo": {
        "url": "https://dev.cdn.airmilesapis.ca/partner-logo/7m10wxejlkq7/69ZErUapt6c8WQioe6iYSS/5107f862d6a2b238ff6f7739c5a90992/AIRMILES_PLANE_ELECTRIC_BLUE_RGB_E.png"
      },
      "partnerProfileURL": "https://www.airmiles.ca/en/offers/partners/air-miles-reward-program-bonus-offers.html",
      "categoryId": "121fa2dd-1fab-4492-8c5d-fb34624f4dda",
      "categoryLabel": "Travel",
      "awardShort": "You could win a Walt Disney World vacation from AIR MILES®",
      "qualifierShort": "Enter with your Collector info. Plus, earn 4 extra entries when you book any trip with AIR MILES® Travel.*",
      "image": {
        "url": "https://s3.amazonaws.com/prod-l1-amrpwl-post-images/processed-images/4008e08c-cd46-4429-bdc6-ce763cf4731a"
      },
      "displayDate": "2024-05-07T00:00:00",
      "startDate": "2024-05-07T00:00:00",
      "endDate": "2024-05-26T23:59:59",
      "description": "Contest ends May 26, 2024. Must be 18 years or older to enter. No purchase necessary. Contest Rules apply. ",
      "programType": "traditionalcore",
      "massOffer": true,
      "displayPriority": 1000,
      "tiers": [
        {
          "awardLong": "You could win a Walt Disney World vacation from AIR MILES®",
          "qualifierLong": "Enter with your Collector info. Plus, earn 4 extra entries when you book any trip with AIR MILES® Travel.*"
        }
      ],
      "mechanisms": [
        {
          "mechanismType": "button",
          "mechanismLabel": "Enter now",
          "mechanismValue": "https://www.airmiles.ca/arrow/Splash?splashId=26100080&changeLocale=en_CA"
        }
      ],
      "states": [
        {
          "name": "OPT_IN",
          "value": "OPTED_IN",
          "updatedAt": "2024-04-29T13:56:09.083363Z"
        },
        {
          "name": "SAVE",
          "value": "SAVED",
          "updatedAt": "2024-04-29T13:56:09.082147Z"
        }
      ],
      "legalText": "The Contest runs from May 6, 2024 to May 26, 2024 and is open to Canadian resident AIR MILES® collectors who have reached the age of 18. *NO PURCHASE NECESSARY Collectors can visit https://airmiles.ca/magicalmemories\nto complete and submit the form and get 1x entry. To get 4 additional entries collectors must book any trip on AIR MILES Travel during the contest period and complete their trip by July 31, 2024 (or complete the alternative bonus entry method outlined in the full contest rules). Limit one (1) base entry and four (4) bonus entries per collector number. One (1) prize is available to be won consisting of a vacation for four (4) to the Walt Disney World Resort in Florida. Approximate Prize value is $10,219.00 USD ($13,285.00 CDN). Correctly answered math skill-testing question required upon selection as a potential winner. Odds of winning depend on the number of eligible entries received. For full contest rules and details, visit https://airmiles.ca/magicalmemories\n\n®™ Trademarks of AM Royalties Limited Partnership used under license by AIR MILES Loyalty Inc.\n"
    }
  ],
  "metadata": {
    "total": 265
  }
}
```

### Response Header

|                  |                                      |
| ---------------- | ------------------------------------ |
| X-Correlation-Id | a0ccb3a7-2b19-4916-8b7e-d3059ccc17bb |

For support its best to keep track of the `X-Correlation-Id` value, as that can be used to track down logs for this request.
