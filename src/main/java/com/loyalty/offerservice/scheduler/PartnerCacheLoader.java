package com.loyalty.offerservice.scheduler;

import com.loyalty.offerservice.enums.LanguageCode;
import com.loyalty.offerservice.model.dto.request.OfferRequest;
import com.loyalty.offerservice.service.partner.PartnerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.annotation.Profile;
import org.springframework.context.event.EventListener;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.locks.ReentrantLock;

import static net.logstash.logback.marker.Markers.appendEntries;

@Service
@Slf4j
@Profile("!test")
public class PartnerCacheLoader {
    @Autowired
    PartnerService partnerService;

    @Autowired
    private Environment environment;

    private final ReentrantLock lock = new ReentrantLock();

    @Async
    @EventListener(ApplicationStartedEvent.class)
    public void onStartup() throws IOException {
        log.info("Executing......... partner's cache load on startup");
        loadData();
    }

    /**
     * This corn job runs at every day midnight 00:00
     * 0 0 0 * * *
     * */
    @Scheduled(cron = "0 0 0 * * *")
    public void loadPartners() throws IOException {
        log.info("Executing.......... partner's Scheduler");
        loadData();
    }

    /**
     * Added a lock to prevent concurrent running of this method
     * */
    void loadData() throws IOException{
        lock.lock();
        try {
            log.info("Executing..... Partner cache for english");
            long totalEnglishPartners = partnerService.loadPartners(generateOfferRequest(LanguageCode.ENGLISH_US.getCode()));
            log.info("Executing..... Partner cache for french");
            long totalFrenchPartners = partnerService.loadPartners(generateOfferRequest(LanguageCode.FRENCH_CA.getCode()));

            Map<String, Long> totals = new HashMap<>();
            totals.put("totalEnglishPartners", totalEnglishPartners);
            totals.put("totalFrenchPartners", totalFrenchPartners);
            log.info(appendEntries(totals),
                    "Loaded a total of English and French partners into the cache.");
        } finally {
            lock.unlock();
        }
    }

    private OfferRequest generateOfferRequest(String language){
        OfferRequest request = new OfferRequest();
        request.setLanguage(language);
        request.setCorrelationId(UUID.randomUUID());
        request.setOriginClient("internal:amrp:web");

        return request;
    }
}
