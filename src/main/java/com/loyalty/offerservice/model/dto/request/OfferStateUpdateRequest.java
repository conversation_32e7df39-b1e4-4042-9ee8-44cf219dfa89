package com.loyalty.offerservice.model.dto.request;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Data
@Builder
public class OfferStateUpdateRequest {
	private LocalDateTime localDate;
	private String originClient;
	private UUID correlationId;
	private UUID offerId;
	private Long collectorId;
	private OfferStateBody body;

	@Data
	public static class OfferStateBody {
		private List<OfferStatesServiceRequest> stateChanges;

	}
}
