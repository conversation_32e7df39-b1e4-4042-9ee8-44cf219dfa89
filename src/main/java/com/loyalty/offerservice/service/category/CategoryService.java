package com.loyalty.offerservice.service.category;

import com.loyalty.offerservice.model.dto.response.category.CategoriesResponse;

import java.io.IOException;

public interface CategoryService {

    /**
     * Method to get Category and SubCategory structure
     *
     * @param locale - specify the language of the labels returned in the response
     * @return CategoriesResponse - List of Categories with id, label, count, List
     *         of subCategories (with each subCategory, containing the id, label,
     *         count)
     * @throws IOException
     */
    CategoriesResponse getCategories(String locale) throws IOException;

}
