package com.loyalty.offerservice.model.dto.response.partner;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class PartnerResponse{
    private List<PartnerObject> results;
    private Integer limit;
    private Integer offset;
    private Integer total;

    public void addPartner(PartnerObject partner) {
        if (results == null) {
            results = new ArrayList<>();
        }
        results.add(partner);
    }

}