---
title: Example Requests and Responses
type: docs
weight: 20
no_list: true
---

This section provides examples of requests and responses for the Offer Service API.

{{< children description="true" />}}

## Metadata Information

The only api that supports `extended_metadata` is the `/offers` api. This will return a list of partners and categories. The `id` field in the response can be used to filter the offers by partner or category or subcategory. By default this field is set as false and can be enabled by passing `extended_metadata=true` in the query params.

The metadata can also be effected by other filters such as partner_id, category_id, subcategory_id, promotion_id, type, program_type, states, availability, mass_offer, event_based, experiment, exclude_partner_id. As those will change the counts of total offers.

### Request

```
GET /offers?region=ON&extended_metadata=true&limit=1
```

### Response

```json
{
    "offers": [
        {
            "id": "6fdb76f3-fa1c-47d7-858e-ceec5ee53542",
            "partnerId": "60d672dd-c9a0-40c7-bd76-b68fc8ef536a",
            "partnerLabel": "AIR MILES Receipts",
            "partnerLogo": {
                "url": "https://cdn.airmilesapis.ca/partner-logo/7m10wxejlkq7/7LdqxL861Z8azcZ1tRvwJ7/85a924af5bc68c70b8fa147abdf48ed2/RECI-logo.png"
            },
            "partnerProfileURL": "https://www.airmiles.ca/en/offers/partners/air-miles-receipts.html",
            "categoryId": "0a889711-9ab8-4972-a0b5-b9eafc8fa312",
            "categoryLabel": "Retail",
            "promotionId": "6ba6a652-b4d0-47bb-a431-146389cedb3b",
            "promotionLabel": "New Partner",
            "awardShort": "50 Bonus Miles",
            "qualifierShort": "when you scan a MEC receipt through AIR MILES® Receipts with a spend of at least $120 (before taxes) on your favourite products.*",
            "image": {
                "url": "https://cdn.airmilesapis.ca/offer-image/processed-images/e9c4e1cc-0281-4912-b040-44a147fea1da.jpg"
            },
            "displayDate": "2025-05-15T00:00:00",
            "startDate": "2025-05-15T00:00:00",
            "endDate": "2025-08-27T23:59:59",
            "description": "limit 1 per collector per week period",
            "programType": "traditionalcore",
            "massOffer": true,
            "displayPriority": 1000,
            "tiers": [
                {
                    "awardValue": 50.0,
                    "awardLong": "50 Bonus Miles",
                    "qualifierLong": "when you scan a MEC receipt through AIR MILES® Receipts with a spend of at least $120 (before taxes) on your favourite products.*"
                }
            ],
            "mechanisms": [
                {
                    "mechanismType": "button",
                    "mechanismLabel": "Scan A Receipt",
                    "mechanismValue": "https://mobileapp.airmiles.ca/D2Ua/a7qlaw2q"
                }
            ],
            "legalText": "Offer valid from May 15 to 11:59 p.m. August 27, 2025 in Alberta, British Columbia, Manitoba, New Brunswick, Newfoundland and Labrador, Northwest Territories, Nova Scotia, Nunavut, Ontario, Prince Edward Island, Québec, Saskatchewan, Thunder Bay, Yukon. \n\nMinimum qualifying spend of $120 must be made in-person at the retailer, in a single transaction, during the offer period. The minimum qualifying spend is calculated before taxes and after all discounts and exclusions are applied. Only receipts printed in-store are eligible for this offer. While quantities of 50 Miles last. \n\nWe reserve the right to limit or make changes to the offer and to terminate the offer at any time, for any reason and without notice. Personal purchases only. To qualify, original receipt must be scanned within 14 days of eligible purchase. No duplicates. Click “Qualifying Details” for receipt verification requirements. Limit of 1 offer uses per AIR MILES collector number. This offer can be combined with other offers, including AIR MILES offers. Void where prohibited. Please allow up to 120 days for receipts to be verified and Bonus Miles to be posted to your collector account. \n\n®™ Trademarks of AM Royalties Limited Partnership used under license by AIR MILES Loyalty Inc. and Mountain Equipment Company Ltd. Partner, supplier and retailer trademarks are owned by the respective partner, supplier and retailer or authorized for their use in Canada",
            "eventBasedOffer": false
        }
    ],
    "metadata": {
        "total": 138,
        "partners": [
            {
                "id": "7b99e1e8-704f-450b-ac15-62c7ae842223",
                "count": 1,
                "label": "Airalo"
            },
            {
                "id": "c0475571-73af-4a7e-a716-fafe5852dd5f",
                "count": 1,
                "label": "Robert Barakett"
            },
            {
                "id": "33d0cf88-5eae-4186-be03-05092061eed9",
                "count": 1,
                "label": "HP"
            },
            {
                "id": "5058f22a-1f5c-4625-b8ff-ffe116756722",
                "count": 2,
                "label": "TIMBER MART"
            },
            {
                "id": "4c736b45-3e3c-4561-95de-6fc0f8d9356f",
                "count": 1,
                "label": "Ever New"
            },
            {
                "id": "1c8a5a98-d880-4dee-a409-76bb18dc6bd1",
                "count": 1,
                "label": "OLG"
            },
            {
                "id": "22a2cdfd-ff82-45f6-bc94-c14a3a533922",
                "count": 2,
                "label": "BMO Bank of Montreal"
            },
            {
                "id": "e914624c-9676-4287-8ce9-04de68dd0507",
                "count": 26,
                "label": "airmilesshops.ca"
            },
            {
                "id": "1d5c3094-8c71-4815-a15e-412dfd99c7c3",
                "count": 1,
                "label": "Adidas"
            },
            {
                "id": "b6ff0d3b-dd58-43ae-a004-7db06eee13ee",
                "count": 1,
                "label": "Fit4Less"
            },
            {
                "id": "ec97d51a-1041-47f0-a1ff-255e082d11b9",
                "count": 1,
                "label": "Nardini Specialties"
            },
            {
                "id": "ef73e58a-db63-4b27-baae-bd1e1d2ae86c",
                "count": 1,
                "label": "Peter & Paul's Gifts"
            },
            {
                "id": "e0a072dd-f9f3-41e0-9f9b-c47efd099d4f",
                "count": 1,
                "label": "Shiseido"
            },
            {
                "id": "60d672dd-c9a0-40c7-bd76-b68fc8ef536a",
                "count": 1,
                "label": "AIR MILES Receipts"
            },
            {
                "id": "50540031-46c9-4ade-a3bb-1805844118df",
                "count": 1,
                "label": "Baskits"
            },
            {
                "id": "4c06d824-61f0-4876-ada0-ada98902917c",
                "count": 1,
                "label": "Budget Car Rental"
            },
            {
                "id": "200bf1dc-15bc-4e63-8e47-1df3b3f320dc",
                "count": 1,
                "label": "Drunk Elephant"
            },
            {
                "id": "6a8231fb-5d17-44c5-b4e4-05baffc1f7e1",
                "count": 1,
                "label": "Clé de Peau Beauté"
            },
            {
                "id": "10def07d-bae9-463c-bf88-28169a1fa922",
                "count": 1,
                "label": "NARS"
            },
            {
                "id": "235ede29-8dfc-40ad-984b-e8d4d9261a09",
                "count": 1,
                "label": "Avis"
            },
            {
                "id": "0c2b557f-ae1c-4e3f-b78e-d013b22217d0",
                "count": 1,
                "label": "Emma Sleep"
            },
            {
                "id": "1563e943-25fd-480b-b77d-94aedc7202af",
                "count": 1,
                "label": "LG Electronics"
            },
            {
                "id": "b90e694d-3510-4cc2-84be-f9b6f61bd31b",
                "count": 20,
                "label": "Shell Mobility"
            },
            {
                "id": "43328b82-e107-48d6-8026-552ca67c14cd",
                "count": 1,
                "label": "United Beans Coffee Roastery"
            },
            {
                "id": "26bc9e95-fbe2-49df-aafe-1943ef326b80",
                "count": 1,
                "label": "Asus"
            },
            {
                "id": "337e5aea-f67d-41fa-aef3-f628d1dd280b",
                "count": 1,
                "label": "Sephora"
            },
            {
                "id": "63e8543d-fbf7-458e-8712-d25332da3614",
                "count": 37,
                "label": "AIR MILES Travel"
            },
            {
                "id": "cdcb2cc9-6720-4c34-aa59-02af5a7088f5",
                "count": 3,
                "label": "Pharmasave"
            },
            {
                "id": "cd1647ac-14d1-4e25-bd6b-b9ee874e9fa2",
                "count": 1,
                "label": "Factor"
            },
            {
                "id": "78e7d401-012a-46a5-aeba-8e88726a905b",
                "count": 1,
                "label": "Flash Offers"
            },
            {
                "id": "8d26fb71-ef30-47cc-904e-49e3f877271d",
                "count": 1,
                "label": "Rewarding Your Opinions by Dynata"
            },
            {
                "id": "0a9e595d-18c9-4f42-a8e0-bc3a45e96856",
                "count": 1,
                "label": "Jiffy Lube"
            },
            {
                "id": "6cfbad8d-946a-460c-b181-702f518a68da",
                "count": 1,
                "label": "Moroccanoil"
            },
            {
                "id": "3267588b-791d-49bc-a321-d85d5f818480",
                "count": 4,
                "label": "AIR MILES"
            },
            {
                "id": "967907ee-fcc9-4610-8301-908c4728eeda",
                "count": 2,
                "label": "Super Channel"
            },
            {
                "id": "8dd859c1-55d1-4157-8afc-7607297bdfeb",
                "count": 2,
                "label": "Global Pet Foods"
            },
            {
                "id": "19e1bb2b-88d4-4b37-be72-262a694ae58d",
                "count": 1,
                "label": "National Car Rental"
            },
            {
                "id": "cbfcd38f-b1c0-4443-881c-c342833649a0",
                "count": 1,
                "label": "Fresh"
            },
            {
                "id": "024e36c1-aedf-47d4-a3dc-426ce790d81c",
                "count": 1,
                "label": "Lyft"
            },
            {
                "id": "3c09f903-7334-4d2b-82ba-22f178c8b96f",
                "count": 1,
                "label": "Mini Mioche"
            },
            {
                "id": "1b36212c-87a6-448c-82a7-c2f28c8b6d52",
                "count": 1,
                "label": "HelloFresh"
            },
            {
                "id": "ca4bf2db-2bd4-43c1-9d25-de31c04597a8",
                "count": 1,
                "label": "Dell"
            },
            {
                "id": "03b4f3f7-f5cd-4840-b2e8-474057e686fc",
                "count": 5,
                "label": "AIR MILES Local Partners"
            },
            {
                "id": "5a92c696-cddd-4255-920e-1f74b121e7ea",
                "count": 1,
                "label": "Kernels"
            },
            {
                "id": "38178928-9281-42aa-9f70-8c39c453baf1",
                "count": 1,
                "label": "Alamo Rent a Car"
            }
        ],
        "categories": [
            {
                "id": "22b666ae-97c2-4e57-a437-e977de6beef4",
                "label": "Grocery",
                "count": 3,
                "subCategories": [
                    {
                        "id": "4683cd1f-bba8-4dd0-b960-4324a246960b",
                        "label": "Baby",
                        "count": 0
                    },
                    {
                        "id": "873c7bfa-745e-4bdd-afe2-55425821d721",
                        "label": "Bakery",
                        "count": 0
                    },
                    {
                        "id": "d614fed9-8c17-4c20-a179-2351e54679c1",
                        "label": "Beverages",
                        "count": 0
                    },
                    {
                        "id": "eab2662b-bfc5-4457-8cbf-33d9e10f9bb6",
                        "label": "Dairy and Eggs",
                        "count": 0
                    },
                    {
                        "id": "f1f75e01-8b8f-460c-bc89-8bdb40060e7c",
                        "label": "Deli and Prepared Food",
                        "count": 0
                    },
                    {
                        "id": "da4cdf1b-e2f0-4b1c-99b7-c0ce4696292f",
                        "label": "Frozen Foods",
                        "count": 0
                    },
                    {
                        "id": "37485ad6-07ad-4145-80f1-fbecdd46a26a",
                        "label": "Fruits and Vegetables",
                        "count": 0
                    },
                    {
                        "id": "0d3245ac-9812-4bc3-9020-16b1349f1531",
                        "label": "Meat and Seafood",
                        "count": 0
                    },
                    {
                        "id": "be58904e-4ae9-49fc-ac6d-0e966dc193bb",
                        "label": "Pantry",
                        "count": 0
                    },
                    {
                        "id": "d295e726-8205-4a41-8207-a6e31714dde3",
                        "label": "Snacks",
                        "count": 0
                    },
                    {
                        "id": "53a177af-b923-43ea-ac7d-0230ea0db805",
                        "label": "Vegan and Vegetarian",
                        "count": 0
                    },
                    {
                        "id": "3c4b099b-061c-493b-8bc4-4ced119949c4",
                        "label": "Organic Groceries",
                        "count": 0
                    },
                    {
                        "id": "e9c77b08-a3e5-4846-90e5-9e9ad0f34de1",
                        "label": "Other Grocery",
                        "count": 0
                    }
                ]
            },
            {
                "id": "1ba636e3-799d-44c3-9c84-42a3df6430b0",
                "label": "Household Supplies",
                "count": 0,
                "subCategories": [
                    {
                        "id": "279ecbd8-b81b-4d46-9726-8fdd4dee8af8",
                        "label": "Cleaning",
                        "count": 0
                    },
                    {
                        "id": "43cf7742-601f-43ef-9934-3a461493eb4e",
                        "label": "Laundry",
                        "count": 0
                    },
                    {
                        "id": "0b9d187d-bbf2-4884-9e10-2df4c97bcd3f",
                        "label": "Paper and Plastics",
                        "count": 0
                    }
                ]
            },
            {
                "id": "0f597ee1-16b8-4a4f-beb7-4654cfd0b45a",
                "label": "Pharmacy",
                "count": 3,
                "subCategories": [
                    {
                        "id": "4683cd1f-bba8-4dd0-b960-4324a246960b",
                        "label": "Baby",
                        "count": 0
                    },
                    {
                        "id": "dc725f55-1c70-4088-8108-cc199bd2ca32",
                        "label": "Beauty",
                        "count": 0
                    },
                    {
                        "id": "ef1ff9bd-6032-4c14-8c51-8b1f43dd0970",
                        "label": "Medicine and Health",
                        "count": 0
                    },
                    {
                        "id": "d871a234-93f3-47f0-991a-b230cb797d74",
                        "label": "Personal Care",
                        "count": 0
                    }
                ]
            },
            {
                "id": "c7a208f3-8f94-4460-b76c-3c75ae53417d",
                "label": "Liquor",
                "count": 1,
                "subCategories": [
                    {
                        "id": "0dd2f52d-af4a-45f9-aa1c-c66cd13f16e6",
                        "label": "Beer and Cider",
                        "count": 0
                    },
                    {
                        "id": "6cad2024-2bc8-47f5-b563-53f97c1f7205",
                        "label": "Coolers",
                        "count": 0
                    },
                    {
                        "id": "b3962476-bb50-47c2-af54-3bdec43fa499",
                        "label": "Spirits",
                        "count": 0
                    },
                    {
                        "id": "8b618c88-cd2c-47fa-9de7-677fe1d3737f",
                        "label": "Wine",
                        "count": 0
                    }
                ]
            },
            {
                "id": "38d878d7-c3e0-4ea1-8e6c-d4759f307042",
                "label": "Pets",
                "count": 2
            },
            {
                "id": "77c6067e-bd25-43a9-826c-7a4788581952",
                "label": "Home and Garden",
                "count": 5
            },
            {
                "id": "0333d0e5-68be-4833-939f-3a20d0cc9806",
                "label": "Fuel and Auto",
                "count": 21
            },
            {
                "id": "0cbc9203-2811-4eae-9dc1-68da692658b8",
                "label": "Financial",
                "count": 3
            },
            {
                "id": "0a889711-9ab8-4972-a0b5-b9eafc8fa312",
                "label": "Retail",
                "count": 20
            },
            {
                "id": "0387d04b-2736-48e4-b804-ac50f4222b79",
                "label": "Office",
                "count": 0
            },
            {
                "id": "121fa2dd-1fab-4492-8c5d-fb34624f4dda",
                "label": "Travel",
                "count": 43
            },
            {
                "id": "17180943-4cef-4fb8-833d-9919e359e4f3",
                "label": "Gift Cards",
                "count": 0
            },
            {
                "id": "e10dea41-3aee-4a24-bc8d-0d8de8c3e5ae",
                "label": "airmilesshops.ca",
                "count": 27
            },
            {
                "id": "cc1b19d6-72e0-47bc-8b81-2b1fbb49d050",
                "label": "Other",
                "count": 10
            }
        ]
    }
}

```

## Category Filtering

Just like `partner_id`, `category_id` can also be used to filter the offers either by single or multiple values. The same way `subcategory_id` can be used to filter the offers by subcategory.

### Request

```
GET /offers?limit=1&extended_metadata=true&region=ON&category_id=22b666ae-97c2-4e57-a437-e977de6beef4,0f597ee1-16b8-4a4f-beb7-4654cfd0b45a
```

### Response

```json
{
    "offers": [
        {
            "id": "240ac0c9-7376-4e1d-82f6-580d7caa26da",
            "partnerId": "ec97d51a-1041-47f0-a1ff-255e082d11b9",
            "partnerLabel": "Nardini Specialties",
            "partnerLogo": {
                "url": "https://cdn.airmilesapis.ca/partner-logo/7m10wxejlkq7/2nmfnRx8NuWlN0Q6CDRXva/fc20f42a74915de46d23f8dd9783023d/NARDINI_LOGO_-_jpg.jpg"
            },
            "partnerProfileURL": "https://www.airmiles.ca/en/offers/partners/nardini-specialties.html",
            "categoryId": "22b666ae-97c2-4e57-a437-e977de6beef4",
            "categoryLabel": "Grocery",
            "promotionId": "6ba6a652-b4d0-47bb-a431-146389cedb3b",
            "promotionLabel": "New Partner",
            "awardShort": "10 Bonus Miles",
            "qualifierShort": "Get 10 Bonus Miles for every $40 spent online at https://www.nardinispecialties.ca/ or in store at Nardini Specialties in a single transaction.*",
            "image": {
                "url": "https://cdn.airmilesapis.ca/offer-image/processed-images/0df97dd2-ff40-45de-b4b2-580070f1d5ca.jpg"
            },
            "displayDate": "2025-07-01T00:00:00",
            "startDate": "2025-07-01T00:00:00",
            "endDate": "2025-09-30T23:59:59",
            "programType": "cardlinked",
            "massOffer": true,
            "displayPriority": 0,
            "tiers": [
                {
                    "awardValue": 10.0,
                    "awardLong": "10 Bonus Miles",
                    "qualifierLong": "Get 10 Bonus Miles for every $40 spent online at https://www.nardinispecialties.ca/ or in store at Nardini Specialties in a single transaction.*"
                }
            ],
            "mechanisms": [
                {
                    "mechanismType": "noAction"
                }
            ],
            "legalText": "Offer valid for all linked cards.\n\n* Offer available to AIR MILES collectors with an eligible Canadian-issued Mastercard credit card or an eligible Bank of Montreal (BMO) issued debit card associated with a BMO Personal Banking account that is linked to their AIR MILES Collector Account (a “Linked Card”). Offer valid from July 1, 2025 to September 30, 2025. Valid at https://www.nardinispecialties.ca and Nardini Specialties store location in Canada while quantity of AIR MILES Bonus Miles last.  \nThere is no offer limit. This offer can be combined with other offers.  \nThe minimum qualifying purchase or spend amount is calculated based on the total transaction amount charged to your Linked Card, including taxes, shipping and handling, as applicable. To qualify for the offer, the eligible purchase transaction(s) must be posted to your Linked Card account by the offer end date. For example, in the case of an online purchase made during the offer period, it may not qualify if the transaction is processed on the shipping date and that date occurs after the offer ends. Please allow up to 30 days after the offer end date for Bonus Miles to be posted to your AIR MILES collector account. Your Linked Card account must be in good standing at the time the Bonus Miles are awarded. Additionally, your AIR MILES Collector Account must be active at the time you make the qualifying purchase. \nNeither Mastercard International Incorporated nor Interac Corp. are responsible for the fulfillment of any offer. Purchases made by an authorized user of a supplementary card linked to the Linked Card do not qualify, unless the supplementary card is also linked to the AIR MILES collector number. AIR MILES reserves the right to make changes to this offer and to withdraw the offer at any time, for any reason and without notice. Before you make a purchase, make sure the offer still actively appears on airmiles.ca/cardlinkedoffers and/or in your AIR MILES offers feed. All offers are subject to the AIR MILES Card Linked Offers Terms and Conditions found online at airmilescardlink.ca/terms, which includes information about how to unlink your AIR MILES Collector account if you no longer wish to participate in Card Linked Offers.",
            "cardType": [
                "NonBmoMastercard",
                "BmoMastercard",
                "BmoDebit"
            ],
            "eventBasedOffer": false,
            "ctaLabel": {
                "en-US": "SHOP NOW "
            },
            "ctaUrl": {
                "en-US": "https://www.nardinispecialties.ca/"
            }
        }
    ],
    "metadata": {
        "total": 6,
        "partners": [
            {
                "id": "cdcb2cc9-6720-4c34-aa59-02af5a7088f5",
                "count": 3,
                "label": "Pharmasave"
            },
            {
                "id": "cd1647ac-14d1-4e25-bd6b-b9ee874e9fa2",
                "count": 1,
                "label": "Factor"
            },
            {
                "id": "ec97d51a-1041-47f0-a1ff-255e082d11b9",
                "count": 1,
                "label": "Nardini Specialties"
            },
            {
                "id": "1b36212c-87a6-448c-82a7-c2f28c8b6d52",
                "count": 1,
                "label": "HelloFresh"
            }
        ],
        "categories": [
            {
                "id": "22b666ae-97c2-4e57-a437-e977de6beef4",
                "label": "Grocery",
                "count": 3,
                "subCategories": [
                    {
                        "id": "4683cd1f-bba8-4dd0-b960-4324a246960b",
                        "label": "Baby",
                        "count": 0
                    },
                    {
                        "id": "873c7bfa-745e-4bdd-afe2-55425821d721",
                        "label": "Bakery",
                        "count": 0
                    },
                    {
                        "id": "d614fed9-8c17-4c20-a179-2351e54679c1",
                        "label": "Beverages",
                        "count": 0
                    },
                    {
                        "id": "eab2662b-bfc5-4457-8cbf-33d9e10f9bb6",
                        "label": "Dairy and Eggs",
                        "count": 0
                    },
                    {
                        "id": "f1f75e01-8b8f-460c-bc89-8bdb40060e7c",
                        "label": "Deli and Prepared Food",
                        "count": 0
                    },
                    {
                        "id": "da4cdf1b-e2f0-4b1c-99b7-c0ce4696292f",
                        "label": "Frozen Foods",
                        "count": 0
                    },
                    {
                        "id": "37485ad6-07ad-4145-80f1-fbecdd46a26a",
                        "label": "Fruits and Vegetables",
                        "count": 0
                    },
                    {
                        "id": "0d3245ac-9812-4bc3-9020-16b1349f1531",
                        "label": "Meat and Seafood",
                        "count": 0
                    },
                    {
                        "id": "be58904e-4ae9-49fc-ac6d-0e966dc193bb",
                        "label": "Pantry",
                        "count": 0
                    },
                    {
                        "id": "d295e726-8205-4a41-8207-a6e31714dde3",
                        "label": "Snacks",
                        "count": 0
                    },
                    {
                        "id": "53a177af-b923-43ea-ac7d-0230ea0db805",
                        "label": "Vegan and Vegetarian",
                        "count": 0
                    },
                    {
                        "id": "3c4b099b-061c-493b-8bc4-4ced119949c4",
                        "label": "Organic Groceries",
                        "count": 0
                    },
                    {
                        "id": "e9c77b08-a3e5-4846-90e5-9e9ad0f34de1",
                        "label": "Other Grocery",
                        "count": 0
                    }
                ]
            },
            {
                "id": "1ba636e3-799d-44c3-9c84-42a3df6430b0",
                "label": "Household Supplies",
                "count": 0,
                "subCategories": [
                    {
                        "id": "279ecbd8-b81b-4d46-9726-8fdd4dee8af8",
                        "label": "Cleaning",
                        "count": 0
                    },
                    {
                        "id": "43cf7742-601f-43ef-9934-3a461493eb4e",
                        "label": "Laundry",
                        "count": 0
                    },
                    {
                        "id": "0b9d187d-bbf2-4884-9e10-2df4c97bcd3f",
                        "label": "Paper and Plastics",
                        "count": 0
                    }
                ]
            },
            {
                "id": "0f597ee1-16b8-4a4f-beb7-4654cfd0b45a",
                "label": "Pharmacy",
                "count": 3,
                "subCategories": [
                    {
                        "id": "4683cd1f-bba8-4dd0-b960-4324a246960b",
                        "label": "Baby",
                        "count": 0
                    },
                    {
                        "id": "dc725f55-1c70-4088-8108-cc199bd2ca32",
                        "label": "Beauty",
                        "count": 0
                    },
                    {
                        "id": "ef1ff9bd-6032-4c14-8c51-8b1f43dd0970",
                        "label": "Medicine and Health",
                        "count": 0
                    },
                    {
                        "id": "d871a234-93f3-47f0-991a-b230cb797d74",
                        "label": "Personal Care",
                        "count": 0
                    }
                ]
            },
            {
                "id": "c7a208f3-8f94-4460-b76c-3c75ae53417d",
                "label": "Liquor",
                "count": 0,
                "subCategories": [
                    {
                        "id": "0dd2f52d-af4a-45f9-aa1c-c66cd13f16e6",
                        "label": "Beer and Cider",
                        "count": 0
                    },
                    {
                        "id": "6cad2024-2bc8-47f5-b563-53f97c1f7205",
                        "label": "Coolers",
                        "count": 0
                    },
                    {
                        "id": "b3962476-bb50-47c2-af54-3bdec43fa499",
                        "label": "Spirits",
                        "count": 0
                    },
                    {
                        "id": "8b618c88-cd2c-47fa-9de7-677fe1d3737f",
                        "label": "Wine",
                        "count": 0
                    }
                ]
            },
            {
                "id": "38d878d7-c3e0-4ea1-8e6c-d4759f307042",
                "label": "Pets",
                "count": 0
            },
            {
                "id": "77c6067e-bd25-43a9-826c-7a4788581952",
                "label": "Home and Garden",
                "count": 0
            },
            {
                "id": "0333d0e5-68be-4833-939f-3a20d0cc9806",
                "label": "Fuel and Auto",
                "count": 0
            },
            {
                "id": "0cbc9203-2811-4eae-9dc1-68da692658b8",
                "label": "Financial",
                "count": 0
            },
            {
                "id": "0a889711-9ab8-4972-a0b5-b9eafc8fa312",
                "label": "Retail",
                "count": 0
            },
            {
                "id": "0387d04b-2736-48e4-b804-ac50f4222b79",
                "label": "Office",
                "count": 0
            },
            {
                "id": "121fa2dd-1fab-4492-8c5d-fb34624f4dda",
                "label": "Travel",
                "count": 0
            },
            {
                "id": "17180943-4cef-4fb8-833d-9919e359e4f3",
                "label": "Gift Cards",
                "count": 0
            },
            {
                "id": "e10dea41-3aee-4a24-bc8d-0d8de8c3e5ae",
                "label": "airmilesshops.ca",
                "count": 0
            },
            {
                "id": "cc1b19d6-72e0-47bc-8b81-2b1fbb49d050",
                "label": "Other",
                "count": 0
            }
        ]
    }
}
```


## Pagination 

The `/offers` api is paginated. The default page size is 20 and can be changed using the `limit` query parameter (max of 96). The `offset` query parameter can be used to navigate the offers. The `total` field in the response can be used to determine the total number of offers available. 

The `offset` is by offer so `offset=2` will skip the first 2 offers and start showing from the 3rd offer. 

Example: lets say you make an `/offers?region=ON` call then you get `20` offers returned, now if your total is `50` in the metadata then to get to page 2 of the offers you would make a call to `/offers?region=ON&offset=20` and you would get the next 20 offers.

### Request

```
GET /offers?offset=1&region=ON&category_id=22b666ae-97c2-4e57-a437-e977de6beef4,0f597ee1-16b8-4a4f-beb7-4654cfd0b45a&limit=1
```

### Response

```json
{
    "offers": [
        {
            "id": "df82055b-6549-4334-b999-ad84122a251b",
            "partnerId": "1b36212c-87a6-448c-82a7-c2f28c8b6d52",
            "partnerLabel": "HelloFresh",
            "partnerLogo": {
                "url": "https://cdn.airmilesapis.ca/partner-logo/7m10wxejlkq7/YhYngmCYU2riJa9h7W7mg/41129364e77b3e1e663befbbc9b8f14e/HELLOFRESH_RGB_720.png"
            },
            "partnerProfileURL": "https://www.airmiles.ca/en/offers/partners/hellofresh.html",
            "categoryId": "22b666ae-97c2-4e57-a437-e977de6beef4",
            "categoryLabel": "Grocery",
            "awardShort": "Get up to 1000 Bonus Miles",
            "qualifierShort": "when you order HelloFresh for the first time*",
            "image": {
                "url": "https://cdn.airmilesapis.ca/offer-image/processed-images/104976a1-08a3-4ecd-b86f-a0c7b8db0c19.jpg"
            },
            "displayDate": "2024-12-26T00:00:00",
            "startDate": "2024-12-26T00:00:00",
            "endDate": "2025-12-31T23:59:59",
            "programType": "traditionalcore",
            "massOffer": true,
            "displayPriority": 1000,
            "tiers": [
                {
                    "awardLong": "Get up to 1000 Bonus Miles",
                    "qualifierLong": "when you order HelloFresh for the first time*"
                }
            ],
            "mechanisms": [
                {
                    "mechanismType": "couponCode",
                    "mechanismValue": "AIRML2025"
                }
            ],
            "legalText": "* 1.Get 500 AIR MILES® Bonus MilesTM on your first order of a HelloFresh Meal Kit box between September 20, 2023 and December 31, 2025. \n1. Use promo code AIRML2025 at checkout and ensure that your email with HelloFresh is the same email in your AIR MILES account profile.\n\n2.Then, get 100 AIR MILES Bonus MilesTM on each of your next 5 orders of HelloFresh Meal Kit Box2,. Ends December 31, 2025. \n\n\n1. Offer is valid September 20, 2023 to December  31, 2025 to Canadian resident AIR MILES collectors who purchase their first HelloFresh Meal Kit Box at hellofresh.ca. For clarification, offer is not valid to collectors who have purchased any HelloFresh Meal Kit Box prior to September 20, 2023, as determined by HelloFresh in its sole discretion. To qualify for this offer, i) the email address used to make the first HelloFresh purchase must be the same email address that appears in the AIR MILES collector profile, ii) eligible Collectors must purchase at least one HelloFresh Meal Kit box in a single transaction, and iii) Promo Code AIRML2025 must be entered at the time of checkout. Offer does not apply to Customers who have previously purchased Meal Kits from HelloFresh. Offer cannot be combined with other HelloFresh AIR MILES offers, including any offers available on airmilesshops.ca. Offer may be combined with other HelloFresh offers as determined by HelloFresh in their sole discretion. Limit of one offer per collector number. Please allow up to 75 days from the date of qualifying purchase for Bonus Miles to be posted to your collector account.\n\n\n2. Offer is valid September 20, 2023 to December  31, 2025 to Canadian resident AIR MILES collectors who i) first meet the criteria and qualify for the 500 Bonus Miles offer, as outlined above, and ii) then proceed to purchase up to 5 additional HelloFresh boxes during the offer period. Offer does not apply to Customers who have previously purchased Meal Kits from HelloFresh. Offer cannot be combined with other HelloFresh AIR MILES offers, including any offers available on airmilesshops.ca. Offer may be combined with other HelloFresh offers as determined by HelloFresh in their sole discretion. Limit of 500 Bonus Miles in connection with this offer per collector number. Please allow up to 75 days from the date of qualifying purchase for Bonus Miles to be posted to your collector account.",
            "eventBasedOffer": false
        }
    ],
    "metadata": {
        "total": 6
    }
}
```

Comparing this response with the [previous response](#request-1) we can see that the first offer is skipped and the second offer is returned.


## Saved Offers

Now saved offer filter do not work with any other filtering or sorting options. They are just a list of offers saved by the user. But `region` is still required, along with `Authorization` header.

### Request 

```
GET /offers?region=ON&states=SAVED HTTP/1.1
```

```
GET /offers?region=ON&states=SAVED&extended_metadata=true
```

```
GET /offers?region=ON&states=SAVED&extended_metadata=true&partner_id=e914624c-9676-4287-8ce9-04de68dd0507&sort=collectorrelevance
```

All above request result in the same response. The filters such as `partner_id`, `category_id`, `subcategory_id`, `promotion_id`, `type`, `program_type`, `availability`, `mass_offer`, `event_based`, `experiment`, `exclude_partner_id` are ignored. The `sort` is also ignored.

> limit and offset are still supported. but are not advised to be used.

### Response

```json
{
    "offers": [
        {
            "id": "0a824bee-5f15-4b75-9e35-7a3c0fb3fee4",
            "partnerId": "0c988dcd-b2a9-4391-b2c4-48f0294d5658",
            "partnerLabel": "Canadian Appliance Source",
            "partnerLogo": {
                "url": "https://uat.cdn.airmilesapis.ca/partner-logo/7m10wxejlkq7/6WcyNeKqlsZEmeiZ1cKxjt/788f1794ab91af196a082a09a06ec7c2/CAS---EN.jpg"
            },
            "partnerProfileURL": "https://sandbox-beta.airmiles.ca/en/offers/partners/canadian_appliance_source.html",
            "categoryId": "cc1b19d6-72e0-47bc-8b81-2b1fbb49d050",
            "categoryLabel": "Other",
            "awardShort": "1 Bonus Miles",
            "qualifierShort": "Buy 1 test in-store*",
            "image": {
                "url": "https://uat.cdn.airmilesapis.ca/offer-image/processed-images/f18c0acf-765d-4a11-9b09-20101603e594.png"
            },
            "displayDate": "2025-07-08T00:00:00",
            "startDate": "2025-07-08T00:00:00",
            "endDate": "2025-08-09T23:59:59",
            "programType": "traditionalcore",
            "massOffer": true,
            "displayPriority": 0,
            "tiers": [
                {
                    "awardValue": 1.0,
                    "qualifierValue": 1.0,
                    "awardLong": "1 Bonus Miles",
                    "qualifierLong": "Buy 1 test in-store*"
                }
            ],
            "mechanisms": [
                {
                    "mechanismType": "optIn"
                }
            ],
            "states": [
                {
                    "name": "OPT_IN",
                    "value": "OPTED_IN",
                    "updatedAt": "2025-07-08T20:54:09.800950Z"
                },
                {
                    "name": "SAVE",
                    "value": "SAVED",
                    "updatedAt": "2025-07-08T20:54:09.800932Z"
                }
            ],
            "legalText": "* Offer valid from July 8, 2025 to August 9, 2025. Valid at participating Canadian Appliance Source locations in Alberta, British Columbia, Manitoba, New Brunswick, Newfoundland and Labrador, Northwest Territories, Nova Scotia, Nunavut, Ontario, Prince Edward Island, Quebec, Saskatchewan, Thunder Bay and Yukon. Opting in to the offer is required before using the AIR MILES Card at the time of purchase. Minimum eligible purchase must be spent in a single transaction. While supplies last. Product availability may vary by store. We reserve the right to limit quantities. AIR MILES Card must be presented at the time of the purchase. Can be combined with other offers, and AIR MILES offers. ®™ Trademarks of AM Royalties Limited Partnership used under license by AIR MILES Loyalty Inc. Partner, Supplier and Retailer trademarks are owned by the respective Partner, Supplier or Retailer or authorized for their use in Canada.",
            "eventBasedOffer": false
        },
        {
            "id": "2f857209-7243-408c-aec9-8dc37235dcad",
            "partnerId": "63e8543d-fbf7-458e-8712-d25332da3614",
            "partnerLabel": "AIR MILES Travel",
            "partnerLogo": {
                "url": "https://uat.cdn.airmilesapis.ca/partner-logo/7m10wxejlkq7/26y8GPqCJuBBrbMNRXgt1/309bddd73c7eff822a195d148509e7c9/TRAVEL_ICON_EN-66-.png"
            },
            "partnerProfileURL": "https://sandbox-beta.airmiles.ca/en/offers/partners/air-miles-travel.html",
            "categoryId": "22b666ae-97c2-4e57-a437-e977de6beef4",
            "categoryLabel": "Grocery",
            "promotionId": "905350da-27a4-11ec-9621-0242ac130002",
            "promotionLabel": "BONUS BOOM Boost",
            "awardShort": "50x Miles",
            "qualifierShort": "Spend $1000+ on almost anything in-store or online*",
            "image": {
                "url": "https://uat.cdn.airmilesapis.ca/offer-image/processed-images/d8e6394f-c00a-4220-81ad-91ad748bf6bb.jpg"
            },
            "displayDate": "2025-03-12T00:00:00",
            "startDate": "2025-03-13T00:00:00",
            "endDate": "2025-11-30T23:59:59",
            "programType": "traditionalcore",
            "massOffer": true,
            "displayPriority": 1000,
            "tiers": [
                {
                    "awardValue": 50.0,
                    "qualifierValue": 1000.0,
                    "awardLong": "50x Miles",
                    "qualifierLong": "Spend $1000+ on almost anything in-store or online*"
                }
            ],
            "mechanisms": [
                {
                    "mechanismType": "optIn"
                }
            ],
            "states": [
                {
                    "name": "SAVE",
                    "value": "SAVED",
                    "updatedAt": "2025-07-08T20:46:59.785461Z"
                },
                {
                    "name": "OPT_IN",
                    "value": "OPTED_IN",
                    "updatedAt": "2025-07-08T20:46:59.785472Z"
                }
            ],
            "legalText": "* Offer valid from March 13, 2025 to November 30, 2025. Valid at participating AIR MILES Travel locations in Alberta, British Columbia, Manitoba, New Brunswick, Newfoundland and Labrador, Northwest Territories, Nova Scotia, Nunavut, Ontario, Prince Edward Island, Quebec, Saskatchewan, Thunder Bay and Yukon or on https://www.airmiles.ca/en/offers/all-offers.html. Opting in to the offer is required before using the AIR MILES Card at the time of purchase. Minimum eligible purchase must be spent in a single transaction. Multiplier offer applies to standard base offer of 1 Mile for every $20.00 purchase. AIR MILES Card must be presented at the time of the purchase. Can be combined with other offers, and AIR MILES offers. ®™ Trademarks of AM Royalties Limited Partnership used under license by AIR MILES Loyalty Inc. Partner, Supplier and Retailer trademarks are owned by the respective Partner, Supplier or Retailer or authorized for their use in Canada.",
            "eventBasedOffer": false
        },
        {
            "id": "83c1ed9c-6da4-4734-a80f-e82096f8c860",
            "partnerId": "63e8543d-fbf7-458e-8712-d25332da3614",
            "partnerLabel": "AIR MILES Travel",
            "partnerLogo": {
                "url": "https://uat.cdn.airmilesapis.ca/partner-logo/7m10wxejlkq7/26y8GPqCJuBBrbMNRXgt1/309bddd73c7eff822a195d148509e7c9/TRAVEL_ICON_EN-66-.png"
            },
            "partnerProfileURL": "https://sandbox-beta.airmiles.ca/en/offers/partners/air-miles-travel.html",
            "categoryId": "22b666ae-97c2-4e57-a437-e977de6beef4",
            "categoryLabel": "Grocery",
            "promotionId": "26dd100d-9cb8-43cd-965e-b7b284ae8a92",
            "promotionLabel": "BAG-A-BONUS",
            "awardShort": "299 Bonus Miles",
            "qualifierShort": "Spend $49+ on Products test in-store or online*",
            "image": {
                "url": "https://uat.cdn.airmilesapis.ca/offer-image/processed-images/3517aa2d-b513-4e06-90a3-bd4a66ad7d91.jpg"
            },
            "displayDate": "2025-03-12T00:00:00",
            "startDate": "2025-03-12T00:00:00",
            "endDate": "2025-11-30T23:59:59",
            "programType": "traditionalcore",
            "massOffer": true,
            "displayPriority": 1000,
            "tiers": [
                {
                    "awardValue": 299.0,
                    "qualifierValue": 49.0,
                    "awardLong": "299 Bonus Miles",
                    "qualifierLong": "Spend $49+ on Products test in-store or online*"
                }
            ],
            "mechanisms": [
                {
                    "mechanismType": "optIn"
                }
            ],
            "states": [
                {
                    "name": "SAVE",
                    "value": "SAVED",
                    "updatedAt": "2025-07-08T20:35:57.860095Z"
                },
                {
                    "name": "OPT_IN",
                    "value": "OPTED_IN",
                    "updatedAt": "2025-07-08T20:35:57.860117Z"
                }
            ],
            "legalText": "* Offer valid from March 12, 2025 to November 30, 2025. Valid at participating AIR MILES Travel locations in Alberta, British Columbia, Manitoba, New Brunswick, Newfoundland and Labrador, Northwest Territories, Nova Scotia, Nunavut, Ontario, Prince Edward Island, Quebec, Saskatchewan, Thunder Bay and Yukon or on https://www.airmiles.ca/en/offers/all-offers.html. Opting in to the offer is required before using the AIR MILES Card at the time of purchase. Minimum eligible purchase must be spent in a single transaction. AIR MILES Card must be presented at the time of the purchase. Can be combined with other offers, and AIR MILES offers. ®™ Trademarks of AM Royalties Limited Partnership used under license by AIR MILES Loyalty Inc. Partner, Supplier and Retailer trademarks are owned by the respective Partner, Supplier or Retailer or authorized for their use in Canada.",
            "eventBasedOffer": false
        },
        {
            "id": "0651b3e6-0882-4a6d-9753-6f88d39e998d",
            "partnerId": "b90e694d-3510-4cc2-84be-f9b6f61bd31b",
            "partnerLabel": "Shell Mobility",
            "partnerLogo": {
                "url": "https://uat.cdn.airmilesapis.ca/partner-logo/7m10wxejlkq7/sLgosCGl6CiU6cOOOwsmE/59835e650a4c920063d881d83b8f33a8/Shell_RGB_updated.png"
            },
            "partnerProfileURL": "https://sandbox-beta.airmiles.ca/en/offers/partners/shell-retail-canada.html",
            "categoryId": "22b666ae-97c2-4e57-a437-e977de6beef4",
            "categoryLabel": "Grocery",
            "subCategoryId": "be58904e-4ae9-49fc-ac6d-0e966dc193bb",
            "subCategoryLabel": "Pantry",
            "promotionId": "4db99143-1768-4f85-a44a-d6fa16011f7b",
            "promotionLabel": "Mega Miles",
            "awardShort": "Up to 30 Bonus Miles",
            "qualifierShort": "Buy 3 Tier 3 test in-store*",
            "image": {
                "url": "https://uat.cdn.airmilesapis.ca/offer-image/processed-images/801a30f8-02a8-498f-9178-f80bebb4a17e"
            },
            "displayDate": "2020-02-10T00:00:00",
            "startDate": "2025-08-02T00:00:00",
            "endDate": "2050-02-10T23:59:59",
            "programType": "traditionalcore",
            "massOffer": true,
            "displayPriority": 1000,
            "tiers": [
                {
                    "awardValue": 10.0,
                    "qualifierValue": 1.0,
                    "awardLong": "10 Bonus Miles",
                    "qualifierLong": "Buy 1 Single Offer Test in-store*"
                },
                {
                    "awardValue": 20.0,
                    "qualifierValue": 2.0,
                    "awardLong": "20 Bonus Miles",
                    "qualifierLong": "Buy 2 Tier 2 test in-store*"
                },
                {
                    "awardValue": 30.0,
                    "qualifierValue": 3.0,
                    "awardLong": "30 Bonus Miles",
                    "qualifierLong": "Buy 3 Tier 3 test in-store*"
                }
            ],
            "mechanisms": [
                {
                    "mechanismType": "noAction"
                }
            ],
            "states": [
                {
                    "name": "OPT_IN",
                    "value": "OPTED_IN",
                    "updatedAt": "2025-07-08T19:53:26.297883Z"
                },
                {
                    "name": "SAVE",
                    "value": "SAVED",
                    "updatedAt": "2025-07-08T19:53:26.297873Z"
                }
            ],
            "legalText": "* Offer valid from August 2, 2025 to February 10, 2050. Valid at participating ACE locations in Alberta, British Columbia, Manitoba, Ontario, Quebec, Saskatchewan and Thunder Bay. Minimum eligible purchase must be spent in a single transaction. While supplies last. Product availability may vary by store. We reserve the right to limit quantities. AIR MILES Card must be presented at the time of the purchase. Can be combined with other offers, and AIR MILES offers. ®™ Trademarks of AM Royalties Limited Partnership used under license by AIR MILES Loyalty Inc. Partner, Supplier and Retailer trademarks are owned by the respective Partner, Supplier or Retailer or authorized for their use in Canada.",
            "eventBasedOffer": false,
            "ctaLabel": {
                "en-US": "english label"
            },
            "ctaUrl": {
                "en-US": "https://testCTA.com/English"
            }
        },
        {
            "id": "70aae1b0-c31b-4f2b-9a62-76951eb20d02",
            "partnerId": "4a755252-876b-478e-9440-42961525e307",
            "partnerLabel": "Metro",
            "partnerLogo": {
                "url": "https://uat.cdn.airmilesapis.ca/partner-logo/7m10wxejlkq7/7rS32o94OWeIKE2OY0sQiu/df3202c1bd940a599d987c56e4f5d4b7/Metro.png_dl_1"
            },
            "partnerProfileURL": "https://sandbox-beta.airmiles.ca/en/offers/partners/metro.html",
            "categoryId": "0f597ee1-16b8-4a4f-beb7-4654cfd0b45a",
            "categoryLabel": "Pharmacy",
            "promotionId": "0f91f291-dc7c-45d3-9a76-fbdadc6cd836",
            "promotionLabel": "Bonus Boom",
            "awardShort": "8 Bonus Miles",
            "qualifierShort": "Buy 7 testing product 8.3 in-store*",
            "image": {
                "url": "https://uat.cdn.airmilesapis.ca/offer-image/processed-images/4c57e7a2-18d6-4c00-a92b-116e55206965.png"
            },
            "displayDate": "2025-07-07T00:00:00",
            "startDate": "2025-07-07T00:00:00",
            "endDate": "2025-09-30T23:59:59",
            "programType": "cardlinked",
            "massOffer": true,
            "displayPriority": 1000,
            "tiers": [
                {
                    "awardValue": 8.0,
                    "qualifierValue": 7.0,
                    "awardLong": "8 Bonus Miles",
                    "qualifierLong": "Buy 7 testing product 8.3 in-store*"
                }
            ],
            "mechanisms": [
                {
                    "mechanismType": "optIn"
                }
            ],
            "states": [
                {
                    "name": "SAVE",
                    "value": "SAVED",
                    "updatedAt": "2025-07-18T14:42:18.571145Z"
                },
                {
                    "name": "OPT_IN",
                    "value": "OPTED_IN",
                    "updatedAt": "2025-07-18T14:42:18.571154Z"
                }
            ],
            "legalText": "* Offer valid from July 7, 2025 to September 30, 2025. Valid at participating Metro locations in Alberta, Ontario and Prince Edward Island. Opting in to the offer is required before using the AIR MILES Card at the time of purchase. Minimum eligible purchase must be spent in a single transaction. While supplies last. Product availability may vary by store. We reserve the right to limit quantities. AIR MILES Card must be presented at the time of the purchase. Can be combined with other offers, and AIR MILES offers. ®™ Trademarks of AM Royalties Limited Partnership used under license by AIR MILES Loyalty Inc. Partner, Supplier and Retailer trademarks are owned by the respective Partner, Supplier or Retailer or authorized for their use in Canada.",
            "cardType": [
                "NonBmoMastercard",
                "BmoMastercard"
            ],
            "eventBasedOffer": false,
            "ctaLabel": {
                "en-US": "English CTA"
            },
            "ctaUrl": {
                "en-US": "https://www.google.com/?hl=co"
            }
        },
        {
            "id": "a67cbea5-f40b-4c62-9e0e-5c2b0ae14c10",
            "partnerId": "3267588b-791d-49bc-a321-d85d5f818480",
            "partnerLabel": "AIR MILES",
            "partnerLogo": {
                "url": "https://uat.cdn.airmilesapis.ca/partner-logo/7m10wxejlkq7/69ZErUapt6c8WQioe6iYSS/5107f862d6a2b238ff6f7739c5a90992/AIRMILES_PLANE_ELECTRIC_BLUE_RGB_E.png"
            },
            "partnerProfileURL": "https://sandbox-beta.airmiles.ca/en/offers/partners/air-miles-reward-program-bonus-offers.html",
            "categoryId": "0cbc9203-2811-4eae-9dc1-68da692658b8",
            "categoryLabel": "Financial",
            "promotionId": "0f91f291-dc7c-45d3-9a76-fbdadc6cd836",
            "promotionLabel": "Bonus Boom",
            "awardShort": "33 Bonus Miles",
            "qualifierShort": "Buy 3 OPT-IN TEST UAT 39 in-store*",
            "image": {
                "url": "https://uat.cdn.airmilesapis.ca/offer-image/processed-images/aa9fba90-7fc0-4a21-a790-b05dbbc8eb42"
            },
            "displayDate": "2025-01-24T00:00:00",
            "startDate": "2025-02-01T00:00:00",
            "endDate": "2025-09-26T23:59:59",
            "programType": "traditionalcore",
            "massOffer": true,
            "displayPriority": 0,
            "tiers": [
                {
                    "awardValue": 33.0,
                    "qualifierValue": 3.0,
                    "awardLong": "33 Bonus Miles",
                    "qualifierLong": "Buy 3 OPT-IN TEST UAT 39 in-store*"
                }
            ],
            "mechanisms": [
                {
                    "mechanismType": "optIn"
                }
            ],
            "states": [
                {
                    "name": "SAVE",
                    "value": "SAVED",
                    "updatedAt": "2025-07-11T14:44:12.556178Z"
                },
                {
                    "name": "OPT_IN",
                    "value": "OPTED_IN",
                    "updatedAt": "2025-07-11T14:44:12.556351Z"
                }
            ],
            "legalText": "* Offer valid from February 1, 2025 to September 26, 2025. Valid at participating AIR MILES locations in Alberta, British Columbia, Manitoba, New Brunswick, Newfoundland and Labrador, Northwest Territories, Nova Scotia, Nunavut, Ontario, Prince Edward Island, Quebec, Saskatchewan, Thunder Bay and Yukon. Opting in to the offer is required before using the AIR MILES Card at the time of purchase. Minimum eligible purchase must be spent in a single transaction. While supplies last. Product availability may vary by store. We reserve the right to limit quantities. AIR MILES Card must be presented at the time of the purchase. Can be combined with other offers, and AIR MILES offers. ®™ Trademarks of AM Royalties Limited Partnership used under license by AIR MILES Loyalty Inc. Partner, Supplier and Retailer trademarks are owned by the respective Partner, Supplier or Retailer or authorized for their use in Canada.",
            "eventBasedOffer": false
        },
        {
            "id": "bf3e4874-e012-4f47-810a-af1999cd4b97",
            "partnerId": "22a2cdfd-ff82-45f6-bc94-c14a3a533922",
            "partnerLabel": "BMO Bank of Montreal",
            "partnerLogo": {
                "url": "https://uat.cdn.airmilesapis.ca/partner-logo/7m10wxejlkq7/6b9YYxL0SiRMY7ijajn51C/3b0bd9f225bb5e48ed2db8ba258e6651/BMO-logo_2_512x512_2023.png"
            },
            "partnerProfileURL": "https://sandbox-beta.airmiles.ca/en/offers/partners/bmo-bank-of-montreal.html",
            "categoryId": "1ba636e3-799d-44c3-9c84-42a3df6430b0",
            "categoryLabel": "Household Supplies",
            "promotionId": "905350da-27a4-11ec-9621-0242ac130002",
            "promotionLabel": "BONUS BOOM Boost",
            "awardShort": "1999 Bonus Miles",
            "qualifierShort": "Buy 10 Shampoo in-store or online*",
            "image": {
                "url": "https://uat.cdn.airmilesapis.ca/offer-image/processed-images/29f1f6cc-aee8-4dda-a0d7-700d65514f55.jpg"
            },
            "displayDate": "2025-03-14T00:00:00",
            "startDate": "2025-03-14T00:00:00",
            "endDate": "2025-10-31T23:59:59",
            "programType": "bmopreapp",
            "massOffer": true,
            "displayPriority": 1000,
            "tiers": [
                {
                    "awardValue": 1999.0,
                    "qualifierValue": 10.0,
                    "awardLong": "1999 Bonus Miles",
                    "qualifierLong": "Buy 10 Shampoo in-store or online*"
                }
            ],
            "mechanisms": [
                {
                    "mechanismType": "optIn"
                }
            ],
            "states": [
                {
                    "name": "OPT_IN",
                    "value": "OPTED_IN",
                    "updatedAt": "2025-06-12T19:21:02.490746Z"
                },
                {
                    "name": "SAVE",
                    "value": "SAVED",
                    "updatedAt": "2025-06-12T19:21:02.490721Z"
                }
            ],
            "legalText": "* Offer valid from March 14, 2025 to October 31, 2025. Valid at participating BMO Bank of Montreal locations in Alberta, British Columbia, Manitoba, New Brunswick, Newfoundland and Labrador, Northwest Territories, Nova Scotia, Nunavut, Ontario, Prince Edward Island, Quebec, Saskatchewan, Thunder Bay and Yukon or on https://www.airmiles.ca/en/offers/all-offers.html. Opting in to the offer is required before using the AIR MILES Card at the time of purchase. Minimum eligible purchase must be spent in a single transaction. While supplies last. Product availability may vary by store. We reserve the right to limit quantities. AIR MILES Card must be presented at the time of the purchase. Can be combined with other offers, and AIR MILES offers. ®™ Trademarks of AM Royalties Limited Partnership used under license by AIR MILES Loyalty Inc. Partner, Supplier and Retailer trademarks are owned by the respective Partner, Supplier or Retailer or authorized for their use in Canada.",
            "eventBasedOffer": false
        },
        {
            "id": "044d1b72-8b65-4a9e-8aa6-ca0c0d7efe4e",
            "partnerId": "c5cf18fb-383c-4174-96a8-15d9cd99a7cf",
            "partnerLabel": "AIR MILES",
            "partnerLogo": {
                "url": "https://uat.cdn.airmilesapis.ca/partner-logo/7m10wxejlkq7/27DPS9oBOtIHkS0SRgwlt4/db700a6e5a84c55e6b704232e3dc6ee4/AIRMILES_PLANE_ELECTRIC_BLUE_RGB_E.PNG"
            },
            "categoryId": "22b666ae-97c2-4e57-a437-e977de6beef4",
            "categoryLabel": "Grocery",
            "awardShort": "5 Bonus Miles",
            "qualifierShort": "Buy 20 TEST PRODUCT EN in-store*",
            "image": {
                "url": "https://uat.cdn.airmilesapis.ca/offer-image/processed-images/0db2d9a4-61aa-45b9-835d-bd2d2f631cc4.jpg"
            },
            "displayDate": "2025-01-07T00:00:00",
            "startDate": "2025-01-07T00:00:00",
            "endDate": "2025-12-31T23:59:59",
            "programType": "traditionalcore",
            "massOffer": true,
            "displayPriority": 1000,
            "tiers": [
                {
                    "awardValue": 5.0,
                    "qualifierValue": 20.0,
                    "awardLong": "5 Bonus Miles",
                    "qualifierLong": "Buy 20 TEST PRODUCT EN in-store*"
                }
            ],
            "mechanisms": [
                {
                    "mechanismType": "optIn"
                }
            ],
            "states": [
                {
                    "name": "SAVE",
                    "value": "SAVED",
                    "updatedAt": "2025-07-08T20:32:35.670067Z"
                },
                {
                    "name": "OPT_IN",
                    "value": "OPTED_IN",
                    "updatedAt": "2025-07-08T20:32:35.670082Z"
                }
            ],
            "legalText": "* Offer valid from January 7, 2025 to December 31, 2025. Valid at participating AIR MILES locations in Alberta, British Columbia, Manitoba, New Brunswick, Newfoundland and Labrador, Northwest Territories, Nova Scotia, Nunavut, Ontario, Prince Edward Island, Quebec, Saskatchewan, Thunder Bay and Yukon. Opting in to the offer is required before using the AIR MILES Card at the time of purchase. Minimum eligible purchase must be spent in a single transaction. While supplies last. Product availability may vary by store. We reserve the right to limit quantities. AIR MILES Card must be presented at the time of the purchase. Can be combined with other offers, and AIR MILES offers. ®™ Trademarks of AM Royalties Limited Partnership used under license by AIR MILES Loyalty Inc. Partner, Supplier and Retailer trademarks are owned by the respective Partner, Supplier or Retailer or authorized for their use in Canada.",
            "eventBasedOffer": false
        },
        {
            "id": "879a6659-6f61-4f74-b604-edd3d3c44956",
            "partnerId": "7f0a8705-8d25-4413-b868-0849aaeb2e0a",
            "partnerLabel": "Action Car and Truck Accessories",
            "partnerLogo": {
                "url": "https://uat.cdn.airmilesapis.ca/partner-logo/7m10wxejlkq7/4hNzZVBdy6GmOIIk7829SP/c832692c293e946e269d1196b08933fd/ACTION_CAR_AND_TRUCK_4C.png_h_250_h_250"
            },
            "partnerProfileURL": "https://sandbox-beta.airmiles.ca/en/offers/partners/action-car-and-truck-accessories.html",
            "categoryId": "cc1b19d6-72e0-47bc-8b81-2b1fbb49d050",
            "categoryLabel": "Other",
            "promotionId": "784bbe95-5299-475b-b03b-5fb274e206c6",
            "promotionLabel": "Our top picks",
            "awardShort": "2 Bonus Miles",
            "qualifierShort": "Spend $2+ on almost anything in-store*",
            "image": {
                "url": "https://uat.cdn.airmilesapis.ca/offer-image/processed-images/842d404d-0494-4f98-af2d-4ab6541f2870.jpg"
            },
            "displayDate": "2025-06-24T00:00:00",
            "startDate": "2025-06-24T00:00:00",
            "endDate": "2026-06-25T23:59:59",
            "programType": "traditionalcore",
            "massOffer": true,
            "displayPriority": 0,
            "tiers": [
                {
                    "awardValue": 2.0,
                    "qualifierValue": 2.0,
                    "awardLong": "2 Bonus Miles",
                    "qualifierLong": "Spend $2+ on almost anything in-store*"
                }
            ],
            "mechanisms": [
                {
                    "mechanismType": "noAction"
                }
            ],
            "states": [
                {
                    "name": "SAVE",
                    "value": "SAVED",
                    "updatedAt": "2025-07-08T20:40:36.946769Z"
                },
                {
                    "name": "OPT_IN",
                    "value": "OPTED_IN",
                    "updatedAt": "2025-07-08T20:40:36.946801Z"
                }
            ],
            "legalText": "* Offer valid from June 24, 2025 to June 25, 2026. Valid at participating Action Car and Truck Accessories locations in Alberta, British Columbia, Manitoba, New Brunswick, Newfoundland and Labrador, Nova Scotia, Ontario, Prince Edward Island, Quebec and Saskatchewan. Minimum eligible purchase must be spent in a single transaction. AIR MILES Card must be presented at the time of the purchase. Can be combined with other offers, and AIR MILES offers. ®™ Trademarks of AM Royalties Limited Partnership used under license by AIR MILES Loyalty Inc. Partner, Supplier and Retailer trademarks are owned by the respective Partner, Supplier or Retailer or authorized for their use in Canada.",
            "eventBasedOffer": false
        },
        {
            "id": "aaa09c6e-28c5-4aba-92a7-e57dd1ff8d7a",
            "partnerId": "63e8543d-fbf7-458e-8712-d25332da3614",
            "partnerLabel": "AIR MILES Travel",
            "partnerLogo": {
                "url": "https://uat.cdn.airmilesapis.ca/partner-logo/7m10wxejlkq7/26y8GPqCJuBBrbMNRXgt1/309bddd73c7eff822a195d148509e7c9/TRAVEL_ICON_EN-66-.png"
            },
            "partnerProfileURL": "https://sandbox-beta.airmiles.ca/en/offers/partners/air-miles-travel.html",
            "categoryId": "0387d04b-2736-48e4-b804-ac50f4222b79",
            "categoryLabel": "Office",
            "awardShort": "3 Bonus Miles",
            "qualifierShort": "Buy 4 Camera in-store*",
            "image": {
                "url": "https://uat.cdn.airmilesapis.ca/offer-image/processed-images/f16dfea2-b045-45c0-aa3f-1ffc3d19f64a.jpg"
            },
            "displayDate": "2025-03-04T00:00:00",
            "startDate": "2025-03-04T00:00:00",
            "endDate": "2025-09-12T23:59:59",
            "programType": "traditionalcore",
            "massOffer": true,
            "displayPriority": 1000,
            "tiers": [
                {
                    "awardValue": 3.0,
                    "qualifierValue": 4.0,
                    "awardLong": "3 Bonus Miles",
                    "qualifierLong": "Buy 4 Camera in-store*"
                }
            ],
            "mechanisms": [
                {
                    "mechanismType": "optIn"
                }
            ],
            "states": [
                {
                    "name": "SAVE",
                    "value": "SAVED",
                    "updatedAt": "2025-07-09T18:44:12.302946Z"
                },
                {
                    "name": "OPT_IN",
                    "value": "OPTED_IN",
                    "updatedAt": "2025-07-09T18:44:12.302970Z"
                }
            ],
            "legalText": "* Offer valid from March 4, 2025 to September 12, 2025. Valid at participating AIR MILES Travel locations in Alberta, British Columbia, Manitoba, New Brunswick, Newfoundland and Labrador, Northwest Territories, Nova Scotia, Nunavut, Ontario, Prince Edward Island, Quebec, Saskatchewan, Thunder Bay and Yukon. Opting in to the offer is required before using the AIR MILES Card at the time of purchase. Minimum eligible purchase must be spent in a single transaction. While supplies last. Product availability may vary by store. We reserve the right to limit quantities. AIR MILES Card must be presented at the time of the purchase. Can be combined with other offers, and AIR MILES offers. ®™ Trademarks of AM Royalties Limited Partnership used under license by AIR MILES Loyalty Inc. Partner, Supplier and Retailer trademarks are owned by the respective Partner, Supplier or Retailer or authorized for their use in Canada.",
            "eventBasedOffer": false,
            "ctaLabel": {
                "en-US": "Buy products"
            },
            "ctaUrl": {
                "en-US": "https://testcta.com/lander"
            }
        }
    ],
    "metadata": {
        "total": 10
    }
}
```

