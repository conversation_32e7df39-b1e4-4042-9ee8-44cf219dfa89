package com.loyalty.offerservice.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum RegionEnum {

    BC("British Columbia"),
    AB("Alberta"),
    SK("Saskatchewan"),
    MB("Manitoba"),
    ON("Ontario"),
    QC("Quebec"),
    TB("Thunder Bay"),
    NB("New Brunswick"),
    NS("Nova Scotia"),
    PE("Prince Edward Island"),
    NL("Newfoundland and Labrador"),
    NT("Northwest Territories"),
    NU("Nunavut"),
    YT("Yukon"),
    ZZ("for automated tests");

    private final String description;


    public String value() {
        return description;
    }

}
