{"NetworkStackName": "program-interface-uat", "ClusterStackName": "program-interface-uat-fg-ecs", "ListenerPort": "34075", "Environment": "uat", "AppName": "offer-service", "AppVersion": "1.0.0", "AppContainerPort": "8080", "KinesisStackName": "nonprod-kinesissplunk", "AmrpwlAccountId": "************", "ResolverFunctionName": "nova-offer-resolver-api-getOffers-lambda", "ResolverFunctionRegion": "us-east-1", "PagerDutyIntegrationKey": "d132264d93f747baa79ead1a770f1935", "AppDesiredCount": "3", "AppMinCount": "3", "AppMaxCount": "10", "PagerDutyHighCpuThreshold": "90", "PagerDutyMemoryThreshold": "80", "HealthyHostThreshold": "1"}