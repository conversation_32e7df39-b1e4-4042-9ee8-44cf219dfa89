package com.loyalty.offerservice.model.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.loyalty.offerservice.model.dto.response.category.CategoriesResponse;
import com.loyalty.offerservice.model.dto.response.offerdelivery.OfferDeliveryResponse;
import lombok.*;

import java.util.List;

@NoArgsConstructor
@Setter
@Getter
@ToString
public class OfferServiceExtendedOfferResponse {

    private List<OfferServiceResponse> offers;
    private OfferServiceMetadata metadata;

    @Data
    @Builder
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class OfferServiceMetadata {
        private Integer total;
        private List<CommonMetadata.MetadataCounts> partners;
        private List<CategoriesResponse.Category> categories;
    }

}
