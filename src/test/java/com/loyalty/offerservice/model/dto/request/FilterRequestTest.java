package com.loyalty.offerservice.model.dto.request;

import com.loyalty.offerservice.enums.ProgramTypeEnum;
import com.loyalty.offerservice.enums.RegionEnum;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

public class FilterRequestTest {

    private final FilterRequest originalFilterRequest = FilterRequest.builder()
            .region(RegionEnum.AB)
            .partnerId(List.of(UUID.fromString("5a493e01-56b8-491a-bd6e-a6088740f1c0")))
            .categoryId(List.of(UUID.fromString("0d9cce67-2e72-4f0c-af17-a634702ebb00")))
            .subCategoryId(List.of(UUID.fromString("8f314c5f-217d-4028-a9fe-0420398ab040")))
            .promotionId(UUID.fromString("99a936a1-317d-4aa4-827c-1e4de09123d0"))
            .type(List.of("type0"))
            .programType(List.of(ProgramTypeEnum.bmopreapp))
            .build();

    private final FilterRequest overrideFilterRequest = FilterRequest.builder()
            .region(RegionEnum.ON)
            .partnerId(List.of(UUID.fromString("5a493e01-56b8-491a-bd6e-a6088740f1c1")))
            .categoryId(List.of(UUID.fromString("0d9cce67-2e72-4f0c-af17-a634702ebb01")))
            .subCategoryId(List.of(UUID.fromString("8f314c5f-217d-4028-a9fe-0420398ab041")))
            .promotionId(UUID.fromString("99a936a1-317d-4aa4-827c-1e4de09123d1"))
            .type(List.of("type1"))
            .programType(List.of(ProgramTypeEnum.cardlinked))
            .build();

    @Test
    public void testOverrideFilterRequest() {
        FilterRequest overridden = originalFilterRequest.override(overrideFilterRequest);

        assertEquals(overridden, overrideFilterRequest);
    }

    @Test
    public void testOverrideFilterRequestWithEmpty() {
        FilterRequest overridden = originalFilterRequest.override(FilterRequest.builder().build());

        assertEquals(overridden, originalFilterRequest);
    }

    @Test
    public void testCustomEqualsMethodEmptyRequests() {
        FilterRequest requestA = FilterRequest.builder().build();
        FilterRequest requestB = FilterRequest.builder().build();

        assertEquals(requestA, requestB);
    }

    @Test
    public void testCustomEqualsMethodDifferentRequests() {
        assertNotEquals(originalFilterRequest, overrideFilterRequest);
    }

    @Test
    public void testCustomEqualsMethodSameRequest() {
        FilterRequest clonedRequest = originalFilterRequest.toBuilder().build();

        assertEquals(originalFilterRequest, clonedRequest);
    }
}