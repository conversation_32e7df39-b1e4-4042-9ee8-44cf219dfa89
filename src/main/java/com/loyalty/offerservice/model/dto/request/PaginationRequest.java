package com.loyalty.offerservice.model.dto.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;

import java.util.List;
import java.util.Objects;

@Getter
@AllArgsConstructor
@Setter
@ToString
@Builder(toBuilder = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PaginationRequest {

    private List<String> sort;
    private Integer limit;
    private Integer offset;

    /**
     * Takes an instance of PaginationRequest and overrides the original instance's
     * attributes with the param's non-null attributes.
     *
     * @param overrides PaginationRequest object to replace original object's
     *                  attributes
     * @return a PaginationRequest object overridden with the param's values
     */
    public PaginationRequest override(PaginationRequest overrides) {
        if (overrides == null)
            return this;

        return this.toBuilder().sort(overrides.sort != null ? overrides.sort : this.sort)
                .limit(overrides.limit != null ? overrides.limit : this.limit)
                .offset(overrides.offset != null ? overrides.offset : this.offset)
                .build();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;

        if (o == null || getClass() != o.getClass())
            return false;

        PaginationRequest that = (PaginationRequest) o;
        return Objects.equals(sort, that.sort) && Objects.equals(limit, that.limit)
                && Objects.equals(offset, that.offset);
    }

}
