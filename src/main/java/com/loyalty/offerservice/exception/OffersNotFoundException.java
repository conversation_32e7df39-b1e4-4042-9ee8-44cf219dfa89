package com.loyalty.offerservice.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
public class OffersNotFoundException extends AppRuntimeException{

    public OffersNotFoundException(String errorCode, String message) {
        super(errorCode, message);
    }

    public OffersNotFoundException(String errorCode, String message, Throwable cause) {
        super(errorCode, message, cause);
    }
}
