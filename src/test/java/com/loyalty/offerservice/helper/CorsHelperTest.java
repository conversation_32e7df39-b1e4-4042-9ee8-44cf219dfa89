package com.loyalty.offerservice.helper;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

@SpringBootTest
public class CorsHelperTest {

    @Autowired
    CorsHelper corsHelper;

    @Test
    void testAllowedOrigins_givenNotAllowedOrigin() {
        // Not Allowed
        String notAllowedOrigin = "NotAllowed";
        boolean isAllowed = corsHelper.isAllowedOrigin(notAllowedOrigin);
        assertFalse(isAllowed);
    }

    @ParameterizedTest()
    @ValueSource(strings = {"https://travel2.airmiles.ca","https://ams.airmiles.ca","https://dev.airmilesshops.ca"})
    void testAllowedOrigins_givenAllowedOrigin(String origin) {
        // Allowed
        boolean isAllowed = corsHelper.isAllowedOrigin(origin);
        assertTrue(isAllowed);
    }
}
