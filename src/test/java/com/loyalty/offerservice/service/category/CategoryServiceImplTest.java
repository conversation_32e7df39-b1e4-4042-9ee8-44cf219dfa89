package com.loyalty.offerservice.service.category;

import com.loyalty.offerservice.model.dto.response.CommonMetadata;
import com.loyalty.offerservice.model.dto.response.category.CategoriesResponse;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;

import java.util.List;
import java.util.UUID;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class CategoryServiceImplTest {

    @Autowired
    CategoryServiceImpl categoryService;

    @Test
    void getCategoriesEnglishTest() throws Exception {
        String locale = "en-US";

        CategoriesResponse response = categoryService.getCategories(locale);
        assertEquals("Apparel", response.getResults().get(0).getLabel());
    }

    @Test
    void getCategoriesFrenchTest() throws Exception {
        String locale = "fr-CA";

        CategoriesResponse response = categoryService.getCategories(locale);
        assertEquals("Vêtements", response.getResults().get(0).getLabel());
    }

    @Test
    void setMultipleCategoriesCountTest() throws Exception {
        String locale = "en-US";

        CategoriesResponse response = categoryService.getCategories(locale);

        // Mock Data to set counts
        CommonMetadata.MetadataCounts metadataCounts = new CommonMetadata.MetadataCounts();
        metadataCounts.setId(UUID.fromString("22b666ae-97c2-4e57-a437-e977de6beef4"));
        metadataCounts.setCount(10L);

        CommonMetadata.MetadataCounts metadataCounts2 = new CommonMetadata.MetadataCounts();
        metadataCounts2.setId(UUID.fromString("38d878d7-c3e0-4ea1-8e6c-d4759f307042"));
        metadataCounts2.setCount(100L);

        CommonMetadata.MetadataCounts metadataCounts3 = new CommonMetadata.MetadataCounts();
        metadataCounts3.setId(UUID.fromString("873c7bfa-745e-4bdd-afe2-55425821d721"));
        metadataCounts3.setCount(99L);
        CommonMetadata commonMetadata = CommonMetadata.builder().categories((List.of(metadataCounts, metadataCounts2)))
                .subCategories(List.of(metadataCounts3)).build();

        // Using the mock data from above set counts into the category response
        response.setCounts(commonMetadata);


        //Verification
        List<String> expectedIds = List.of("22b666ae-97c2-4e57-a437-e977de6beef4","38d878d7-c3e0-4ea1-8e6c-d4759f307042","873c7bfa-745e-4bdd-afe2-55425821d721");
        List<Integer> expectedCounts = List.of(10,100,99);

        Stream<CategoriesResponse.Category> categoryData = response.getResults().stream().flatMap(
                data -> Stream.concat(Stream.of(data), data.getSubCategories().stream())
        );

        categoryData.filter(
                data -> data.getCount() > 0
        ).forEach(
                data -> {
                    int index = expectedIds.indexOf(data.getId().toString());
                    System.out.println("For each " + index +" " + data.toString());
                    assertEquals(expectedCounts.get(index),data.getCount());
                }
        );
    }
}
