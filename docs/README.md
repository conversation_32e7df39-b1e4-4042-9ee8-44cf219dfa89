# Offer-Service
Welcome to the Springboot Starter-Kit!

Lets get started!

Further information about starter kits (such as structure, and file contents) can be found here:  
https://loyaltyone.atlassian.net/wiki/spaces/DevStarterKit/pages/65700112/DSK+Templates

## Repository Initialization 

<details>
   <summary>Expand</summary>
  
   After the new starterkit repository has been created, to make it work with CI/CD pipelines, the following tasks should be completed.

   ### Codeowners

   Update the `CODEOWNERS` file in the `.github/` folder with the name of the team that will be responsible for the contents of the repo.  This team will be notfied about pull requests and will be responsible for reviewing those requests.
   

   example of `.github/CODEOWNERS`

      # Documentation
      # https://help.github.com/en/github/creating-cloning-and-archiving-repositories/about-code-owners
      
      # These owners will be the default owners for everything in
      # the repo. Unless a later match takes precedence,
      # these owners will be requested for review when 
      # someone opens a pull request.
      *       @LoyaltyOne/[PUT_GITHUB_TEAM_NAME_HERE]


   ### Environment Configuration

   Environment specific variables are stored in files in the `env/` folder.

   Each file is prefixed with the short name of the environment.  For example dev, int, uat, load, cert, prod

   One set of three files is required for each environment.

   Environment Files
   Name | Contents
   ---- | -----------
   [env].env | a list of environment variables which will be passed to the ECS container on startup
   [env].params.json | values for parameters in the CF deployment templates
   [env].tags.json | a list of tags to apply to the CF stack

   See the include dev environment files for examples

</details>


## Local setup

<details>
   <summary>Expand</summary>

   To start the application locally esure you have all required prerequisties

   - Install Java17 (You can install Java using [homebrew](https://brew.sh/) and manage using [jenv](https://github.com/jenv/jenv) for Mac) [install guide for windows](https://docs.oracle.com/en/java/javase/17/install/installation-jdk-microsoft-windows-platforms.html#GUID-A7E27B90-A28D-4237-9383-A58B416071CA)
   - Install [Docker Desktop](https://www.docker.com/products/docker-desktop) to run the dockerfile
   
   - Clone this repo and change directory to the root of the repo.
   ```
      git clone https://github.com/LoyaltyOne/offer-service.git
      OR
      <NAME_EMAIL>:LoyaltyOne/offer-service.git

      cd /offer-service
   ```
  
   - Compile and start the application 
   ```
   ./gradlew bootRun --args='--spring.profiles.active=local'
   ```
   - Test the API (open a new terminal window)
      ```
      curl -sb -H "Accept: application/json" "http://localhost:8080/offer-service/health"
      ...
      {"applicationName":"offer-service","status":"Success"}  
      ```
     
   - When you are done testing you can close the application session
   ```
   Mac: Ctrl + C      
   Windows:Ctrl + C
   ```

   ### Compile the application
   ```
   Linux/Mac: ./gradlew build
   Windows: .\gradlew.bat build
   ```
   The application will be build under
   ```
   build/libs/
   ```

   ### Build with Docker
   ```
   docker build --tag myapi:latest .
   ```

   ### Run Docker image
   ```
   docker run -it --rm -p 8080:8080 --name api myapi
   ```
   ### Stop the container
   ```
   Ctrl + c
   ```
   or

   ```
   docker stop $(docker ps -qf name=api)
   ```

</details>


## Configuration Changes for CD

<details>
   <summary>Expand</summary>

   ```
   Modify env\[env].params.json, env\[env].tags.json, aws\cfn\service.yaml, env\service.json
   ```
   aws\cfn\services.yaml and env\service.json

      - NetworkConfiguration: Ensure Imported Subnet Naming matches the account you are deploying to (App Subnet)
      - SecurityGroupIngress: Ensure the imported CIDR IPs are matching with the account (LoadBalancer Subnet)
      - Check out this guide: https://confluence.loyalty.com/display/AWS/Find+Network+Stack+Information
   
   env\[env]-infra.params.json

      - Parameters to deploy the infra resources
   
   env\[env].tags.json

      - set tags for app resources

   env\[env].params.json

      - set import parameters for services.yaml(json) CFN template
      - set scheduler to shut dowrn the services in nonprod environments after office hours. For more details: https://loyaltyone.atlassian.net/wiki/spaces/AA/pages/********/AWS+Tagging+Standard

</details>


## Versioning 📦

<details>
   <summary>Expand</summary>

   Versioning is fully automated and managed by [semantic-release](https://github.com/semantic-release/semantic-release).

   ### How does it work?

   #### Commit message format

   **semantic-release** uses the commit messages to determine the type of changes in the codebase. Following formalized conventions for commit messages, **semantic-release** automatically determines the next [semantic version](https://semver.org) number, generates a changelog and publishes the release.

   By default **semantic-release** uses [Angular Commit Message Conventions](https://github.com/angular/angular.js/blob/master/DEVELOPERS.md#-git-commit-guidelines). The commit message format can be changed with the [`preset` or `config` options](docs/usage/configuration.md#options) of the [@semantic-release/commit-analyzer](https://github.com/semantic-release/commit-analyzer#options) and [@semantic-release/release-notes-generator](https://github.com/semantic-release/release-notes-generator#options) plugins.

   Tools such as [commitizen](https://github.com/commitizen/cz-cli) or [commitlint](https://github.com/conventional-changelog/commitlint) can be used to help contributors and enforce valid commit messages.

   Here is an example of the release type that will be done based on a commit messages:

   | Commit message                                                                                                                                                                                   | Release type               |
   | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | -------------------------- |
   | `fix(pencil): stop graphite breaking when too much pressure applied`                                                                                                                             | Patch Release              |
   | `feat(pencil): add 'graphiteWidth' option`                                                                                                                                                       | ~~Minor~~ Feature Release  |
   | `perf(pencil): remove graphiteWidth option`<br><br>`BREAKING CHANGE: The graphiteWidth option has been removed.`<br>`The default graphite width of 10mm is always used for performance reasons.` | ~~Major~~ Breaking Release |

</details>


## Scorecard
[scorecard.md](scorecard.md)


## Enable / Disable Dependabot

- Please see following link for enable / disable dependabot
  https://docs.github.com/en/code-security/dependabot/dependabot-security-updates/configuring-dependabot-security-updates


## Skip CERT stage deployment

To bypass CERT stage deployment, please set `ENV_CERT=false` in file meta.config. The default value is `ENV_CERT=true`, enabling CERT stage deployment


## Tips
   
   - Please make sure commit messages are properly formatted according to the semantic release standards mentioned above
   - Windows Users: highly recommend installing gitbash to have a similar experience to Linux/ Mac Users
   - See https://spring.io/guides/gs/spring-boot/#scratch for more information
   - In order to be better stewards of this pipeline, please ensure that any modifications to your Jenkinsfiles involving adding 'input' steps (a.k.a. Approval Gates) do not consume any agents/executors in Jenkins. Failing to do this will add uneccessary congestion to Jenkins, and dramatically increase lead deployment time into production for other teams. 
