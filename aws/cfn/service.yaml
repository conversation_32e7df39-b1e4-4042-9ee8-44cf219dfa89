AWSTemplateFormatVersion: '2010-09-09'
Description: Creates a service / task def for sample play
Parameters:
  NetworkStackName:
    Type: String
  ClusterStackName:
    Description: Name of an active CloudFormation stack that contains an ECS cluster
    Type: String
    MinLength: 1
    MaxLength: 255
    AllowedPattern: ^[a-zA-Z][-a-zA-Z0-9]*$
  ListenerPort:
    Type: Number
    Description: The port to register with the Load Balancer
  AppName:
    Type: String
    Description: Name of app. Should be the same as docker repository name.
  Environment:
    Description: Type of environment
    Type: String
    AllowedValues: [ dev, int, load, uat, sandbox, cert, prod ]
  AppVersion:
    Type: String
    Description: Version label of app
  AppContainerPort:
    Type: Number
    Description: Port the app runs on in the image
  KinesisStackName:
    Type: String
    Description: The name of Kinesis which is unique for each environment, used for
      logging to splunk
  ContainerTotalMemory:
    Type: Number
    Description: Soft memory of all containers (secure-proxy uses 512mb of this by default)
    Default: 1024
  ContainerTotalCpu:
    Type: Number
    Description: cpu units of all containers (secure-proxy uses 256 of this by default)
    Default: 512
  AppMemory:
    Type: Number
    Description: Soft memory of app container
    Default: 512
  AppCpu:
    Type: Number
    Description: cpu units of app container
    Default: 256
  AppDesiredCount:
    Type: Number
    Description: Number of instances of the service to run
    Default: '1'
  AppMaxCount:
    Type: Number
    Description: Max number of instances of the service to scale out to
    Default: '1'
  AppMinCount:
    Type: Number
    Description: Min number of instances of the service to scale in to
    Default: '1'
  PagerDutyHighCpuThreshold:
    Type: Number
    Description: Percentage of cpu utilization that is considered high
    Default: '90'
  PagerDutyMemoryThreshold:
    Type: Number
    Description: Percentage of memory utilization that is considered high
    Default: '80'
  HealthyHostThreshold:
    Type: Number
    Description: healthy host alarm threshold
    Default: '1'
  AutoScaleHighThreshold:
    Type: Number
    Description: Percentage of service memory utilization that is considered high
    Default: '70'
  AutoScaleLowThreshold:
    Type: Number
    Description: Percentage of service memory utilization that is considered low
    Default: '20'
  LogRetention:
    Type: Number
    Description: Number of days to retain logs in CWL
    Default: '14'
  PagerDutyIntegrationKey:
    Type: String
    Description: The PagerDuty key for this service to forward pager alarms
    Default: ""
  ResolverFunctionName:
    Type: String
    Description: The resolver-lambda function ARN
  ResolverFunctionRegion:
    Type: String
    Description: The AWS Region where resolver-lambda is running
  AmrpwlAccountId:
    Type: String
    Description: The AWS Account ID where resolver-lambda is running

Conditions:
  CreatePagerDutyAlarms: !Not [ !Equals [ !Ref PagerDutyIntegrationKey, "" ] ]
  IsDevEnvironment: !Equals [ !Ref 'Environment', 'dev' ]

Resources:
  TargetGroup:
    Type: AWS::ElasticLoadBalancingV2::TargetGroup
    Properties:
      Name: !Sub "${Environment}-${AppName}"
      Port: 443
      Protocol: TCP
      TargetType: ip
      HealthCheckProtocol: HTTPS
      HealthCheckPath: "/offer-service/health"
      Matcher:
        HttpCode: 200
      TargetGroupAttributes:
        - Key: deregistration_delay.timeout_seconds
          Value: '20'
      VpcId: 
        Fn::ImportValue:
          !Sub ${NetworkStackName}-VpcId
      Tags:
        - Key: Name
          Value: !Sub 'ECS Target Group - ${AWS::StackName}'
  Listener:
    Type: AWS::ElasticLoadBalancingV2::Listener
    Properties:
      Port: !Ref ListenerPort
      Protocol: TCP
      LoadBalancerArn: !ImportValue
        Fn::Sub: ${ClusterStackName}-NetworkLoadBalancerARN
      DefaultActions:
        - TargetGroupArn: !Ref TargetGroup
          Type: forward
  EcsTaskRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Sid: ''
            Effect: Allow
            Principal:
              Service: ecs-tasks.amazonaws.com
            Action: sts:AssumeRole
      Path: /
      Policies:
        - PolicyName: CloudWatch-Metrics-Access
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - cloudwatch:PutMetricData
                Resource:
                  - !Sub 'arn:aws:cloudwatch:${AWS::Region}:${AWS::AccountId}:service/${Environment}-${AppName}*'
        - PolicyName: !Sub '${AppName}-dynamodb-policy'
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - dynamodb:*
                Resource:
                  - !Sub 'arn:aws:dynamodb:${AWS::Region}:${AWS::AccountId}:table/${AppName}*'
        - PolicyName: Lambda-Resolver-Access
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - lambda:InvokeFunction
                Resource:
                  - !Sub 'arn:aws:lambda:${ResolverFunctionRegion}:${AmrpwlAccountId}:function:${Environment}-${ResolverFunctionName}'
        - PolicyName: ECS-Task-Access
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - ecs:DescribeTaskDefinition
                Resource:
                  - !Sub 'arn:aws:ecs:${AWS::Region}:${AWS::AccountId}:task-set/${ClusterStackName}/${Environment}-${AppName}*'
  EcsExecutionTaskRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Sid: ''
            Effect: Allow
            Principal:
              Service: ecs-tasks.amazonaws.com
            Action: sts:AssumeRole
      Path: /
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/AmazonEC2ContainerRegistryReadOnly
        - arn:aws:iam::aws:policy/CloudWatchLogsFullAccess
  TaskDefinition:
    Type: AWS::ECS::TaskDefinition
    Properties:
      ContainerDefinitions:
        - Name: !Ref 'AppName'
          Image: !Sub '************.dkr.ecr.${AWS::Region}.amazonaws.com/${AppName}:${AppVersion}'
          Cpu: !Ref 'AppCpu'
          PortMappings:
            - ContainerPort: !Ref 'AppContainerPort'
          MemoryReservation: !Ref 'AppMemory'
          Essential: 'true'
          LogConfiguration:
            LogDriver: awslogs
            Options:
              awslogs-group: !Ref 'LogGroup'
              awslogs-region: !Ref 'AWS::Region'
              awslogs-stream-prefix: ecs
              awslogs-datetime-format: '%Y-%m-%d %H:%M:%S'
        - Name: secure-proxy
          Image: !Sub '************.dkr.ecr.${AWS::Region}.amazonaws.com/secure-proxy:${Environment}'
          Cpu: 256
          MemoryReservation: 512
          VersionConsistency: disabled
          PortMappings:
            - ContainerPort: 443
          Environment:
            - Name: BACKEND_TARGET
              Value: !Sub 'http://127.0.0.1:${AppContainerPort}'
          Ulimits:
            - Name: nofile
              SoftLimit: 32768
              HardLimit: 32768
          Essential: 'true'
          LogConfiguration:
            LogDriver: awslogs
            Options:
              awslogs-group: !Ref 'LogGroup'
              awslogs-region: !Ref 'AWS::Region'
              awslogs-stream-prefix: ecs
      Family: !Ref 'AWS::StackName'
      TaskRoleArn: !Ref 'EcsTaskRole'
      NetworkMode: awsvpc
      RequiresCompatibilities:
        - FARGATE
      ExecutionRoleArn: !Ref 'EcsExecutionTaskRole'
      Cpu: !Ref 'ContainerTotalCpu'
      Memory: !Ref 'ContainerTotalMemory'
  Service:
    Type: AWS::ECS::Service
    DependsOn:
      - Listener
      - LogGroup
    Properties:
      ServiceName: !Ref 'AWS::StackName'
      LaunchType: FARGATE
      TaskDefinition: !Ref 'TaskDefinition'
      DesiredCount: !Ref 'AppDesiredCount'
      LoadBalancers:
        - TargetGroupArn: !Ref 'TargetGroup'
          ContainerPort: 443
          ContainerName: secure-proxy
      Cluster: !Ref ClusterStackName
      DeploymentConfiguration:
        MinimumHealthyPercent: 100
      NetworkConfiguration:
        AwsvpcConfiguration:
          AssignPublicIp: DISABLED
          Subnets:
            - Fn::ImportValue:
                !Sub ${NetworkStackName}-Private-A-SubID
            - Fn::ImportValue:
                !Sub ${NetworkStackName}-Private-B-SubID
            - Fn::ImportValue:
                !Sub ${NetworkStackName}-Private-C-SubID
          SecurityGroups:
            - !Ref 'AppSecurityGroup'
  AppSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupName: !Sub '${Environment}-${AppName}-sg'
      GroupDescription: ECS Allowed Ports
      VpcId: 
        Fn::ImportValue:
          !Sub ${NetworkStackName}-VpcId
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          CidrIp: 
            Fn::ImportValue:
              !Sub ${NetworkStackName}-Public-A-CIDR
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          CidrIp: 
            Fn::ImportValue:
              !Sub ${NetworkStackName}-Public-B-CIDR
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          CidrIp: 
            Fn::ImportValue:
              !Sub ${NetworkStackName}-Public-C-CIDR
  LogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      RetentionInDays: !Ref 'LogRetention'
      LogGroupName: !Ref 'AWS::StackName'
  SubscriptionFilter:
    Type: AWS::Logs::SubscriptionFilter
    DependsOn:
      - LogGroup
    Properties:
      RoleArn: !ImportValue
        Fn::Sub: ${KinesisStackName}-Role-Arn
      LogGroupName: !Ref 'LogGroup'
      FilterPattern: ''
      DestinationArn: !ImportValue
        Fn::Sub: ${KinesisStackName}-Stream-Arn
  EcsAutoScaleRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: application-autoscaling.amazonaws.com
            Action: sts:AssumeRole
      Path: /
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AmazonEC2ContainerServiceAutoscaleRole
  ScalableTarget:
    Type: AWS::ApplicationAutoScaling::ScalableTarget
    Properties:
      MaxCapacity: !Ref 'AppMaxCount'
      MinCapacity: !Ref 'AppMinCount'
      ResourceId: !Sub 'service/${ClusterStackName}/${Service.Name}'
      RoleARN: !GetAtt 'EcsAutoScaleRole.Arn'
      ScalableDimension: ecs:service:DesiredCount
      ServiceNamespace: ecs
  TargetTrackingScalingPolicy:
    Type: AWS::ApplicationAutoScaling::ScalingPolicy
    Properties:
      PolicyName: !Sub '${Service.Name}-TargetTrackingScaleUpPolicy'
      PolicyType: TargetTrackingScaling
      ScalingTargetId: !Ref 'ScalableTarget'
      TargetTrackingScalingPolicyConfiguration:
        TargetValue: 50
        ScaleInCooldown: 300
        ScaleOutCooldown: 60
        PredefinedMetricSpecification:
          PredefinedMetricType: ECSServiceAverageCPUUtilization

  PagerDutyTopic:
    Type: AWS::SNS::Topic
    Condition: CreatePagerDutyAlarms
    Properties:
      TopicName: !Sub "${AWS::StackName}-PagerDuty"
      Subscription:
      - Endpoint: !Sub "https://events.pagerduty.com/integration/${PagerDutyIntegrationKey}/enqueue"
        Protocol: https
  PagerDutyZeroHealthyInstances:
    Type: AWS::CloudWatch::Alarm
    Condition: CreatePagerDutyAlarms
    Properties:
      AlarmDescription: Triggers when there are 0 healthy ECS instances in service
      AlarmActions:
        - !Ref PagerDutyTopic
      OKActions:
        - !Ref PagerDutyTopic
      MetricName: HealthyHostCount
      Namespace: AWS/NetworkELB
      Statistic: Minimum
      Period: '60'
      EvaluationPeriods: '5'
      Threshold: !Ref 'HealthyHostThreshold'
      ActionsEnabled: !If [ IsDevEnvironment, false, true ]
      ComparisonOperator: LessThanThreshold
      TreatMissingData: breaching
      Dimensions:
        - Name: TargetGroup
          Value: !GetAtt 'TargetGroup.TargetGroupFullName'
        - Name: LoadBalancer
          Value: !Select
            - '1'
            - !Split
              - loadbalancer/
              - !ImportValue
                Fn::Sub: "${ClusterStackName}-NetworkLoadBalancerARN"
  PagerDutyHighCpu:
    Type: AWS::CloudWatch::Alarm
    Condition: CreatePagerDutyAlarms
    Properties:
      AlarmDescription: Triggers for extended periods of high cpu usage, that is not lowered by autoscaling
      AlarmActions:
        - !Ref PagerDutyTopic
      OKActions:
        - !Ref PagerDutyTopic
      MetricName: CPUUtilization
      Namespace: AWS/ECS
      Statistic: Average
      Period: '60'
      EvaluationPeriods: '30'
      Threshold: !Ref 'PagerDutyHighCpuThreshold'
      ActionsEnabled: !If [ IsDevEnvironment, false, true ]
      ComparisonOperator: GreaterThanThreshold
      TreatMissingData: ignore
      Dimensions:
        - Name: ClusterName
          Value: !Ref 'ClusterStackName'
        - Name: ServiceName
          Value: !GetAtt 'Service.Name'
  PagerDutyHighMemory:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmDescription: Triggers for extended periods of high memory usage, that is not lowered by autoscaling
      AlarmActions:
        - !Ref PagerDutyTopic
      OKActions:
        - !Ref PagerDutyTopic
      MetricName: MemoryUtilization
      Namespace: AWS/ECS
      Statistic: Average
      Period: '60'
      EvaluationPeriods: '30'
      Threshold: !Ref 'PagerDutyMemoryThreshold'
      ActionsEnabled: !If [ IsDevEnvironment, false, true ]
      ComparisonOperator: GreaterThanThreshold
      TreatMissingData: ignore
      Dimensions:
        - Name: ClusterName
          Value: !Ref 'ClusterStackName'
        - Name: ServiceName
          Value: !GetAtt 'Service.Name'

Outputs:
  Service:
    Description: The name of the ECS service created
    Value: !GetAtt 'Service.Name'
    Export:
      Name: !Sub '${AWS::StackName}-ServiceName'
  TaskFamily:
    Description: The family of the task created for the service
    Value: !Ref 'AWS::StackName'
  TaskArn:
    Description: The ARN of the task created for the service
    Value: !Ref 'TaskDefinition'
  LogGroup:
    Description: The name of the log group created for the app
    Value: !Ref 'LogGroup'
    Export:
      Name: !Sub '${AWS::StackName}-LogGroupName'
