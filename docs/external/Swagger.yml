openapi: 3.0.1
info:
  title: Offer API - External
  version: 1.16.14
servers:
  - url: https://localhost:8080
    description: Local server
  - url: https://dev.cdn.airmilesapis.ca
    description: Development server
  - url: https://int.cdn.airmilesapis.ca
    description: Integration server
  - url: https://uat.cdn.airmilesapis.ca
    description: UAT server
  - url: https://cdn.airmilesapis.ca/offers
    description: PROD server
paths:
  /offers:
    get:
      tags:
        - offers-unauthenticated-authenticated
      summary: Get all offers
      parameters:
        - $ref: "#/components/parameters/regionParam"
        - $ref: "#/components/parameters/partnerParam"
        - $ref: "#/components/parameters/categoryParam"
        - $ref: "#/components/parameters/subCategoryParam"
        - $ref: "#/components/parameters/promotionParam"
        - $ref: "#/components/parameters/offerTypeParam"
        - $ref: "#/components/parameters/programTypeParam"
        - $ref: "#/components/parameters/statesParam"
        - $ref: "#/components/parameters/availabilityParam"
        - $ref: "#/components/parameters/massOfferParam"
        - $ref: "#/components/parameters/extendedMetadataParam"
        - $ref: "#/components/parameters/experimentParam"
        - $ref: "#/components/parameters/excludePartnerParam"
        - $ref: "#/components/parameters/sortParam"
        - $ref: "#/components/parameters/offsetParam"
        - $ref: "#/components/parameters/limitParam"
        - $ref: "#/components/parameters/correlationHeader"
        - $ref: "#/components/parameters/clientHeader"
        - $ref: "#/components/parameters/authHeader"
        - $ref: "#/components/parameters/languageHeader"
        - $ref: "#/components/parameters/originHeader"
      responses:
        200:
          description: Success
          headers:
            Set-Cookie:
              schema:
                type: string
          $ref: "#/components/responses/offers"
        400:
          description: Bad Request
          headers:
            Set-Cookie:
              schema:
                type: string
          $ref: "#/components/responses/400"
        401:
          description: Unauthorized
          headers:
            Set-Cookie:
              schema:
                type: string
          $ref: "#/components/responses/401"
        403:
          description: Forbidden
          headers:
            Set-Cookie:
              schema:
                type: string
          $ref: "#/components/responses/403"
        404:
          description: Not Found
          headers:
            Set-Cookie:
              schema:
                type: string
          $ref: "#/components/responses/404"
        500:
          description: Internal Server Error
          headers:
            Set-Cookie:
              schema:
                type: string
          $ref: "#/components/responses/500"
      security:
        - jwt-authorizer: []
      x-amazon-apigateway-integration:
        type: "http_proxy"
        httpMethod: "GET"
        connectionType: "VPC_LINK"
        connectionId: "${stageVariables.vpcLinkId}"
        passthroughBehavior: "when_no_match"
        uri: "https://${stageVariables.offerApiEndpoint}:${stageVariables.offerApiEndpointPort}/offer-service/offers"
    options:
      summary: Support CORS request for getOffers, getOfferById and setOfferState. Auth/NoAuth
      tags:
        - CORS
      responses:
        200:
          description: Default response for CORS method
          headers:
            Access-Control-Allow-Origin:
              schema:
                type: "string"
            Access-Control-Allow-Methods:
              schema:
                type: "string"
            Access-Control-Allow-Headers:
              schema:
                type: "string"
          content: {}
        403:
          description: Unauthorized response for when calling authenticated api without being on the allowedOrigin list
          headers:
            Access-Control-Allow-Origin:
              schema:
                type: "string"
            Access-Control-Allow-Methods:
              schema:
                type: "string"
            Access-Control-Allow-Headers:
              schema:
                type: "string"
          content: {}

  /offers/{id}:
    get:
      tags:
        - offers-unauthenticated-authenticated
      summary: Get offer by ID
      parameters:
        - name: id
          in: path
          description: Offer Id
          required: true
          schema:
            type: string
            format: uuid
        - name: region
          in: query
          required: true
          schema:
            $ref: "#/components/parameters/regionParam"
          example: ON
          description: Not used for filtering, its used for extra context to return warning if an offer is unavalialable in your region
        - $ref: "#/components/parameters/correlationHeader"
        - $ref: "#/components/parameters/clientHeader"
        - $ref: "#/components/parameters/authHeader"
        - $ref: "#/components/parameters/languageHeader"
        - $ref: "#/components/parameters/originHeader"
      responses:
        200:
          description: Success
          headers:
            Set-Cookie:
              schema:
                type: string
          $ref: "#/components/responses/offer"
        400:
          description: Bad Request
          headers:
            Set-Cookie:
              schema:
                type: string
          $ref: "#/components/responses/400"
        401:
          description: Unauthorized
          headers:
            Set-Cookie:
              schema:
                type: string
          $ref: "#/components/responses/401"
        403:
          description: Forbidden
          headers:
            Set-Cookie:
              schema:
                type: string
          $ref: "#/components/responses/403"
        404:
          description: Not Found
          headers:
            Set-Cookie:
              schema:
                type: string
          $ref: "#/components/responses/404"
        500:
          description: Internal Server Error
          headers:
            Set-Cookie:
              schema:
                type: string
          $ref: "#/components/responses/500"
  /offers/{id}/states:
    put:
      tags:
        - offers-unauthenticated-authenticated
      summary: Change offer state for a specific collector
      parameters:
        - name: id
          in: path
          description: Offer Id
          required: true
          schema:
            type: string
            format: uuid
        - $ref: "#/components/parameters/correlationHeader"
        - $ref: "#/components/parameters/clientHeader"
        - $ref: "#/components/parameters/originHeader"
        - name: Authorization
          in: header
          schema:
            $ref: "#/components/parameters/authHeader"
          required: true
          description: As this is a collector flow (authenticated flow), auth is always required

      requestBody:
        $ref: '#/components/requestBodies/offerStates'
      responses:
        204:
          description: Success
        400:
          description: Bad Request
          headers:
            Set-Cookie:
              schema:
                type: string
          $ref: "#/components/responses/400"
        401:
          description: Unauthorized
          headers:
            Set-Cookie:
              schema:
                type: string
          $ref: "#/components/responses/401"
        403:
          description: Forbidden
          headers:
            Set-Cookie:
              schema:
                type: string
          $ref: "#/components/responses/403"
        404:
          description: Not Found
          headers:
            Set-Cookie:
              schema:
                type: string
          $ref: "#/components/responses/404"
        500:
          description: Internal Server Error
          headers:
            Set-Cookie:
              schema:
                type: string
          $ref: "#/components/responses/500"

components:
  schemas:
    Error:
      type: object
      properties:
        code:
          type: string
          example: ERROR_CODE
        message:
          type: string
          example: Something seems fishy
    Offer:
      type: object
      description: simplified offer object
      properties:
        id:
          type: string
          format: uuid
        partnerId:
          type: string
          format: uuid
        partnerLabel:
          type: string
        partnerLogo:
          type: object
          properties:
            url:
              type: string
        partnerProfileURL:
          type: string
        categoryId:
          type: string
          format: uuid
        categoryLabel:
          type: string
        subCategoryId:
          type: string
          format: uuid
        subCategoryLabel:
          type: string
        promotionId:
          type: string
          format: uuid
        promotionLabel:
          type: string
        awardShort:
          type: string
        qualifierShort:
          type: string
          description: short description
        image:
          type: object
          properties:
            url:
              type: string
        startDate:
          type: string
          format: date-time
        displayDate:
          type: string
          format: date-time
        endDate:
          type: string
          format: date-time
        description:
          type: string
        programType:
          type: string
        massOffer:
          type: boolean
        displayPriority:
          type: number
        cardType:
          type: array
          items:
            type: string
        ctaLabel:
          type: object
          additionalProperties:
            type: string
        ctaUrl:
          type: object
          additionalProperties:
            type: string
        eventBasedOffer:
          type: boolean
        firstQualificationDate:
          type: string
          format: date-time
        lastQualificationDate:
          type: string
          format: date-time
        eligibilityDuration:
          type: number
        eligibilityDurationUnit:
          type: string
          items:
            enum: [ "days","hours","minutes" ]
        tiers:
          type: array
          items:
            $ref: '#/components/schemas/tier'
        mechanisms:
          type: array
          items:
            $ref: '#/components/schemas/mechanism'
        states:
          type: array
          items:
            anyOf:
              - $ref: '#/components/schemas/optInState'
              - $ref: '#/components/schemas/saveState'
        legalText:
          type: string
    simpleMetadata:
      type: object
      description: Simple metadata object
      properties:
        total:
          type: number
    fullMetadata:
      type: object
      description: Return partners and categories only when extended_metadata flag is true.
      properties:
        total:
          type: number
        partners:
          type: array
          items:
            $ref: '#/components/schemas/linkObject'
        categories:
          type: array
          items:
            $ref: '#/components/schemas/categoryObject'
    warning:
      type: object
      description: Warning object returned when getting specific offer resource.
      properties:
        errorCode:
          type: string
          example: err-region
        message:
          type: string
          example: Offer Id does not belong to user's region
    tier:
      type: object
      properties:
        qualifierLong:
          type: string
        qualifierValue:
          type: number
          format: float
        awardLong:
          type: string
        awardValue:
          type: number
          format: float
    mechanism:
      type: object
      properties:
        mechanismType:
          type: string
        mechanismValue:
          type: string
        mechanismLabel:
          type: string
    optInState:
      type: object
      properties:
        name:
          type: string
          example: OPT_IN
        value:
          type: string
          example: OPTED_IN
        updatedAt:
          type: string
          format: date-time
    saveState:
      type: object
      properties:
        name:
          type: string
          example: SAVE
        value:
          type: string
          example: SAVED
        updatedAt:
          type: string
          format: date-time
    linkObject:
      type: object
      properties:
        id:
          type: string
          format: uuid
        count:
          type: number
        label:
          type: string
          description: Will be english or french depending on the language header
    categoryObject:
        allOf:
        - $ref: "#/components/schemas/linkObject"
        - type: object
          properties:
            subCategories:
              type: array
              items:
                $ref: "#/components/schemas/linkObject"

  responses:
    offers:
      description: simplified offers data returned
      content:
        application/json:
          schema:
            type: object
            properties:
              offers:
                type: array
                items:
                  $ref: "#/components/schemas/Offer"
              metadata:
                $ref: "#/components/schemas/simpleMetadata"
    offer:
      description: single offer data returned
      content:
        application/json:
          schema:
            type: object
            properties:
              offer:
                $ref: "#/components/schemas/Offer"
              warning:
                $ref: "#/components/schemas/warning"
    400:
      description: Bad request
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/Error"
    401:
      description: Unauthorized
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/Error"
    403:
      description: Forbidden
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/Error"
    404:
      description: Not Found
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/Error"
    500:
      description: Internal Server Error
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/Error"

  requestBodies:
    offerStates:
      description: request body for updating offer state, can update multiple states at a time for a single offer.
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              states:
                type: array
                items:
                  type: object
                  properties:
                    name:
                      type: string
                    value:
                      type: string
                example:
                  - name: SAVE
                    value: SAVED
                  - name: OPT_IN
                    value: OPTED_IN

  parameters:
    offerIdParam:
      name: id
      in: query
      schema:
        type: array
        items:
          type: string
          format: uuid
      required: false
      description: filter - Offer id (single/multiple)
      style: form
      explode: false
    detailParam:
      in: query
      name: with_metadata
      schema:
        type: boolean
        example: true
        default: true
      required: false
      description: Response Body change based on the value sent
    regionParam:
      in: query
      name: region
      schema:
        type: string
        example: "ON"
      required: true
      description: filter - Region, list of available values in documentation
    partnerParam:
      in: query
      name: partner_id
      schema:
        type: array
        items:
          type: string
          format: uuid
      required: false
      description: filter - List of partner id (UUID)
      style: form
      explode: false
    promotionParam:
      in: query
      name: promotion_id
      schema:
        type: string
        format: uuid
      required: false
      description: filter - promotion id
    categoryParam:
      in: query
      name: category_id
      schema:
        type: array
        items:
          type: string
          format: uuid
      required: false
      description: filter - List of category id (UUID)
      style: form
      explode: false
    subCategoryParam:
      in: query
      name: subcategory_id
      schema:
        type: array
        items:
          type: string
          format: uuid
      required: false
      description: filter - List of subcategory id (UUID)
      style: form
      explode: false
    offerTypeParam:
      in: query
      name: type
      schema:
        type: array
        items:
          enum: ["buy","spend","custom"]
      required: false
      description: filter - offer type
      style: form
      explode: false
    massOfferParam:
      in: query
      name: mass_offer
      schema:
        type: boolean
      required: false
      description: filter - mass offer
    availabilityParam:
      in: query
      name: availability
      schema:
        type: array
        items:
          enum: ["instore","online"]
      required: false
      description: filter - offer availability
      style: form
      explode: false
    programTypeParam:
      in: query
      name: program_type
      schema:
        type: array
        items:
          enum:
            - traditionalcore
            - cardlinked
            - airmilesshops
            - bmopreapp
      required: false
      description: filter - program tag
      style: form
      explode: false
    statesParam:
      in: query
      name: states
      schema:
        type: string
        enum:
          - SAVED
          - OPTED_IN
      required: false
      description: |
          filter - get offers that include the states selected. 
          
          **NOTE**: Authorization header is required.
          
          Only following other query parameters are supported in combination with the states query paramter, [region, partner_id, offset, limit]. Other query parameters are not supported when states query parameter has a value.
      style: form
      explode: false
    extendedMetadataParam:
      in: query
      name: extended_metadata
      schema:
        type: boolean
        default: false
      required: false
      description: filter - Returns the extra metadata information, ie partner counts (Legacy support)
    experimentParam:
      in: query
      name: experiment
      schema:
        type: string
        format: uuid
      required: false
      description: filter - Used for enhancing the order of returned offers
    sortParam:
      in: query
      name: sort
      schema:
        type: array
        items:
          enum:
          - "partnerId"
          - "promotionId"
          - "massOffer"
          - "-massOffer"
          - "displayPriority"
          - "-displayPriority"
          - "endDate"
          - "-endDate"
          - "collectorrelevance"
          - "regionrelevance"
      required: false
      description: |
        pagination - page sorted by given values

        **NOTE** - Not all permutations/combination of the sort array are allowed
      style: form
      explode: false
    offsetParam:
      in: query
      name: offset
      schema:
        type: number
      required: false
      description: pagination - page number
    limitParam:
      in: query
      name: limit
      schema:
        type: number
        default: 20
        maximum: 96
        minimum: 1
      required: false
      description: pagination - offers per page
    excludePartnerParam:
      in: query
      name: exclude_partner_id
      schema:
        type: array
        items:
          type: string
          format: uuid
      required: false
      description: filter - List of partner id (UUID) to exclude
      style: form
      explode: false
    correlationHeader:
      in: header
      name: X-Correlation-Id
      description: Auto generated by Offer-Service if not provided.
      schema:
        type: string
        format: uuid
      required: false
    clientHeader:
      in: header
      name: X-Origin-Client
      description: This field is used to better track the clients sending requests to Offer Service.
      schema:
        type: string
      required: true
      example: "external:test:swagger"
    languageHeader:
      in: header
      name: Accept-Language
      schema:
        type: string
        enum:
        - en-US
        - fr-CA
        default: en-US
      required: false
      description: Return the offer in the given locale
    authHeader:
      in: header
      name: Authorization
      description: Should be passed when accessing member/collector offer (authenticated flow)
      required: false
      schema:
        type: string
    originHeader:
      in: header
      name: Origin
      description: This header is sent by the browser and used by our service to determine CORS headers
      required: false
      schema:
        type: string

  securitySchemes:
    jwt-authorizer:
      type: "apiKey"
      name: "Authorization"
      in: "header"
      x-amazon-apigateway-authtype: "custom"
      x-amazon-apigateway-authorizer:
        authorizerUri:
          Fn::Sub: arn:aws:apigateway:ca-central-1:lambda:path/2015-03-31/functions/arn:aws:lambda:ca-central-1:${AWS::AccountId}:function:${environment}-jwt-authorizer-lambda/invocations
        authorizerResultTtlInSeconds:
          Ref: authPolicyCachingTTL
        type: "token"

x-amazon-apigateway-request-validators:
  all:
    validateRequestBody: false
    validateRequestParameters: false
  params-only:
    validateRequestBody: false
    validateRequestParameters: false
  Validate body:
    validateRequestParameters: false
    validateRequestBody: false

