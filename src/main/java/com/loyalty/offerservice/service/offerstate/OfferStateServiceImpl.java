package com.loyalty.offerservice.service.offerstate;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.loyalty.offerservice.model.dto.request.*;
import com.loyalty.offerservice.model.dto.response.offerstate.CountDTO;
import com.loyalty.offerservice.model.dto.response.offerstate.FindCollectorOffersDTO;
import com.loyalty.offerservice.model.dto.response.offerstate.GetCollectorOffersDTO;
import com.loyalty.offerservice.util.States;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ResourceLoader;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;

import static net.logstash.logback.marker.Markers.appendEntries;

@Service
@Slf4j
public class OfferStateServiceImpl implements OfferStateService {

    @Autowired
    ObjectMapper objectMapper;

    @Autowired
    ResourceLoader resourceLoader;

    @Autowired
    OfferStateClient offerStateClient;


    @Override
    public GetCollectorOffersDTO getOfferState(OfferRequest request, Long collectorId, List<UUID> offerId) throws IOException {
        GetCollectorOffersDTO offerResponse = null;
        try {
            offerResponse = invokeClient(request, collectorId, offerId);
        } catch (Exception e) {
            offerResponse = getOfferStateFallback(request, collectorId, offerId);
        }
        return offerResponse;
    }

    @Override
    public void updateOfferState(OfferStates request, OfferStateUpdateRequest offerStateUpdateRequest) {

        OfferStateUpdateRequest.OfferStateBody requestBody = new OfferStateUpdateRequest.OfferStateBody();

        // Check if request is valid and matches our supported values
        for (OfferState requestState : request.getStates()) {
            try {
                if (!States.stateValidator(requestState.getName(), requestState.getValue())) {
                    throw new IllegalArgumentException(String.format("States of %s cannot have the value of %s", requestState.getName(), requestState.getValue()));
                }
            } catch (NullPointerException e) {
                throw new IllegalArgumentException(String.format("States update with name %s is not supported", requestState.getName()));
            }
        }
        requestBody.setStateChanges(List.of(new OfferStatesServiceRequest(request, offerStateUpdateRequest.getOfferId())));
        offerStateUpdateRequest.setBody(requestBody);

        String collectorId = String.valueOf(offerStateUpdateRequest.getCollectorId());
        offerStateClient.updateOffer(generateHeaders(String.valueOf(offerStateUpdateRequest.getLocalDate()), String.valueOf(offerStateUpdateRequest.getCorrelationId()),
                offerStateUpdateRequest.getOriginClient()), offerStateUpdateRequest.getBody(), collectorId);

        Map<String, Object> logMap = new HashMap<>();
        logMap.put("offerId", offerStateUpdateRequest.getOfferId());
        logMap.put("collectorId", offerStateUpdateRequest.getCollectorId());
        log.info(appendEntries(logMap), "States updated successfully");
    }

    @Override
    public FindCollectorOffersDTO findOfferState(OfferRequest request, Long collectorId) throws IOException {
        FindCollectorOffersDTO offerResponse = null;
        try {
            offerResponse = invokeStateClient(request);
        } catch (Exception e) {
            offerResponse = getOfferStateFallbackForSavedOffer(request, collectorId);
        }
        return offerResponse;
    }

    @Override
    public CountDTO findOfferStateCount(OfferRequest request, Long collectorId) throws IOException {
        CountDTO countDTO = null;
        try {
            countDTO = invokeStateCountClient(request);
        } catch (Exception e) {
            countDTO = getOfferStateCountFallbackForSavedOffer(request, collectorId);
        }
        return countDTO;
    }

    private CountDTO invokeStateCountClient(OfferRequest request) {
        List<UUID> partnerIds = request.getFilters().getPartnerId();
        String collectorId = String.valueOf(request.getCollectorNumber());

        String stateCriteria = request.getFilters().getStates().getName() + ":" + request.getFilters().getStates().getValue();

        CountDTO count = offerStateClient.findOfferStateCount(generateHeaders(String.valueOf(request.getLocalTime()), String.valueOf(request.getCorrelationId()), request.getOriginClient()),
                request.getFilters().getRegion(),
                partnerIds != null && partnerIds.size() > 0 ? partnerIds.get(0) : null,
                stateCriteria, collectorId);
        return count;
    }

    private HttpHeaders generateHeaders(String localTime, String correlationId, String channel) {
        HttpHeaders headers = new HttpHeaders();
        headers.set("X-Local-Time", localTime);
        headers.set("X-Correlation-Id", correlationId);
        headers.set("X-Origin-Client", channel);
        return headers;

    }

    private GetCollectorOffersDTO invokeClient(OfferRequest request, Long collectorId, List<UUID> offerId) {
        String requestCollectorId = String.valueOf(collectorId);
        HttpHeaders requestHeaders = generateHeaders(String.valueOf(request.getLocalTime()), String.valueOf(request.getCorrelationId()), request.getOriginClient());
        return offerStateClient.getOffersState(requestHeaders, offerId, requestCollectorId);
    }

    @SneakyThrows
    private FindCollectorOffersDTO invokeStateClient(OfferRequest request) {
        List<UUID> partnerIds = request.getFilters().getPartnerId();
        String collectorId = String.valueOf(request.getCollectorNumber());
        Integer offset = Math.round(request.getPagination().getOffset() / request.getPagination().getLimit()) + 1;

        String stateCriteria = request.getFilters().getStates().getName() + ":" + request.getFilters().getStates().getValue();

        FindCollectorOffersDTO findCollectorOffersDTO = offerStateClient.findOfferState(generateHeaders(String.valueOf(request.getLocalTime()), String.valueOf(request.getCorrelationId()), request.getOriginClient()),
                request.getFilters().getRegion(),
                partnerIds != null && partnerIds.size() > 0 ? partnerIds.get(0) : null,
                offset,
                request.getPagination().getLimit(),
                stateCriteria, collectorId);

        return findCollectorOffersDTO;
    }

    private GetCollectorOffersDTO getOfferStateFallback(OfferRequest request, Long collectorId, List<UUID> offerIds) {
        var logMap = Map.of("offerIds", offerIds, "collectorId", collectorId, "request", request);
        log.error(appendEntries(logMap), "Unable to get offer state for given request. Returning empty state list",
                request, collectorId, offerIds);
        GetCollectorOffersDTO collectorOffersDTO = new GetCollectorOffersDTO();
        collectorOffersDTO.setOffer(Collections.emptyList());
        return collectorOffersDTO;
    }

    private FindCollectorOffersDTO getOfferStateFallbackForSavedOffer(OfferRequest request, Long collectorId) {
        var logMap = Map.of("request", request, "collectorId", collectorId);
        log.error(appendEntries(logMap), "Unable to get offer state for given request. Returning empty state list",
                request, collectorId);
        FindCollectorOffersDTO collectorOffersDTO = new FindCollectorOffersDTO();
        collectorOffersDTO.setOffers(Collections.emptyList());
        return collectorOffersDTO;
    }

    private CountDTO getOfferStateCountFallbackForSavedOffer(OfferRequest request, Long collectorId) {
        var logMap = Map.of("request", request, "collectorId", collectorId);
        log.error(appendEntries(logMap), "Unable to get offer state count for given request. Returning empty state list",
                request, collectorId);
        CountDTO countDTO = new CountDTO();
        countDTO.setCount(null);
        return countDTO;
    }

}
