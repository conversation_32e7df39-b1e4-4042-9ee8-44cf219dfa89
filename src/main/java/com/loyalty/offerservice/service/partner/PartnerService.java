package com.loyalty.offerservice.service.partner;

import com.loyalty.offerservice.model.dto.request.OfferRequest;
import com.loyalty.offerservice.model.dto.response.partner.PartnerResponse;

import java.io.IOException;
import java.util.List;
import java.util.UUID;

public interface PartnerService {

    PartnerResponse getPartner(List<UUID> partnerId, OfferRequest offerRequest) throws IOException;

    long loadPartners(OfferRequest offerRequest) throws IOException;
}
