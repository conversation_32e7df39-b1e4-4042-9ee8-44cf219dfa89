package com.loyalty.offerservice.service.offerresolver;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.loyalty.offerservice.cache.CacheService;
import com.loyalty.offerservice.exception.OffersNotFoundException;
import com.loyalty.offerservice.model.dto.request.FilterRequest;
import com.loyalty.offerservice.model.dto.request.OfferRequest;
import com.loyalty.offerservice.enums.RegionEnum;
import com.loyalty.offerservice.model.dto.response.offerresolver.OfferResolverResponseDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.cloud.client.circuitbreaker.CircuitBreaker;
import org.springframework.cloud.client.circuitbreaker.CircuitBreakerFactory;
import org.springframework.core.env.Environment;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;

import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class OfferResolverServiceImplTest {
    private static final String tokenValue = "***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";

    @Mock
    ResolverLambdaClient resolverClient;

    @Mock
    Environment environment;

    @Spy
    CaffeineCacheManager cacheManager;

    @Autowired
    ObjectMapper objectMapper1;

    @Autowired
    ResourceLoader resourceLoader1;

    @Spy @InjectMocks
    CacheService cacheService;

    @InjectMocks
    private OfferResolverServiceImpl offerResolverServiceImpl;

    private OfferResolverResponseDTO offerResponse1;
    private OfferResolverResponseDTO offerResponse2;
    private List<UUID> offers;
    private OfferRequest request;

    @BeforeEach
    public void setup() throws IOException {
        MockitoAnnotations.openMocks(this);
        offers = new ArrayList<>(List.of(UUID.fromString("71d603c0-2b0e-4a54-b4c4-97df0436c167"), UUID.fromString("71d603c0-2b0e-4a54-b4c4-97df0436c165")));
        request = new OfferRequest();

        Resource resource1 = resourceLoader1.getResource("classpath:CacheOfferResponse1.json");
        offerResponse1 = objectMapper1.readValue(new InputStreamReader(resource1.getInputStream()),
                OfferResolverResponseDTO.class);

        Resource resource2 = resourceLoader1.getResource("classpath:CacheOfferResponse2.json");
        offerResponse2 = objectMapper1.readValue(new InputStreamReader(resource2.getInputStream()),
                OfferResolverResponseDTO.class);

        
        request.setCollectorNumber(12345678L);
        request.setCorrelationId(UUID.fromString("22a2cdfd-ff82-45f6-bc94-c14a3a533922"));
        request.setOriginClient("web");
        request.setFilters(new FilterRequest(RegionEnum.ON, null, null, null, null, null, null, null, null, null, null, null, null));

        when(environment.getActiveProfiles()).thenReturn(new String[]{"cacheTest"});
        when(resolverClient.getOffersByUUID(any(), any(), any())).thenReturn(offerResponse1, offerResponse2, offerResponse2);
        when(environment.getProperty(any())).thenReturn("3");
    }

    @Test
    void getOffer() throws IOException {
        when(environment.getProperty("cache-expire.offer-resolver.value", Long.class)).thenReturn(50000000L);
        when(offerResolverServiceImpl.invokeClient(offers, request)).thenReturn(offerResponse1, offerResponse2, offerResponse2);
        offerResolverServiceImpl.getOffer(offers, request);
        OfferResolverResponseDTO finalOfferResponse2 = offerResolverServiceImpl.getOffer(offers, request);;
        assertEquals(4, finalOfferResponse2.getOffers().size());
        assertEquals("cache2", finalOfferResponse2.getOffers().get(0).getOfferCode());

        cacheManager.getCache("offerResolver").invalidate();
        OfferResolverResponseDTO finalOfferResponse3 = offerResolverServiceImpl.getOffer(offers, request);
        assertEquals(4, finalOfferResponse3.getOffers().size());
        assertEquals("cache2", finalOfferResponse3.getOffers().get(0).getOfferCode());
    }

    @Test
    void getOfferThrowsException() {
//        when(offerResolverServiceImpl.invokeClient(offers, request)).thenThrow(new OffersNotFoundException("NO_OFFERS_FOUND", "No offers found, try again later"));
        when(resolverClient.getOffersByUUID(any(), any(),any())).thenThrow(new OffersNotFoundException("NO_OFFERS_FOUND", "No offers found, try again later"));

        OffersNotFoundException exception = assertThrows(OffersNotFoundException.class, () -> {
            offerResolverServiceImpl.getOffer(offers, request);
        });

        assertEquals("NO_OFFERS_FOUND", exception.getErrorCode());
        assertEquals("No offers found, try again later", exception.getMessage());
    }
}