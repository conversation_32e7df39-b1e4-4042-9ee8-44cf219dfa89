package com.loyalty.offerservice.model.dto.response.buildresponse;


import com.loyalty.offerservice.enums.ODSErrorCodes;
import com.loyalty.offerservice.enums.RegionEnum;
import com.loyalty.offerservice.exception.OffersNotFoundException;
import com.loyalty.offerservice.exception.ResourceNotFoundException;
import com.loyalty.offerservice.model.dto.request.FilterRequest;
import com.loyalty.offerservice.model.dto.request.OfferRequest;
import com.loyalty.offerservice.model.dto.request.PaginationRequest;
import com.loyalty.offerservice.model.dto.response.*;
import com.loyalty.offerservice.model.dto.response.category.CategoriesResponse;
import com.loyalty.offerservice.model.dto.response.offerdelivery.OfferDeliveryOfferObject;
import com.loyalty.offerservice.model.dto.response.offerdelivery.OfferDeliveryResponse;
import com.loyalty.offerservice.model.dto.response.offerresolver.OfferDTO;
import com.loyalty.offerservice.model.dto.response.offerresolver.OfferResolverResponseDTO;
import com.loyalty.offerservice.model.dto.response.offerstate.CountDTO;
import com.loyalty.offerservice.model.dto.response.partner.PartnerObject;
import com.loyalty.offerservice.service.GenericException;
import com.loyalty.offerservice.service.OfferService;
import com.loyalty.offerservice.service.OfferServiceImpl;
import com.loyalty.offerservice.service.async.OfferDelivery;
import com.loyalty.offerservice.service.async.OfferResolver;
import com.loyalty.offerservice.service.async.OfferState;
import com.loyalty.offerservice.service.category.CategoryService;
import com.loyalty.offerservice.service.offerdelivery.OfferDeliveryService;
import com.loyalty.offerservice.service.offerresolver.OfferResolverService;
import com.loyalty.offerservice.service.offerstate.OfferStateService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

import static net.logstash.logback.marker.Markers.append;
import static net.logstash.logback.marker.Markers.appendEntries;

@Component
@Slf4j
public class BuildOfferServiceResponse extends BuildOfferResponse {
    @Autowired
    OfferResolverService offerResolverService;

    @Autowired
    OfferDeliveryService offerDeliveryService;

    @Autowired
    OfferStateService offerStateService;

    @Autowired
    CategoryService categoryService;

    @Autowired
    OfferDelivery offerDeliveryProcess;

    @Autowired
    OfferResolver offerResolverProcess;

    @Autowired
    OfferState offerStateProcess;

    @Autowired
    @Lazy
    OfferService offerService;

    public final static List<ODSErrorCodes> ODSWarningErrorCodes = new ArrayList<>(List.of(ODSErrorCodes.REGION_NOT_MATCH, ODSErrorCodes.UNAUTHORIZED));

    /**
     * Build out Offer Response
     *
     * @param request OfferRequest
     * @return OfferServiceExtendedOfferResponse
     * @throws IOException, ExecutionException, InterruptedException
     */
    public OfferServiceExtendedOfferResponse buildResponse(OfferRequest request) throws IOException, ExecutionException, InterruptedException {

        OfferServiceExtendedOfferResponse offerServiceExtendedOfferResponse = new OfferServiceExtendedOfferResponse();
        Map<String, String> mainThreadContext = MDC.getCopyOfContextMap();

        OfferDeliveryResponse offerDeliveryResponse = offerDeliveryProcess.retrieveProcess(request);
        Map<UUID, OfferDeliveryOfferObject> offerDeliveryDataMap = offerDeliveryProcess.getOfferDeliveryDataMap(offerDeliveryResponse);

        List<UUID> offerIds = offerDeliveryDataMap.keySet().stream().toList();

        if (offerIds.isEmpty())
            throw new ResourceNotFoundException("OFFERS_NOT_FOUND", "Offers not found, try again later. Offer Delivery contained no Offer information.");

        log.debug(append("offerIds", offerIds), "Offer Delivery Service: List of offer id");

        CompletableFuture<Map<UUID, List<Map<String, Object>>>> offerStateResponse = offerStateProcess.retrieveGetProcessAsync(offerIds, request, mainThreadContext);
        CompletableFuture<OfferResolverResponseDTO> offerResolverResponse = offerResolverProcess.retrieveProcessAsync(offerIds, request, mainThreadContext);

        List<OfferServiceResponse> offerServiceResponseList = offerService.getOfferResponseAsync(offerResolverResponse, offerDeliveryResponse, request, offerStateResponse, offerIds);

        offerServiceExtendedOfferResponse.setOffers(offerServiceResponseList);

        return getExtendedOfferServiceResponse(request, offerDeliveryResponse, offerServiceExtendedOfferResponse);

    }

    /**
     * Build out Extended Offer Service Response
     *
     * @param request OfferRequest
     * @return OfferServiceExtendedOfferResponse
     * @throws IOException
     */
    private OfferServiceExtendedOfferResponse getExtendedOfferServiceResponse(OfferRequest request, OfferDeliveryResponse offerDeliveryResponse, OfferServiceExtendedOfferResponse offerServiceExtendedOfferResponse) throws IOException {

        // Add the metadata that we get from the ODS service to the Offer Service response
        // If extended metadata is specified then get all metadata otherwise just get total
        if (request.getExtendedMetadata()) {
            CategoriesResponse categoryMetadata = getCategoriesWithCount(request.getLanguage(),
                    offerDeliveryResponse.getMetadata());

            OfferServiceExtendedOfferResponse.OfferServiceMetadata metadata = OfferServiceExtendedOfferResponse.OfferServiceMetadata
                    .builder()
                    .total(offerDeliveryResponse.getMetadata().getTotal())
                    .partners(offerDeliveryResponse.getMetadata().getPartners())
                    .categories(categoryMetadata.getResults()).build();
            offerServiceExtendedOfferResponse.setMetadata(metadata);
        } else {

            OfferServiceExtendedOfferResponse.OfferServiceMetadata metadata = OfferServiceExtendedOfferResponse.OfferServiceMetadata
                    .builder()
                    .total(offerDeliveryResponse.getMetadata().getTotal()).build();
            offerServiceExtendedOfferResponse.setMetadata(metadata);
        }
        return offerServiceExtendedOfferResponse;
    }

    private CategoriesResponse getCategoriesWithCount(String locale, CommonMetadata offerDeliveryMetadata) throws IOException {

        CategoriesResponse categoriesResponse = categoryService.getCategories(locale);
        categoriesResponse.setCounts(offerDeliveryMetadata);
        return categoriesResponse;
    }

    /**
     * Build out Offer Response
     *
     * @param request OfferRequest
     * @return OfferServiceExtendedOfferResponseForOfferId
     * @throws GenericException, IOException
     */
    public OfferServiceExtendedOfferResponseForOfferId buildResponseForOffersById(OfferRequest request) throws GenericException, IOException {

        OfferServiceExtendedOfferResponseForOfferId offerServiceExtendedOfferResponseForOfferId = new OfferServiceExtendedOfferResponseForOfferId();
        Map<String, String> mainThreadContext = MDC.getCopyOfContextMap();

        CompletableFuture<OfferDeliveryOfferObject> offerDeliveryObjectResponse = offerDeliveryProcess.retrieveProcessByIdAsync(request, mainThreadContext);
        CompletableFuture<Map<UUID, List<Map<String, Object>>>> offerStateAsyncResponse = offerStateProcess.retrieveGetProcessAsync(Arrays.asList(request.getOfferId()), request, mainThreadContext);
        CompletableFuture<OfferResolverResponseDTO> offerResolverAsyncResponse = offerResolverProcess.retrieveProcessAsync(Arrays.asList(request.getOfferId()), request, mainThreadContext);

        try {
            CompletableFuture.allOf(offerDeliveryObjectResponse, offerStateAsyncResponse, offerResolverAsyncResponse).join();
        } catch (CompletionException ex) {
            if (ex.getCause() instanceof ResourceNotFoundException) {
                String code = ((ResourceNotFoundException) ex.getCause()).getErrorCode();
                // Check to see if we have a specific error from ODS
                // If that error is encountered treat is as non blocking and set the warning object
                ODSErrorCodes errorCode = ODSErrorCodes.of(code);
                if (ODSWarningErrorCodes.contains(errorCode)) {
                    Warning warning = new Warning();
                    warning.setErrorCode(errorCode.getCode());
                    String message = ex.getMessage().contains(":") ? ex.getMessage().split(":")[1].trim() : ex.getMessage();
                    warning.setMessage(message);
                    offerServiceExtendedOfferResponseForOfferId.setWarning(warning);
                    var logMap = Map.of("offerId", request.getOfferId(), "errorCode", errorCode.getCode(), "message", ex.getMessage());
                    log.warn(appendEntries(logMap), "Offer not found in ODS with Errors Codes");
                } else {
                    throw ((ResourceNotFoundException) ex.getCause());
                }
            } else {
                Map<String, Object> data = new HashMap<>();
                data.put("offerResolverException", offerResolverAsyncResponse.isCompletedExceptionally());
                data.put("offerStateException", offerStateAsyncResponse.isCompletedExceptionally());
                data.put("offerDeliveryObjectException", offerDeliveryObjectResponse.isCompletedExceptionally());
                log.error(appendEntries(data)
                        , "Exception occurred when waiting for offerResolverResponse, offerStateResponse, offerDeliveryObjectResponse to complete", ex);
                throw ex;

            }

        }
        OfferResolverResponseDTO offerResolverResponse = offerResolverAsyncResponse.join();
        Map<UUID, List<Map<String, Object>>> offerStateResponse = offerStateAsyncResponse.join();

        OfferDeliveryOfferObject offerDeliveryOfferObject = null;
        if (offerDeliveryObjectResponse.isCompletedExceptionally()) {
            log.warn(append("offerId", request.getOfferId()), "Exception occurred when waiting for offerDeliveryObjectResponse to complete");
        } else {
            offerDeliveryOfferObject = offerDeliveryObjectResponse.join();
        }

        OfferServiceResponse offerServiceResponse = offerService.getOfferResponseForOfferId(offerResolverResponse, offerDeliveryOfferObject, request, offerStateResponse);

        offerServiceExtendedOfferResponseForOfferId.setOffer(offerServiceResponse);
        return offerServiceExtendedOfferResponseForOfferId;

    }

    /**
     * Build out Offer Response for Offer State
     *
     * @param request OfferRequest
     * @return OfferServiceExtendedOfferResponse
     * @throws GenericException, IOException
     */
    public OfferServiceExtendedOfferResponse buildResponseByStates(OfferRequest request) throws GenericException, IOException, ExecutionException, InterruptedException {
        OfferServiceExtendedOfferResponse offerServiceExtendedOfferResponse = new OfferServiceExtendedOfferResponse();
        Map<String, String> mainThreadContext = MDC.getCopyOfContextMap();
        OfferDeliveryResponse offerDeliveryResponseObject;

        CompletableFuture<CountDTO> offerStateCountResponse = offerStateProcess.retrieveCountProcessAsync(request, mainThreadContext);
        CompletableFuture<Map<UUID, List<Map<String, Object>>>> offerStateFindResponse = offerStateProcess.retrieveFindProcessAsync(request, mainThreadContext);

        // Create custom pagination request for ODS for getting EBO offers
        OfferRequest ODSRequest = new OfferRequest(request);
        ODSRequest.setPagination(PaginationRequest.builder().limit(96).offset(0).build());
        CompletableFuture<OfferDeliveryResponse> offerDeliveryResponse = offerDeliveryProcess.retrieveProcessAsync(ODSRequest, mainThreadContext);

        try {
            CompletableFuture.allOf(offerStateCountResponse, offerStateFindResponse, offerDeliveryResponse).join();
            offerDeliveryResponseObject = offerDeliveryResponse.join();
        } catch (CompletionException ex) {
            Map<String, Object> data = new HashMap<>();
            if (offerDeliveryResponse.isCompletedExceptionally() && ex.getCause() instanceof ResourceNotFoundException) {
                offerDeliveryResponseObject = null;
                data.put("offerDeliveryException", offerDeliveryResponse.isCompletedExceptionally());
                log.error(appendEntries(data), "Exception occurred when waiting for offerDeliveryException to complete", ex);
            } else {
                data.put("offerStateCountException", offerStateCountResponse.isCompletedExceptionally());
                data.put("offerStateFindException", offerStateFindResponse.isCompletedExceptionally());
                data.put("offerDeliveryException", offerDeliveryResponse.isCompletedExceptionally());
                log.error(appendEntries(data), "Exception occurred when waiting for offerStateCountException, offerStateFindException, offerDeliveryException to complete", ex);
                throw ex;
            }
        }

        CountDTO count = offerStateCountResponse.join();
        Map<UUID, List<Map<String, Object>>> statesMap = offerStateFindResponse.join();
        Set<UUID> stateOfferIds = new HashSet<>(statesMap.keySet());

        Map<UUID, OfferDeliveryOfferObject> savedODSOffers = new HashMap<UUID, OfferDeliveryOfferObject>();
        if(offerDeliveryResponseObject != null) {
            savedODSOffers = offerDeliveryResponseObject
                    .getData()
                    .stream()
                    .filter(odsOffer -> stateOfferIds.contains(odsOffer.getId()))
                    .collect(Collectors.toMap(OfferDeliveryOfferObject::getId, offer -> offer));
        }

        if (!stateOfferIds.isEmpty()) {
            OfferResolverResponseDTO offerResolverResponse = offerResolverProcess.retrieveProcess(stateOfferIds.stream().toList(), request);

            for (OfferDTO offer : offerResolverResponse.getOffers()) {
                OfferDeliveryOfferObject savedODSObject = savedODSOffers.get(offer.getId());
                if (savedODSObject != null && !savedODSOffers.isEmpty()) {
                    OfferServiceImpl.replaceOfferDatesODS(savedODSObject, offer, request);
                }
            }

            List<OfferServiceResponse> offerResponses = offerService.getOfferResponse(offerResolverResponse, request, statesMap);

            offerServiceExtendedOfferResponse.setOffers(offerResponses);
        } else {
            offerServiceExtendedOfferResponse.setOffers(new ArrayList<>());
        }

        OfferServiceExtendedOfferResponse.OfferServiceMetadata.OfferServiceMetadataBuilder metadataBuilder
                = OfferServiceExtendedOfferResponse.OfferServiceMetadata.builder();

        if (count != null && count.getCount() != null && count.getCount().intValue() > 0) {
            metadataBuilder.total(count.getCount().intValue());
        } else {

            metadataBuilder.total(0);
        }

        offerServiceExtendedOfferResponse.setMetadata(metadataBuilder.build());
        return offerServiceExtendedOfferResponse;

    }

    public OfferServiceExtendedOfferResponseForOfferId buildResponseForOffersByIdAdmin(OfferRequest request)
            throws GenericException, IOException {

        OfferServiceExtendedOfferResponseForOfferId response = new OfferServiceExtendedOfferResponseForOfferId();
        Map<String, String> mainThreadContext = MDC.getCopyOfContextMap();

        CompletableFuture<OfferResolverResponseDTO> offerResolverAsyncResponse =
                offerResolverProcess.retrieveProcessAsync(Collections.singletonList(request.getOfferId()), request, mainThreadContext);

        OfferResolverResponseDTO offerResolverResponse = null;
        try {
            offerResolverResponse = offerResolverAsyncResponse.join();
        } catch (CompletionException ex) {
            handleAsyncException(ex, offerResolverAsyncResponse, null, "offerResolverAsyncResponse", request, response);
        }

        // Throw an error if there is nothing returned from the Offer resolver
        Optional.ofNullable(offerResolverResponse)
                .map(OfferResolverResponseDTO::getOffers)
                .filter(offers -> !offers.isEmpty())
                .orElseThrow(() -> new OffersNotFoundException("OFFER_NOT_FOUND", "Offer not found, try again later."));

        // Validate resolved offer data
        OfferDTO offerResolverObject = offerResolverResponse.getOffers().stream().findFirst().orElse(null);

        // Now, check if we actually need to use offerDeliveryProcess response
        OfferDeliveryOfferObject offerDeliveryOfferObject = null;
        boolean isTargetted = !offerResolverObject.getMassOffer();
        boolean isEBO = offerResolverObject.getEventBasedOffer();
        if (request.getCollectorNumber() != null && (isTargetted || isEBO)) {
            // Extract the region from Resovler and set it to the request for ODS
            RegionEnum region = offerResolverObject.getRegions().get(0);
            FilterRequest filterRequest = request.getFilters();
            filterRequest.setRegion(region);
            request.setFilters(filterRequest);

            CompletableFuture<OfferDeliveryOfferObject> offerDeliveryAsyncResponse =
                    offerDeliveryProcess.retrieveProcessByIdAsync(request, mainThreadContext);

            try {
                offerDeliveryOfferObject = offerDeliveryAsyncResponse.join();
            } catch (CompletionException ex) {
                handleAsyncException(ex, null, offerDeliveryAsyncResponse, "offerDeliveryObjectResponse", request, response);
            }
        }

        response.setOffer(offerService.getOfferResponseForOfferId(offerResolverResponse, offerDeliveryOfferObject, request, null));
        return response;
    }

    private void handleAsyncException(CompletionException ex, CompletableFuture<?> resolverFuture,
                                      CompletableFuture<?> deliveryFuture, String errorContext,
                                      OfferRequest request, OfferServiceExtendedOfferResponseForOfferId response) throws IOException {

        if (ex.getCause() instanceof ResourceNotFoundException) {
            String code = ((ResourceNotFoundException) ex.getCause()).getErrorCode();
            // Check to see if we have a specific error from ODS
            // If that error is encountered treat is as non blocking and set the warning object
            ODSErrorCodes errorCode = ODSErrorCodes.of(code);
            if (ODSWarningErrorCodes.contains(errorCode) ||
                    List.of(ODSErrorCodes.EVENT_BASED_OFFER_AUTHORIZATION_ERROR, ODSErrorCodes.EVENT_BASED_OFFER_EXCLUSIVE,
                            ODSErrorCodes.OFFER_EXCLUSIVE, ODSErrorCodes.OFFER_EXPIRED, ODSErrorCodes.OFFER_NOT_LIVE).contains(errorCode)) {
                Warning warning = new Warning();
                warning.setErrorCode(errorCode.getCode());
                String message = ex.getMessage().contains(":") ? ex.getMessage().split(":")[1].trim() : ex.getMessage();
                warning.setMessage(message);
                response.setWarning(warning);
                var logMap = Map.of("offerId", request.getOfferId(), "errorCode", errorCode.getCode(), "message", ex.getMessage());
                log.warn(appendEntries(logMap), "Offer not found in ODS with Errors Codes");
            } else {
                throw ((ResourceNotFoundException) ex.getCause());
            }
        } else {
            Map<String, Object> data = new HashMap<>();
            if (resolverFuture != null && resolverFuture.isCompletedExceptionally()) {
                data.put("offerResolverException", true);
            }
            if (deliveryFuture != null && deliveryFuture.isCompletedExceptionally()) {
                data.put("offerDeliveryObjectException", true);
            }

            if (!data.isEmpty()) {
                log.error(appendEntries(data), "Exception occurred when waiting for " + errorContext + " to complete", ex);
            } else {
                log.error("Unexpected exception in " + errorContext, ex);
            }
        }
    }
}