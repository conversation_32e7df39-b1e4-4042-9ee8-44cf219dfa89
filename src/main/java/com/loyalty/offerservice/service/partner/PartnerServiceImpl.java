package com.loyalty.offerservice.service.partner;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.loyalty.offerservice.cache.CacheService;
import com.loyalty.offerservice.model.dto.cache.OfferServiceCacheResponse;
import com.loyalty.offerservice.model.dto.request.OfferRequest;
import com.loyalty.offerservice.model.dto.response.partner.PartnerObject;
import com.loyalty.offerservice.model.dto.response.partner.PartnerResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.client.circuitbreaker.CircuitBreaker;
import org.springframework.cloud.client.circuitbreaker.CircuitBreakerFactory;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ResourceLoader;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import static net.logstash.logback.marker.Markers.append;
import static net.logstash.logback.marker.Markers.appendEntries;

import java.io.IOException;
import java.util.*;

@Service
@Slf4j
public class PartnerServiceImpl implements PartnerService {

    private static final String cacheName = "partners";
    private static final String cacheExpireValue = "cache-expire.partner.value";
    @Autowired
    ObjectMapper objectMapper;
    @Autowired
    PartnerClient partnerClient;
    @Autowired
    ResourceLoader resourceLoader;
    @Autowired
    private Environment environment;
    @Autowired
    private CacheService cacheService;

    @Override
    public PartnerResponse getPartner(List<UUID> partnerIds, OfferRequest offerRequest) throws IOException {
        PartnerResponse partnerResponse = new PartnerResponse(new ArrayList<>(), 100, 0, partnerIds.size());

        OfferServiceCacheResponse<PartnerObject, UUID> cacheResponse = cacheService.fetchFromCache(partnerIds, offerRequest.getLanguage(), cacheName, PartnerObject.class);
        if (null != cacheResponse.getCachedObjects()) {
            partnerResponse.getResults().addAll(cacheResponse.getCachedObjects());
        }

        if (null != cacheResponse.getMissingKeys() && !cacheResponse.getMissingKeys().isEmpty()) {
            log.info(append("missingKeys", cacheResponse.getMissingKeys()), "Partner Service Missing keys from cache");
            PartnerResponse partnersFromService = null;

            try {
                partnersFromService = invokeClient(cacheResponse.getMissingKeys(),offerRequest);
            } catch (Exception e){
                partnersFromService = partnerClientFallback(partnerIds, offerRequest, e);
            }

            partnerResponse.getResults().addAll(partnersFromService.getResults());
            partnerResponse.setTotal(partnerResponse.getResults().size());
            partnerResponse.setLimit(partnersFromService.getLimit());
            partnerResponse.setOffset(partnersFromService.getOffset());

            updateCache(partnersFromService.getResults(), offerRequest);
        }

        return partnerResponse;
    }

    @Override
    public long loadPartners(OfferRequest offerRequest) throws IOException {
        PartnerResponse partnerResponse = new PartnerResponse(new ArrayList<>(), 100, 0, 0);
        long totalPartners = 0L;
        int offSet = 0;
        int limit;
        int total;

        do {
            PartnerResponse partnersFromService = partnerClient.getPartners(generateHeaders(offerRequest), offSet, "in-store");
            partnerResponse.getResults().addAll(partnersFromService.getResults());
            partnerResponse.setTotal(partnersFromService.getTotal());
            partnerResponse.setLimit(partnersFromService.getLimit());
            partnerResponse.setOffset(partnersFromService.getOffset());
            limit = partnersFromService.getLimit();
            total = partnersFromService.getTotal();
            offSet = partnersFromService.getOffset() + (limit == 0 ? partnersFromService.getResults().size() : limit);
        }
        while (offSet < total);

        if (partnerResponse.getResults().size() > 0) {
            cacheService.removeCache(cacheName, offerRequest.getLanguage());
            updateCache(partnerResponse.getResults(), offerRequest);
        }
        totalPartners = partnerResponse.getResults().size();
        partnerResponse.getResults().clear();

        return totalPartners;
    }

    private HttpHeaders generateHeaders(OfferRequest request) {
        HttpHeaders headers = new HttpHeaders();
        headers.set("X-Correlation-Id", String.valueOf(request.getCorrelationId()));
        headers.set("X-Origin-Client", request.getOriginClient());
        headers.set("Accept-Language", request.getLanguage());
        Optional.ofNullable(request.getCollectorNumber()).ifPresent(
                v -> headers.set("Collector-Number", String.valueOf(v)));

        return headers;
    }

    private void updateCache(List<PartnerObject> partnersList, OfferRequest request) {
        long loadExpireValue = environment.getProperty(cacheExpireValue, Long.class);
        for (PartnerObject partner : partnersList) {
            cacheService.updateCache(loadExpireValue, partner.getId(), partner, cacheName, request.getLanguage());
        }
    }

    public PartnerResponse invokeClient(List<UUID> missingPartnerIds, OfferRequest offerRequest) {
        return partnerClient.getPartner(
                missingPartnerIds,
                generateHeaders(offerRequest));
    }

    private PartnerResponse partnerClientFallback(List<UUID> partnerIds, OfferRequest offerRequest, Throwable throwable) {
        var logData = Map.of("partnerIds", partnerIds, "offerRequest", offerRequest);
        log.error(appendEntries(logData), "Unable to get partner for given request. Returning empty partner list on partnerClientFallback", throwable);
        PartnerResponse partnerResponse = new PartnerResponse();
        partnerResponse.setResults(Collections.emptyList());

        return partnerResponse;
    }
}