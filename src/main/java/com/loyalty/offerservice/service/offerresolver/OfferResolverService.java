package com.loyalty.offerservice.service.offerresolver;

import com.loyalty.offerservice.model.dto.request.OfferRequest;
import com.loyalty.offerservice.model.dto.response.offerresolver.OfferResolverResponseDTO;

import java.io.IOException;
import java.util.List;
import java.util.UUID;

public interface OfferResolverService {

    OfferResolverResponseDTO getOffer(List<UUID> data, OfferRequest offerRequest) throws IOException;
}
