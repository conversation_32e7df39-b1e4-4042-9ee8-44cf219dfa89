package com.loyalty.offerservice.exception;

import static com.loyalty.offerservice.exception.utils.ExceptionConstantsUtils.INTERNAL_SERVER_ERROR;
import static com.loyalty.offerservice.exception.utils.ExceptionConstantsUtils.INTERNAL_SERVER_ERROR_MESSAGE;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
public class InternalServerException extends AppRuntimeException {
    public InternalServerException(Throwable cause) {
        this(INTERNAL_SERVER_ERROR_MESSAGE, cause);
    }

    public InternalServerException(String message, Throwable cause) {
        this(INTERNAL_SERVER_ERROR, message, cause);
    }

    public InternalServerException(String errorCode, String message, Throwable cause) {
        super(errorCode, message, cause);
    }

}
