package com.loyalty.offerservice.converter;

import com.loyalty.offerservice.enums.LanguageCode;
import org.springframework.core.convert.converter.Converter;

public class LanguageEnumConverter implements Converter<String, LanguageCode> {
    @Override
    public LanguageCode convert(String source) {
        try {
            return LanguageCode.fromValue(source);
        } catch (Exception e) {
            return LanguageCode.ENGLISH_US;
        }
    }
}
