<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <appender name="jsonEncoder" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <logLevel>
                    <fieldName>level</fieldName>
                </logLevel>
                <timestamp>
                    <fieldName>timestamp</fieldName>
                    <timeZone>UTC</timeZone>
                    <pattern>yyyy-MM-dd' 'HH:mm:ss.SSS</pattern>
                </timestamp>
                <loggerName>
                    <fieldName>logger</fieldName>
                </loggerName>
                <threadName>
                    <fieldName>thread</fieldName>
                </threadName>
                <message />
                <nestedField>
                    <fieldName>mdc</fieldName>
                    <providers>
                        <mdc />
                    </providers>
                </nestedField>
                <nestedField>
                    <fieldName>data</fieldName>
                    <providers>
                        <logstashMarkers />
                    </providers>
                </nestedField>
                <stackTrace>
                    <fieldName>stackTrace</fieldName>
                    <!-- maxLength - limit the length of the stack trace -->
                    <throwableConverter class="net.logstash.logback.stacktrace.ShortenedThrowableConverter">
                        <maxDepthPerThrowable>200</maxDepthPerThrowable>
                        <maxLength>14000</maxLength>
                        <rootCauseFirst>true</rootCauseFirst>
                    </throwableConverter>
                </stackTrace>
                <throwableClassName>
                    <fieldName>exceptionClass</fieldName>
                </throwableClassName>
            </providers>
        </encoder>
    </appender>

    <logger name="jsonEncoderLogger" level="DEBUG">
        <appender-ref ref="jsonEncoder"/>
    </logger>

    <root>
        <appender-ref ref="jsonEncoder"/>
    </root>
</configuration>