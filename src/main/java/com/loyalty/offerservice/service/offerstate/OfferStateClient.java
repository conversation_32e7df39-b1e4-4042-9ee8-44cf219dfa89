package com.loyalty.offerservice.service.offerstate;

import com.loyalty.offerservice.config.FeignLoggingConfiguration;
import com.loyalty.offerservice.model.dto.request.OfferState;
import com.loyalty.offerservice.model.dto.request.OfferStateUpdateRequest;
import com.loyalty.offerservice.model.dto.response.offerstate.CountDTO;
import com.loyalty.offerservice.enums.RegionEnum;
import com.loyalty.offerservice.model.dto.response.offerstate.FindCollectorOffersDTO;
import com.loyalty.offerservice.model.dto.response.offerstate.GetCollectorOffersDTO;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

@FeignClient(name = "OfferStateClient", url = "${offer-state.base}", configuration = FeignLoggingConfiguration.class)
public interface OfferStateClient {

	@GetMapping(value = "/state-reader/v1/{collectorId}/get", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	GetCollectorOffersDTO getOffersState(@RequestHeader HttpHeaders headers, @RequestParam List<UUID> offerId,
			@PathVariable String collectorId);

	@PutMapping(value = "/state-writer/v1/{collectorId}/put", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	void updateOffer(@RequestHeader HttpHeaders headers,
			@RequestBody OfferStateUpdateRequest.OfferStateBody requestBody,
			@PathVariable String collectorId);

	@GetMapping(value = "/state-reader/v1/{collectorId}/find", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	FindCollectorOffersDTO findOfferState(@RequestHeader HttpHeaders headers,
			@RequestParam RegionEnum region,
			@RequestParam UUID partnerId,
			@RequestParam(defaultValue = "0", required = false, name = "pageNumber") @Min(0) Integer offset,
			@RequestParam(defaultValue = "96", required = false, name = "pageSize") @Min(0) @Max(96) Integer limit,
			@RequestParam(required = false, name = "state") String stateCriteria,
			@PathVariable String collectorId);

	@GetMapping(value = "/state-reader/v1/{collectorId}/count", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	CountDTO findOfferStateCount(@RequestHeader HttpHeaders headers,
			@RequestParam RegionEnum region,
			@RequestParam(required = false) UUID partner_id,
			@RequestParam(required = false, name = "state") String stateCriteria,
			@PathVariable String collectorId);

}