package com.loyalty.offerservice.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonEnumDefaultValue;

import java.util.Arrays;

public enum LanguageCode {

    @JsonEnumDefaultValue
    ENGLISH_US("en-US"),
    FRENCH_CA("fr-CA");

    private final String code;
    LanguageCode(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    @JsonCreator
    public static LanguageCode fromValue(String code) {
        return Arrays.stream(LanguageCode.values())
                .filter(errorCode -> errorCode.getCode().equals(code))
                .findFirst()
                .orElse(ENGLISH_US);
    }

}
