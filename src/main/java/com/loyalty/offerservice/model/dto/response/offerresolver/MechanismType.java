package com.loyalty.offerservice.model.dto.response.offerresolver;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

@Getter
public enum MechanismType {

    @JsonProperty("noAction")
    NoAction("noAction"),

    @<PERSON>son<PERSON>roperty("barcodeUPC")
    BarcodeUPC("barcodeUPC"),

    @JsonProperty("barcodeEAN")
    BarcodeEAN("barcodeEAN"),

    @JsonProperty("barcodeCODE39")
    BarcodeCODE39("barcodeCODE39"),

    @JsonProperty("barcodeCODE128")
    BarcodeCODE128("barcodeCODE128"),

    @JsonProperty("plu")
    PLU("plu"),

    @JsonProperty("couponCode")
    CouponCode("couponCode"),

    @JsonProperty("button")
    Button("button"),

    @JsonProperty("load+go")
    LoadGo("load+go"),

    @JsonProperty("optIn")
    OptIn("optIn"),

    @JsonProperty("scanReceipt")
    ScanReceipt("scanReceipt");

    MechanismType(String noAction) {
    }
}
