package com.loyalty.offerservice.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.loyalty.offerservice.model.dto.request.OfferRequest;
import com.loyalty.offerservice.model.dto.response.OfferServiceExtendedOfferResponse;
import com.loyalty.offerservice.model.dto.response.offerdelivery.OfferDeliveryOfferObject;
import com.loyalty.offerservice.model.dto.response.offerdelivery.OfferDeliveryResponse;
import com.loyalty.offerservice.service.async.OfferDelivery;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.io.InputStreamReader;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * This test file is used to test out logic related to the incoming request
 * itself, such as verifying the query parameter, headers.
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
public class OfferServiceTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    OfferService offerService;

    @Captor
    ArgumentCaptor<OfferRequest> offerRequestArgumentCaptor;

    @Autowired
    ResourceLoader resourceLoader;

    @Autowired
    ObjectMapper objectMapper;

    @Autowired
    OfferDelivery offerDeliveryProcess;

    @ParameterizedTest
    @CsvSource({ "regionrelevance,200", "testing,400", "massOffer,200" })
    void testSortingQueryParam(String sort, int status) throws Exception {
        OfferServiceExtendedOfferResponse offerServiceExtendedOfferResponse = mock(
                OfferServiceExtendedOfferResponse.class);

        when(offerService.getOffers(offerRequestArgumentCaptor.capture()))
                .thenReturn(offerServiceExtendedOfferResponse);

        mockMvc.perform(get("/offers?region=ON")
                .queryParam("sort", sort)
                .header("X-Timestamp", "2023-10-04T01:30:00.000-05:00")
                .header("X-Origin-Client", "unit:test")
                .header("Authorization", "e58ed763-928c-4155-bee9-fdbaaadc15f3"))
                .andExpectAll(status().is(status),
                        content().contentType(MediaType.APPLICATION_JSON));

        if (status != 400) {
            assertEquals(offerRequestArgumentCaptor.getValue().getPagination().getSort(), List.of(sort));
        }

    }

    @Test
    void orderTesting() throws Exception{
        Resource resource = resourceLoader.getResource("classpath:offerDeliverySortTest.json");
        OfferDeliveryResponse offerDeliveryResponse = objectMapper.readValue(new InputStreamReader(resource.getInputStream()), OfferDeliveryResponse.class);

        Map<UUID, OfferDeliveryOfferObject> dataMap = offerDeliveryProcess.getOfferDeliveryDataMap(offerDeliveryResponse);
        List<UUID> offerIds = dataMap.keySet().stream().toList();

        assertEquals(offerDeliveryResponse.getData().size(), offerIds.size());
        var count = 0;
        for (UUID id: offerIds) {
            assertEquals(offerDeliveryResponse.getData().get(count).getId(), id);
            count++;
        }

    }
}
