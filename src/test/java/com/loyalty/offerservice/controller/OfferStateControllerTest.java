package com.loyalty.offerservice.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.loyalty.offerservice.TestHelper;
import com.loyalty.offerservice.model.dto.request.OfferState;
import com.loyalty.offerservice.model.dto.request.OfferStates;
import com.loyalty.offerservice.service.offerstate.OfferStateClient;
import net.minidev.json.JSONArray;
import org.json.JSONObject;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.util.List;
import java.util.stream.Stream;

import static org.mockito.ArgumentMatchers.any;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest
@AutoConfigureMockMvc
public class OfferStateControllerTest {

    @Autowired
    private MockMvc mvc;

    @MockBean
    OfferStateClient stateClient;

    private static final String correntRequestBody = "{\"states\":[{\"name\":\"SAVE\",\"value\":\"SAVED\"}]}";

    @Test
    public void updateStatus() throws Exception {

        Mockito.doNothing().when(stateClient).updateOffer(any(), any(),any());
        mvc.perform(MockMvcRequestBuilders.put("/offers/e0e93712-aa92-4489-a258-83d751763a1d/states")
                        .contentType(MediaType.APPLICATION_JSON)
                        .header("X-Correlation-Id", "550e8400-e29b-41d4-a716-446655440000")
                        .header("X-Origin-Client", "mobile")
                        .header("Collector-Number", "80000000110")
                        .header("X-Timestamp", "2023-04-03T00:00:00")
                        .content(correntRequestBody))
                .andExpect(status().isNoContent());
    }

    @Test
    public void updateStatusWithoutToken() throws Exception {

        Mockito.doNothing().when(stateClient).updateOffer(any(), any(),any());
        mvc.perform(MockMvcRequestBuilders.put("/offers/e0e93712-aa92-4489-a258-83d751763a1d/states")
                        .contentType(MediaType.APPLICATION_JSON)
                        .header("X-Correlation-Id", "550e8400-e29b-41d4-a716-446655440000")
                        .header("X-Origin-Client", "mobile")
                        .header("X-Timestamp", "2023-04-03T00:00:00")
                        .content(correntRequestBody))
                .andExpect(status().isBadRequest());
    }

    @Test
    public void updateStatusWithPost() throws Exception {

        Mockito.doNothing().when(stateClient).updateOffer(any(), any(),any());
        mvc.perform(MockMvcRequestBuilders.post("/offers/e0e93712-aa92-4489-a258-83d751763a1d/states")
                        .contentType(MediaType.APPLICATION_JSON)
                        .header("X-Correlation-Id", "550e8400-e29b-41d4-a716-446655440000")
                        .header("X-Origin-Client", "mobile")
                        .header("Authorization", "e58ed763-928c-4155-bee9-fdbaaadc15f3")
                        .header("X-Timestamp", "2023-04-03T00:00:00")
                        .content(correntRequestBody))
                .andExpect(status().isMethodNotAllowed());
    }

    @ParameterizedTest
    @CsvSource({"UNSAVED,UNSAVED", "SAVE,TEST", "OPT_IN,OPTED_TEST"})
    public void updateStatusWithIncorrectBody(String name, String value) throws Exception {

        OfferState offerState = new OfferState();
        offerState.setName(name);
        offerState.setValue(value);

        OfferStates offerStates = new OfferStates();
        offerStates.setStates(List.of(offerState));


        String json = new ObjectMapper().writeValueAsString(offerStates);

        Mockito.doNothing().when(stateClient).updateOffer(any(), any(),any());
        mvc.perform(MockMvcRequestBuilders.put("/offers/133b7d2c-9223-446e-a594-2c654b23fb50/states")
                        .contentType(MediaType.APPLICATION_JSON)
                        .header("X-Origin-Client", "unit:test")
                        .header("Authorization", "e58ed763-928c-4155-bee9-fdbaaadc15f3")
                        .content(json))
                .andExpect(status().is4xxClientError());
    }

}