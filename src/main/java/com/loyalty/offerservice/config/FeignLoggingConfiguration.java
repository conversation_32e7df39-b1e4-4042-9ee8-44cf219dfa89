package com.loyalty.offerservice.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import feign.Logger;
import feign.Request;
import feign.Response;
import feign.Util;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

import static net.logstash.logback.marker.Markers.appendEntries;

@Slf4j
public class FeignLoggingConfiguration {

    @Bean
    Logger.Level feignLoggerLevel() {
        return Logger.Level.BASIC;
    }

    @Bean
    Logger feignLogger() {
        return new CustomLogger();
    }


    private static class CustomLogger extends Logger {

        @Override
        protected void log(String configKey, String format, Object... args) {
        }

        @Override
        protected void logRequest(String configKey, Level logLevel, Request request) {
            Map<String, Object> reqLog = new HashMap<>();
            reqLog.put("method", request.httpMethod());
            reqLog.put("url", request.url());
            reqLog.put("queryParam", request.requestTemplate().queries());
            reqLog.put("headers", request.headers());
            if (request.body() != null) {
                reqLog.put("body", Util.decodeOrDefault(request.body(), StandardCharsets.UTF_8, ""));
            }
            reqLog.put("feignMethod", configKey);

            log.info(appendEntries(reqLog), "Feign Request Details: " + request.requestTemplate().feignTarget().name());
        }

        @Override
        protected Response logAndRebufferResponse(String configKey, Level logLevel, Response response, long elapsedTime)
                throws IOException {

            int status = response.status();

            Map<String, Object> resLog = new HashMap<>();
            resLog.put("feignMethod", configKey);
            resLog.put("elapsedTime", elapsedTime);
            resLog.put("status", status);
            resLog.put("headers", response.headers());

            String message = "Feign Response Details: " + response.request().requestTemplate().feignTarget().name();

            // Using the same logic used in Feign.Logger
            int bodyLength = 0;
            if (response.body() != null && !(status == 204 || status == 205)) {
                // HTTP 204 No Content "...response MUST NOT include a message-body"
                // HTTP 205 Reset Content "...response MUST NOT include an entity"
                byte[] bodyData = Util.toByteArray(response.body().asInputStream());
                bodyLength = bodyData.length;
                // logging body when debug is enabled otherwise only log body when an error is received
                if (bodyLength > 0 && (log.isDebugEnabled() || status != 200)) {
                    ObjectMapper objectMapper = new ObjectMapper();
                    Map bodyString = objectMapper.readValue(bodyData, Map.class);
                    resLog.put("body", bodyString);
                }
                log.info(appendEntries(resLog), message);
                return response.toBuilder().body(bodyData).build();
            }
            log.info(appendEntries(resLog), message);
            return response;
        }
    }
}
