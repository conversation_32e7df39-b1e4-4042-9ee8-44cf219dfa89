package com.loyalty.offerservice.model.dto.response.partner;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.loyalty.offerservice.cache.Cacheable;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper=false)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PartnerObject extends Cacheable {
    private String name;
    private String baseEarnRate;
    private String baseEarnRateShort;
    private Integer priority;
    private UUID id;
    private String url;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private Integer revision;
    private List<String> regions;
    private List<String> sponsorCodes;
    private List<String> type;
    private List<HeartLogo> fullLogo;
    private String maxCashMiles;
    private Boolean getMilesFeatured;
    private List<HeartLogo> heartLogo;
    private List<String> partnerCategory;
    private Integer categoryPriority;
    private String partnerUrl;
    private String betaPartner;
    private String locale;
    private String terms;
    private String pageUrl;
}
