package com.loyalty.offerservice.model.dto.response.offerdelivery;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
@ToString
@Component
@JsonIgnoreProperties(ignoreUnknown = true)
public class OfferDeliveryOfferObject {

    private UUID id;
    private LocalDateTime startDate;
    private LocalDateTime endDate;
    private LocalDateTime displayDate;
    private UUID partnerId;
    private String type;
    private Integer displayPriority;
    private List<String> regions;
    private UUID promotionId;
    private Boolean massOffer;
    private UUID categoryId;
    private UUID subCategoryId;
    private List<String> availability;
    private Boolean active;
    private String programType;
    private Boolean eventBased;

}
