package com.loyalty.offerservice.util;

import com.loyalty.offerservice.model.dto.request.OfferState;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

public class States {
    private static OfferState.OfferStateBuilder offerStateBuilder = OfferState.builder();

    public static final OfferState SAVED = offerStateBuilder.name("SAVE").value("SAVED").build();
    public static final OfferState UNSAVED = offerStateBuilder.name("SAVE").value("UNSAVED").build();
    public static final OfferState OPT_IN = offerStateBuilder.name("OPT_IN").value("OPTED_IN").build();

    public static final List<OfferState> STATES = Collections.unmodifiableList(
            Arrays.asList(SAVED, UNSAVED, OPT_IN)
    );

    public static OfferState getInstance(String value) {
        if (value == null || value.isEmpty() || value.trim().isEmpty())
            return null;

        OfferState validState = lookUpByValue(value);

        if (validState == null) {
            throw new IllegalArgumentException(String.format("States cannot have the value of %s", value));
        }

        return validState;
    }

    static final Map<String, List<String>> statePairMapping = Map.of(
            "SAVE", List.of("SAVED", "UNSAVED"),
            "OPT_IN", List.of("OPTED_IN")
    );

    public static boolean stateValidator(String name, String value) throws NullPointerException {
        return statePairMapping.get(name).contains(value);
    }

    private static OfferState lookUpByValue(String value) {
        for (OfferState state : STATES) {
            if (value.equals(state.getValue())) {
                return state;
            }
        }

        return null;
    }

}
