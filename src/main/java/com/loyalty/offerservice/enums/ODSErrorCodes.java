package com.loyalty.offerservice.enums;
import lombok.Getter;

import java.util.Arrays;

@Getter
public enum ODSErrorCodes {

    UNAUTHORIZED("err-login-required"),
    OFFER_EXPIRED("err-expired"),
    OFFER_NOT_LIVE("err-not-live"),
    REGION_NOT_MATCH("err-region"),
    OFFER_EXCLUSIVE("err-exclusive"),
    OFFER_NOT_FOUND("err-not-found"),
    PARTNER_NOT_FOUND("err-partner-not-found"),
    INVALID_REQUEST("err-invalid-request"),
    INTERNAL_SERVER_ERROR("err-server-internal"),
    EVENT_BASED_OFFER_AUTHORIZATION_ERROR("err-ebo-login"),
    EVENT_BASED_OFFER_EXCLUSIVE("err-ebo-exclusive"),
    SUCCESS("success"),
    UNKNOWN("unknown");

    private final String code;
    ODSErrorCodes(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public static ODSErrorCodes of(String code) {
        return Arrays.stream(ODSErrorCodes.values())
                .filter(errorCode -> errorCode.getCode().equals(code))
                .findFirst()
                .orElse(UNKNOWN);
    }

}
