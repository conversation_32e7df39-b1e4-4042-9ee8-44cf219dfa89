package com.loyalty.offerservice.service;

import com.loyalty.offerservice.exception.OffersNotFoundException;
import com.loyalty.offerservice.helper.CorsHelper;
import com.loyalty.offerservice.model.dto.request.OfferRequest;
import com.loyalty.offerservice.model.dto.response.*;
import com.loyalty.offerservice.model.dto.response.buildresponse.BuildOfferServiceResponse;
import com.loyalty.offerservice.model.dto.response.offerdelivery.OfferDeliveryOfferObject;
import com.loyalty.offerservice.model.dto.response.offerdelivery.OfferDeliveryResponse;
import com.loyalty.offerservice.model.dto.response.offerresolver.OfferDTO;
import com.loyalty.offerservice.model.dto.response.offerresolver.OfferResolverResponseDTO;
import com.loyalty.offerservice.model.dto.response.partner.PartnerObject;
import com.loyalty.offerservice.service.async.Partner;
import com.loyalty.offerservice.service.partner.PartnerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.stream.Collectors;

import static net.logstash.logback.marker.Markers.append;
import static net.logstash.logback.marker.Markers.appendEntries;

@Service
@Slf4j
@RequiredArgsConstructor
public class OfferServiceImpl extends BuildOfferServiceResponse implements OfferService {

    @Value("${cache-control.max-age}")
    String cacheMaxAge;

    @Autowired
    PartnerService partnerService;

    @Autowired
    Partner partnerProcess;

    @Autowired
    CorsHelper corsHelper;

    public static final String COLLECTOR_START_DATE_PLACEHOLDER = "<COLLECTOR_START_DATE>";

    public static final String COLLECTOR_END_DATE_PLACEHOLDER = "<COLLECTOR_END_DATE>";

    @Override
    public OfferServiceExtendedOfferResponse getOffers(OfferRequest request) throws Exception {
        // Offer Delivery: to get offer id's

        // Partner: to get partner id's with their priority

        // Offer Resolver : to get offer details

        // Offer State: only when member calls to resolve offer state
        if (request.getFilters().getStates() != null && request.getCollectorNumber() != null) {
            return buildResponseByStates(request);
        } else {
            return buildResponse(request);
        }
    }

    /**
     * This method is to add response headers, which allows us to unlock features:
     * - Adding cache header based on if the request is public or collector
     * - Adding CORS headers based on origin
     *
     * @param request
     * @return HttpHeaders
     */
    @Override
    public HttpHeaders addResponseHeaders(OfferRequest request) {
        HttpHeaders responseHeaders = new HttpHeaders();

        if (request.getCollectorNumber() != null) {
            responseHeaders.set("Cache-Control", "no-cache");
        } else {
            responseHeaders.set("Cache-Control", "public, max-age="+ cacheMaxAge);
        }

        responseHeaders.set("X-Correlation-Id", request.getCorrelationId().toString());

        // Add CORS headers based on origin
        String origin = request.getOrigin();
        if (origin != null && corsHelper.isAllowedOrigin(origin)) {
            HttpHeaders corsHeaders = corsHelper.getAuthenticatedCorsHeaders(origin);
            responseHeaders.addAll(corsHeaders);
        } else {
            HttpHeaders corsHeaders = corsHelper.getPublicCorsHeaders();
            responseHeaders.addAll(corsHeaders);
        }

        return responseHeaders;
    }

    @Override
    public OfferServiceExtendedOfferResponseForOfferId getOfferByOfferId(OfferRequest request)
            throws GenericException, IOException {
        return buildResponseForOffersById(request);
    }



    /**
     * Build out Offer Response
     *
     * @param offerResolverResponse OfferResolverResponseDTO (sync data)
     * @param offerRequest          OfferRequest
     * @param offerStateResponse    Map<UUID, List<Map<String, Object>>> (sync data)
     * @return List of OfferServiceResponse
     * @throws IOException
     */
    @Override
    public List<OfferServiceResponse> getOfferResponse(OfferResolverResponseDTO offerResolverResponse, OfferRequest offerRequest, Map<UUID, List<Map<String, Object>>> offerStateResponse) throws IOException {
        List<OfferServiceResponse> offerResponses = new ArrayList<>();

        List<UUID> partnerIds = createPartnerIdList(offerResolverResponse.getOffers());

        Map<UUID, PartnerObject> partnerResponse = partnerProcess.retrieveProcess(partnerIds, offerRequest);


        // Throw an error if there is nothing returned from the Offer resolver
        if (offerResolverResponse.getOffers().isEmpty()) {
            throw new OffersNotFoundException("OFFER_NOT_FOUND", "Offer not found, try again later. Offer Resolver contained no Offer information.");
        }

        // Loop through all the offers returned from offer resolver
        offersLoop:
        for (OfferDTO offerResolverObject : offerResolverResponse.getOffers()) {

            // Extract specific partner data
            PartnerObject partnerResponseObject = partnerResponse.getOrDefault(offerResolverObject.getPartnerId(), null);

            // If partner information could not be found
            if (partnerResponseObject == null) {
                //this offer has no partner info and therefore must be skipped
                log.error(append("offerId", offerResolverResponse.getOffers().get(0)), "Issue retrieving information about the offer. (Partner info not found)");
                continue offersLoop;
            }

            // Main data fill
            OfferServiceResponse offerServiceResponse = createOfferServiceResponseObject(offerResolverObject, partnerResponseObject);

            // Data Fill from State
            if (offerStateResponse != null && offerStateResponse.containsKey(offerResolverObject.getId())) {
                List<Map<String, Object>> stateList = offerStateResponse.get(offerResolverObject.getId());
                offerServiceResponse.setStates(stateList);
            }

            offerResponses.add(offerServiceResponse);

        }

        // Check to see if we were able to successfully build atleast one offer object
        if (offerResponses.isEmpty()) {
            throw new OffersNotFoundException("NO_OFFERS_FOUND", "No offers found, try again later. Failed to build Offer response.");
        }

        return offerResponses;

    }


    /**
     * Build out Offer Response - Async & With Offer Delivery Service
     *
     * @param offerResolverResponse OfferResolverResponseDTO (async: CompletableFuture)
     * @param offerRequest          OfferRequest
     * @param offerStateResponse    Map<UUID, List<Map<String, Object>>> (async: CompletableFuture)
     * @param offerDeliveryIdList  List of OfferIds from OfferDelivery (sync data)
     * @return List of OfferServiceResponse
     * @throws IOException
     */
    @Override
    public List<OfferServiceResponse> getOfferResponseAsync(CompletableFuture<OfferResolverResponseDTO> offerResolverResponse, OfferDeliveryResponse offerDeliveryResponse, OfferRequest offerRequest, CompletableFuture<Map<UUID, List<Map<String, Object>>>> offerStateResponse, List<UUID> offerDeliveryIdList) throws IOException {
        List<OfferServiceResponse> offerResponses = new ArrayList<>();

        List<UUID> partnerIds = createPartnerIdList(offerDeliveryResponse.getMetadata());

        CompletableFuture<Map<UUID, PartnerObject>> partnerResponse = partnerProcess.retrieveProcessAsync(partnerIds, offerRequest, MDC.getCopyOfContextMap());

        //Wait for all ASYNC calls to respond back
        try {
            CompletableFuture.allOf(offerResolverResponse, offerStateResponse, partnerResponse).join();
        } catch (CompletionException ex) {
            Map<String, Object> data = new HashMap<>();
            data.put("offerResolverException", offerResolverResponse.isCompletedExceptionally());
            data.put("offerStateException", offerStateResponse.isCompletedExceptionally());
            data.put("partnerException", partnerResponse.isCompletedExceptionally());
            log.error(appendEntries(data)
                    , "Exception occurred when waiting for offerResolverResponse, offerStateResponse, partnerResponse to complete", ex);
            throw ex;
        }
        Map<UUID, OfferDTO> offerResolverMap = offerResolverResponse.join().getOffers().stream().collect(Collectors.toMap(OfferDTO::getId, offerDTO -> offerDTO));
        Map<UUID, List<Map<String, Object>>> offerStateMap = offerStateResponse.join();
        Map<UUID, PartnerObject> partnerMap = partnerResponse.join();


        //Metadata fill
        createOfferServiceResponseMetadata(offerDeliveryResponse.getMetadata(), partnerMap);

        // Throw an error if there is nothing returned from the Offer resolver
        if (offerResolverMap.isEmpty()) {
            throw new OffersNotFoundException("OFFER_NOT_FOUND", "Offer not found, try again later. Offer Resolver contained no Offer information.");
        }

        // The delivery offer must be retrieved using the offer id, for efficiency purposes, the response is converted to a map
        Map<UUID, OfferDeliveryOfferObject> deliveryResponseMap = offerDeliveryResponse.getData().stream()
                .collect(Collectors.toMap(OfferDeliveryOfferObject::getId, offer -> offer));

        // Loop through all the offers returned from offer resolver with the order from ODS
        offersLoop:
        for ( UUID offerId: offerDeliveryIdList) {
            OfferDTO offerResolverObject = offerResolverMap.get(offerId);
            // If resolver doesnt have the offerId
            if (offerResolverObject == null){
                log.error(append("offerId", offerId), "Resolver response did not contain the offerId.");
                continue offersLoop;
            }

            // Extract specific partner data
            PartnerObject partnerResponseObject = partnerMap.getOrDefault(offerResolverObject.getPartnerId(), null);

            // If partner information could not be found
            if (partnerResponseObject == null) {
                //this offer has no partner info and therefore must be skipped
                log.error(append("offerId", offerId), "Issue retrieving information about the offer. (Partner info not found)");
                continue offersLoop;
            }

            // Main data fill
            OfferServiceResponse offerServiceResponse = createOfferServiceResponseObject(offerResolverObject, partnerResponseObject);

            // The offer retrieved from the delivery response
            OfferDeliveryOfferObject deliveryOffer = deliveryResponseMap.get(offerId);

            replaceOfferDatesODS(deliveryOffer, offerServiceResponse, offerRequest);

            // Data Fill from State
            if (offerStateMap != null && offerStateMap.containsKey(offerResolverObject.getId())) {
                List<Map<String, Object>> stateList = offerStateMap.get(offerResolverObject.getId());
                offerServiceResponse.setStates(stateList);
            }

            offerResponses.add(offerServiceResponse);

        }

        // Check to see if we were able to successfully build atleast one offer object
        if (offerResponses.isEmpty()) {
            throw new OffersNotFoundException("NO_OFFERS_FOUND", "No offers found, try again later. Failed to build Offer response.");
        }

        return offerResponses;

    }

    /**
     * Build out Offer Response by ID
     *
     * @param offerResolverResponse OfferResolverResponseDTO (sync data)
     * @param offerRequest          OfferRequest
     * @param offerStateResponse    Map<UUID, List<Map<String, Object>>> (sync data)
     * @return Single Offer - OfferServiceResponse
     * @throws IOException
     */
    @Override
    public OfferServiceResponse getOfferResponseForOfferId(OfferResolverResponseDTO offerResolverResponse, OfferDeliveryOfferObject deliveryOfferObject, OfferRequest offerRequest, Map<UUID, List<Map<String, Object>>> offerStateResponse) throws IOException {

        // Throw an error if there is nothing returned from the Offer resolver
        if (offerResolverResponse.getOffers().isEmpty() || offerResolverResponse.getOffers().get(0) == null) {
            throw new OffersNotFoundException("OFFER_NOT_FOUND", "Offer not found, try again later.");
        }
        // Since this is by ID, only one offer should be returned by OfferResolver
        OfferDTO offerResolverObject = offerResolverResponse.getOffers().get(0);

        // Get Partner information
        Map<UUID, PartnerObject> partnerResponse = partnerProcess.retrieveProcess(
                Collections.singletonList(offerResolverObject.getPartnerId()), offerRequest);

        // Extract specific partner data
        PartnerObject partnerResponseObject = partnerResponse.getOrDefault(offerResolverObject.getPartnerId(), null);

        // If partner information could not be found
        if (partnerResponseObject == null) {
            //this offer has no partner info and therefore must be skipped
            log.error(append("offerId", offerResolverResponse.getOffers().get(0)), "Issue retrieving information about the offer. (Partner info not found)");
            throw new OffersNotFoundException("NO_OFFERS_FOUND", "No partner information found, for partnerId: " + offerResolverObject.getPartnerId());
        }

        // Main data fill
        OfferServiceResponse offerServiceResponse = createOfferServiceResponseObject(offerResolverObject, partnerResponseObject);

        // Data Fill from State
        if (offerStateResponse != null && offerStateResponse.containsKey(offerResolverObject.getId())) {
            List<Map<String, Object>> stateList = offerStateResponse.get(offerResolverObject.getId());
            offerServiceResponse.setStates(stateList);
        }

        replaceOfferDatesODS(deliveryOfferObject, offerServiceResponse, offerRequest);

        return offerServiceResponse;
    }

    public static void replaceOfferDatesODS(OfferDeliveryOfferObject deliveryOffer, OfferServiceResponse offerServiceResponse, OfferRequest offerRequest) {
        DateTimeFormatter formatter = getReplacementDateFormatter(offerRequest);

        // If the delivery offer is found and is an event based offer, we use the dates from the ODS and change the legal text
        if (deliveryOffer != null && Boolean.TRUE.equals(deliveryOffer.getEventBased())) {
            offerServiceResponse.setStartDate(deliveryOffer.getStartDate());
            offerServiceResponse.setEndDate(deliveryOffer.getEndDate());
            offerServiceResponse.setDisplayDate(deliveryOffer.getDisplayDate());

            String legalText = offerServiceResponse.getLegalText();

            legalText = legalText.replace(COLLECTOR_START_DATE_PLACEHOLDER, deliveryOffer.getStartDate().format(formatter));
            legalText = legalText.replace(COLLECTOR_END_DATE_PLACEHOLDER, deliveryOffer.getEndDate().format(formatter));

            offerServiceResponse.setLegalText(legalText);
        }
    }

    public static void replaceOfferDatesODS(OfferDeliveryOfferObject deliveryOffer, OfferDTO offer, OfferRequest offerRequest) {
        DateTimeFormatter formatter = getReplacementDateFormatter(offerRequest);

        // If the delivery offer is found and is an event based offer, we use the dates from the ODS and change the legal text
        if (deliveryOffer != null && Boolean.TRUE.equals(deliveryOffer.getEventBased())) {
            offer.setStartDate(deliveryOffer.getStartDate());
            offer.setEndDate(deliveryOffer.getEndDate());
            offer.setDisplayDate(deliveryOffer.getDisplayDate());

            String legalText = offer.getLegalText();

            legalText = legalText.replace(COLLECTOR_START_DATE_PLACEHOLDER, deliveryOffer.getStartDate().format(formatter));
            legalText = legalText.replace(COLLECTOR_END_DATE_PLACEHOLDER, deliveryOffer.getEndDate().format(formatter));

            offer.setLegalText(legalText);
        }
    }

    private static DateTimeFormatter getReplacementDateFormatter(OfferRequest offerRequest) {
        // Choosing the date formatter as per the language header passed in, Default to en-US
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MMMM d, yyyy", Locale.US);
        if (offerRequest !=null && offerRequest.getLanguage().matches("fr-CA")){
            formatter = DateTimeFormatter.ofPattern("d MMMM yyyy", Locale.CANADA_FRENCH);
        }

        return formatter;
    }
    @Override
    public OfferServiceExtendedOfferResponseForOfferId getOfferByOfferIdAdmin(OfferRequest request)
            throws GenericException, IOException {
        return buildResponseForOffersByIdAdmin(request);
    }
}
