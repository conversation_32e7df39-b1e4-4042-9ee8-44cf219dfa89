package com.loyalty.offerservice.model.dto.response.category;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.loyalty.offerservice.model.dto.response.CommonMetadata;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.UUID;

@Data
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CategoriesResponse {
    private List<Category> results;
    private static String locale = "en-US";

    public CategoriesResponse(String locale) {
        CategoriesResponse.locale = locale;
    }

    public void setCounts(CommonMetadata commonMetadata) {
        if (results != null && commonMetadata.getCategories() != null) {
            setCountsWithRecursion(results, commonMetadata.getCategories(), commonMetadata.getSubCategories());
        }
    }

    private void setCountsWithRecursion(List<Category> results, List<CommonMetadata.MetadataCounts> categories,
            List<CommonMetadata.MetadataCounts> subcategories) {
        for (Category result : results) {
            setCountByMetadataList(result, categories);
            if (result.subCategories != null && subcategories != null) {
                setCountsWithRecursion(result.subCategories, subcategories, subcategories);
            }
        }

    }

    private void setCountByMetadataList(Category result, List<CommonMetadata.MetadataCounts> metadataList) {
        for (CommonMetadata.MetadataCounts metadataCount : metadataList) {
            if (metadataCount.getId().equals(result.getId())) {
                result.setCount(Math.toIntExact(metadataCount.getCount()));
                break;
            }
        }
    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    public static class Category {
        private UUID id;

        private String label;
        private Integer count = 0;

        @JsonAlias({ "childCategories" })
        private List<Category> subCategories;

        @JsonProperty("translations")
        private void unpackNested(Map<String, Object> translations) {
            this.label = (String) translations.get(locale);
        }
    }
}
