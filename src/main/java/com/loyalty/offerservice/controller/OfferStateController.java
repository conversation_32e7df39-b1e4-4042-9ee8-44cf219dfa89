package com.loyalty.offerservice.controller;

import com.loyalty.offerservice.helper.CorsHelper;
import com.loyalty.offerservice.model.dto.request.OfferStateUpdateRequest;
import com.loyalty.offerservice.model.dto.request.OfferStates;
import com.loyalty.offerservice.service.offerstate.OfferStateService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

import static net.logstash.logback.marker.Markers.append;

@RestController
@RequestMapping(value = "/offers", headers = {"Accept=application/json"})
@Validated
@Slf4j
public class OfferStateController {

    @Autowired
    private OfferStateService offerStateService;

    @Autowired
    CorsHelper corsHelper;

    @PutMapping(path = "/{offerId}/states")
    public ResponseEntity updateOfferState(
            @RequestHeader(value = "X-Correlation-Id", required = false) UUID correlationId,
            @RequestHeader(value = "X-Origin-Client") String originClient,
            @RequestHeader(value = "Collector-Number") Long collectorNumber,
            @RequestHeader(value = "Origin", required = false) String origin,
            @PathVariable UUID offerId,
            @RequestBody OfferStates offerStates
    ) throws Exception {
        if (correlationId == null) {
            correlationId = UUID.randomUUID();
        }

        MDC.put("correlationId", String.valueOf(correlationId));
        MDC.put("originClient", originClient);
        MDC.put("origin", origin);
        MDC.put("collectorNumber", String.valueOf(collectorNumber));
        MDC.put("requestType", "PUT");
        MDC.put("uri", String.format("/offers/%s/states", offerId));

        OfferStateUpdateRequest request = OfferStateUpdateRequest.builder()
                .correlationId(correlationId)
                .originClient(originClient)
                .offerId(offerId)
                .collectorId(collectorNumber).build();

        log.info(append("body", offerStates), "PUT Offer State: Request Received");

        offerStateService.updateOfferState(offerStates, request);

        log.info(append("status", HttpStatus.NO_CONTENT.value()), "PUT Offer State: Response");

        MDC.clear();


        return ResponseEntity.status(HttpStatus.NO_CONTENT)
                .header("X-Correlation-Id", request.getCorrelationId().toString())
                .build();

    }

}
