{"NetworkStackName": "program-interface-prod", "ClusterStackName": "program-interface-prod-fg-ecs", "ListenerPort": "34075", "Environment": "prod", "AppName": "offer-service", "AppVersion": "1.0.0", "AppContainerPort": "8080", "KinesisStackName": "prod-kinesissplunk", "AmrpwlAccountId": "************", "ResolverFunctionName": "nova-offer-resolver-api-getOffers-lambda", "ResolverFunctionRegion": "us-east-1", "PagerDutyIntegrationKey": "734af4fdd18f4405c05cbc216ea4dcb1", "AppDesiredCount": "3", "AppMinCount": "3", "AppMaxCount": "10", "PagerDutyHighCpuThreshold": "90", "PagerDutyMemoryThreshold": "80", "HealthyHostThreshold": "1"}