package com.loyalty.offerservice.controller;

import com.loyalty.offerservice.component.OfferFacade;
import com.loyalty.offerservice.enums.LanguageCode;
import com.loyalty.offerservice.model.dto.request.OfferRequest;
import com.loyalty.offerservice.model.dto.response.OfferServiceExtendedOfferResponseForOfferId;
import com.loyalty.offerservice.service.OfferService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.UUID;

import static net.logstash.logback.marker.Markers.append;

@RestController
@RequestMapping("/admin")
@Slf4j
public class AdminController {

    private final OfferFacade adminOfferFacade;
    private final OfferService offerService;

    public AdminController(OfferFacade adminOfferFacade,
                           OfferService offerService) {
        this.adminOfferFacade = adminOfferFacade;
        this.offerService = offerService;
    }

    @GetMapping("/offers/{offerId}")
    public ResponseEntity<?> getOffersByOfferIdAdmin(
            @PathVariable("offerId") UUID offerId,
            @RequestHeader(value = "X-Timestamp", required = false) LocalDateTime localDate,
            @RequestHeader(value = "X-Correlation-Id", required = false) UUID correlationId,
            @RequestHeader(value = "X-Origin-Client") String originClient,
            @RequestHeader(value = "Collector-Number", required = false) Long collectorNumber,
            @RequestHeader(value = "Accept-Language", defaultValue = "en-US") LanguageCode acceptLanguage,
            @RequestHeader(value = "Origin", required = false) String origin
    ) throws IOException {

        UUID finalCorrelationId = (correlationId != null) ? correlationId : UUID.randomUUID();

        adminOfferFacade.configureMDC(finalCorrelationId, originClient, origin, offerId, collectorNumber);

        OfferRequest request = adminOfferFacade.buildOfferRequest(offerId, localDate, finalCorrelationId, originClient,
                collectorNumber, acceptLanguage, origin);

        OfferServiceExtendedOfferResponseForOfferId responseBody = offerService.getOfferByOfferIdAdmin(request);

        HttpHeaders responseHeaders = offerService.addResponseHeaders(request);

        log.info(append("status", HttpStatus.OK.value()).and(append("headers", responseHeaders)),
                "GET Offer By OfferId for Admin: Response");

        adminOfferFacade.closeMDC();

        return ResponseEntity.ok().headers(responseHeaders).body(responseBody);
    }
}

