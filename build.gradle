buildscript {
    ext {
        springBootVersion = '3.3.12'
        springCloudAwsVersion = '2.4.4'
        gradleReleasePluginVersion = '2.7.0'
        gradleTestSetPluginVersion = '2.1.1'
        gradleDependenyManagementPluginVersion = '1.1.0'
        groovyVersion = '4.0.13'
        spockVersion = '2.4-M1-groovy-4.0'
        springDependencyManageVersion = '1.1.0'
        researchgateReleaseVersion = '3.0.2'
        springBootSecurityVersion = '6.1.6'
    }

    ext['groovy.version'] = '4.0.13'

    repositories {
        mavenCentral()
    }

    dependencies {
        classpath("org.springframework.boot:spring-boot-gradle-plugin:${springBootVersion}")
        classpath("net.researchgate:gradle-release:${gradleReleasePluginVersion}")
        classpath("org.unbroken-dome.gradle-plugins:gradle-testsets-plugin:${gradleTestSetPluginVersion}")
        classpath("io.spring.gradle:dependency-management-plugin:${gradleDependenyManagementPluginVersion}")
    }
}

plugins {
    id 'org.springframework.boot' version "$springBootVersion"
    id 'io.spring.dependency-management' version "$springDependencyManageVersion"
    id "net.researchgate.release" version "$researchgateReleaseVersion"
    id 'java'
    id 'groovy'
    id 'eclipse'
    id 'idea'
    id "jacoco"
    id 'java-library'
}

sourceCompatibility = 17
targetCompatibility = 17

repositories {
    mavenCentral()
}

sourceSets {
    test {
        resources {
            srcDir file('src/test/java')
            exclude '**/*.java'
        }
    }
    integrationTest {
        java {
            srcDirs = ['src/integrationTest/java']
        }
    }
}
configurations {
    integrationTestImplementation.extendsFrom implementation
    integrationTestRuntimeOnly.extendsFrom runtimeOnly
    integrationTestImplementation.extendsFrom testImplementation
}
dependencies {
    implementation("org.springframework.boot:spring-boot-starter-security")
    implementation("org.springframework.boot:spring-boot-starter-web") // No version needed here either
    implementation("org.springframework.boot:spring-boot-starter-cache") // Or here
    implementation("net.logstash.logback:logstash-logback-encoder:7.4")
    // tag::actuator[]
    implementation("org.springframework.boot:spring-boot-starter-actuator") // Or here
    implementation("org.springframework.boot:spring-boot-starter-validation") // Or here    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
    implementation 'org.projectlombok:lombok:1.18.38'
    // end::actuator[]
    // tag::tests[]
    testImplementation("org.spockframework:spock-core:${spockVersion}")
    testImplementation("org.spockframework:spock-spring:${spockVersion}")
    testImplementation ("org.apache.groovy:groovy-all:${groovyVersion}")
    testImplementation("org.springframework.boot:spring-boot-starter-test:${springBootVersion}")
    // end::tests[]
    implementation("org.springframework.cloud:spring-cloud-starter-openfeign:4.1.3")
    implementation("org.springframework.cloud:spring-cloud-starter-circuitbreaker-resilience4j:3.1.2")
    implementation group: 'com.intuit.karate', name: 'karate-core', version: '1.3.0'
    implementation group: 'com.intuit.karate', name: 'karate-junit5', version: '1.4.0'
    integrationTestImplementation 'org.junit.jupiter:junit-jupiter:5.7.1'
    implementation 'org.apache.httpcomponents.client5:httpclient5:5.2.1'
    implementation group: 'commons-io', name: 'commons-io', version: '2.14.0'
    implementation 'io.netty:netty-tcnative-boringssl-static:2.0.61.Final'
    implementation group: 'net.masterthought', name: 'cucumber-reporting', version: '5.7.6'
    implementation group: 'software.amazon.awssdk', name: 'lambda', version: '2.19.14'
    implementation group: 'software.amazon.awssdk', name: 'secretsmanager', version: '2.19.14'
    implementation group: 'com.amazonaws', name: 'aws-java-sdk-s3', version: '1.12.261'
    implementation (group: 'io.awspring.cloud', name: 'spring-cloud-starter-aws-secrets-manager-config', version: '2.3.4')
    implementation group: 'net.minidev', name: 'json-smart', version: '2.4.11'
    implementation group: 'com.github.ben-manes.caffeine', name: 'caffeine', version: '3.1.7'

    implementation group: 'org.yaml', name: 'snakeyaml', version: '2.0'
    //implementation group: 'org.springframework', name: 'spring-core'
    implementation group: 'io.netty', name: 'netty-codec-http2', version: '4.1.100.Final'
    implementation group: 'com.google.guava', name: 'guava', version: '32.0.1-jre'
    //implementation group: 'ch.qos.logback', name: 'logback-core', version: '1.4.12'
    //implementation('ch.qos.logback:logback-classic:1.4.12')
    implementation group: 'org.apache.tomcat.embed', name: 'tomcat-embed-websocket', version: '9.0.85'
    implementation group: 'org.springframework.boot', name: 'spring-boot-starter-tomcat'
    implementation group: 'com.fasterxml.jackson.core', name: 'jackson-databind', version: '2.17.0'
    implementation group: 'org.bouncycastle', name: 'bcprov-jdk18on', version: '1.77'
    implementation group: 'org.bouncycastle', name: 'bctls-fips', version: '1.0.18'
    implementation group: 'com.jayway.jsonpath', name: 'json-path', version: '2.9.0'
}

test {
    environment "spring.profiles.active","test"
    useJUnitPlatform()
    testLogging {
        // During 'test' task, output test results without needing --info flag
        events "passed", "skipped", "failed", "standard_error"
    }
    systemProperty "karate.options", System.properties.getProperty("karate.options")
    systemProperty "karate.env", System.properties.getProperty("karate.env")
    outputs.upToDateWhen { false }
}
apply plugin: 'idea'

idea {
    module {
        testSourceDirs += sourceSets.integrationTest.output.classesDirs
    }
}

tasks.register('integrationTest', Test) {
    description = 'Runs integration tests.'
    group = 'verification'

    systemProperty "karate.options", System.properties.getProperty("karate.options")
    systemProperty "karate.env", System.properties.getProperty("karate.env")

    testClassesDirs = sourceSets.integrationTest.output.classesDirs
    classpath = sourceSets.integrationTest.runtimeClasspath
    shouldRunAfter test

    useJUnitPlatform()

    testLogging {
        events "passed"
    }
}

check.dependsOn integrationTest


jacocoTestReport {
    reports {
        xml.enabled true
    }
}

jar{
    enabled = true
}

bootJar {
    //    classifier = 'restservice'
    archiveFileName = "app.jar"
}

task karateDebug(type:JavaExec) {
    classpath = sourceSets.test.runtimeClasspath
    main = 'com.intuit.karate.cli.Main'
}


// release {
// 	failOnSnapshotDependencies = true
// 	revertOnFail = true
// 	git {
// 		requireBranch = 'master'
// 	}
// }
