Feature: Offer Service Cucumber Integration Test

  Background:
    * url apiUrl


  @local @dev @unauthenticated
  Scenario: Offer  service endpoint -Happy Path for an unauthenticated user.
  Validate when the unauthenticated user makes a request with a valid headers and query params, it should return all offers in the response.
  "Given unauthenticated user makes the request
  "When all the headers are valid
  "And all the query params are valid
  "Then we get all offers in the response
    * configure headers = { Accept: 'application/json', X-Origin-Client: 'internal:test:integration', X-Correlation-Id: 'eaa48a1c-4bf0-467d-9bf9-9e5dbea60081'}
    * def query = {region: 'ON'}
    * def mechanism = {"mechanismType":"#string","mechanismLabel":"##string","mechanismValue":"##string"}
    * def url1 = {"url":"#string"}
    * def tiers = {"awardValue":"##number","qualifierValue":"##number","awardLong":"#string", "qualifierLong":"#string"}
    And params query
    And method GET
    Then status 200
    And match response.metadata contains
    """
            {
             "total":"#number"
            }
    """
    And match response.offers[*] contains
    """
            {
             "id":"#uuid",
             "partnerId":"#uuid",
             "partnerLabel":"#string",
             "partnerProfileURL":"#string",
             "partnerLogo":"##(url1)",
             "categoryId":"#uuid",
             "categoryLabel":"#string",
             "subCategoryId":"##uuid",
             "subCategoryLabel":"##string",
             "promotionId":"##uuid",
             "promotionLabel":"##string",
             "programType":"#string",
             "massOffer":"#boolean",
             "description":"##string",
             "awardShort":"#string",
             "qualifierShort":"#string",
             "displayDate":"#string",
             "startDate":"#string",
             "endDate":"#string",
             "displayPriority":"#number",
             "qualifierShort":"#string",
             "mechanisms":"#[] ##(mechanism)",
             "tiers":"#[] ##(tiers)",
             "image":"##(url1)",
             "legalText":"#string",
             "eventBasedOffer": "#boolean"
            }
    """

  @local1 @dev @unauthenticated
  Scenario: Offer service endpoint -Happy Path for an unauthenticated user with french header.
  Validate when the unauthenticated user makes a request with a valid headers and query params, it should return success response.
  "Given unauthenticated user makes the  request
  "When all the headers are valid
  "And all the query params are valid
  "Then we get all offers response in French
    * configure headers = { Accept: 'application/json',Accept-Language:'fr-CA',X-Origin-Client: 'internal:test:integration',X-Correlation-Id: '6211c6ef-bab0-4771-b1ed-eecfc84e02b7'}
    * def query = {region: 'ON'}
    And params query
    And method GET
    Then status 200


  @local @dev @unauthenticated
  Scenario: Offer service endpoint -Happy Path for english language header.
  Validate when the unauthenticated user makes a request with a valid headers and query params, it should return all offers response for specific fields in English.
  "Given unauthenticated user make the request
  "When all the headers are valid
  "And all the query params are valid
  "Then we get all offers response in english
    * configure headers = { Accept: 'application/json',Accept-Language:'en-US', X-Origin-Client: 'internal:test:integration',X-Correlation-Id: 'f4220bf4-dde3-40ba-8b26-3262251c4ebd'}
    * def query = {region: 'ON'}
    And params query
    And method GET
    Then status 200


  @local1 @dev @unauthenticated
  Scenario: Offer service endpoint -UnHappy Path for an unauthenticated user for when limit is set to 97.
  Validate when the unauthenticated user makes a request with a valid headers and invalid query params, it should return error response.
  "Given unauthenticated user makes the  request
  "When all the headers are valid
  "And limit parameter is invalid
  "Then we get error in response.
    * configure headers = { Accept: 'application/json',X-Origin-Client: 'internal:test:integration',X-Correlation-Id: '8392ad8c-0262-4431-8967-97eff82d3a72'}
    * def query = {region: 'ON',limit: '97'}
    And params query
    And method GET
    Then status 400
    And match response contains
     """
            {
         "code":"BAD_REQUEST",
         "message":"getOffers.limit: must be less than or equal to 96"
            }
    """

  @local1 @dev @unauthenticated
  Scenario: Offer service endpoint -UnHappy Path for an unauthenticated user for when region is not passed.
  Validate when the unauthenticated user makes a request with a valid headers and invalid query params, it should return error response.
  "Given unauthenticated user makes the  request
  "When all the headers are valid
  "And region parameter is not passed
  "Then we get error in response.
    * configure headers = { Accept: 'application/json',X-Origin-Client: 'internal:test:integration',X-Correlation-Id: '0a2cb331-afb4-4f5a-b8d9-045f2b63337d'}
    * def query = {limit: '20',offset: '1'}
    And params query
    And method GET
    Then status 400
    And match response contains
     """
            {
      "code": "BAD_REQUEST",
      "message": "Required request parameter 'region' for method parameter type RegionEnum is not present"
            }
    """


  @local1 @dev @unauthenticated
  Scenario: Offer service endpoint -UnHappy Path for an unauthenticated user for when region is passed as ON!.
  Validate when the unauthenticated user makes a request with a valid headers and invalid query params, it should return error response.
  "Given unauthenticated user makes the  request
  "When all the headers are valid
  "And region parameter is passed as ON!
  "Then we get error in response.
    * configure headers = { Accept: 'application/json',X-Origin-Client: 'internal:test:integration',X-Correlation-Id: '68ae4a77-cc59-4126-99a7-11385079eff3'}
    * def query = {region: 'ON!'}
    And params query
    And method GET
    Then status 400
    And match response contains
     """
            {
     "code": "BAD_REQUEST",
     "message": "Failed to convert value of type 'java.lang.String' to required type 'com.loyalty.offerservice.enums.RegionEnum'; Failed to convert from type [java.lang.String] to type [@org.springframework.web.bind.annotation.RequestParam com.loyalty.offerservice.enums.RegionEnum] for value [ON!]"
            }
    """


  @local1 @dev @unauthenticated
  Scenario: Offer service endpoint -UnHappy Path for an unauthenticated user for when x-origin client is not passed.
  Validate when the unauthenticated user makes a request with a valid headers and invalid query params, it should return error response.
  "Given unauthenticated user makes the  request
  "When x -origin client header is not passed
  "And valid query params are passed
  "Then we get error in response.
    * configure headers = { Accept: 'application/json',X-Correlation-Id: '677567fe-ef70-46a2-b618-d5e0a1c18801'}
    * def query = {limit: '20',offset: '1'}
    And params query
    And method GET
    Then status 400
    And match response contains
     """
            {
      "code": "BAD_REQUEST",
      "message": "Required request header 'X-Origin-Client' for method parameter type String is not present"
            }
    """

  @local @dev @unauthenticated
  Scenario: Offer  service endpoint with filters unauthenticated -Filter by single Partner (Shell)
  Validate when the unauthenticated user makes a request with a valid headers and query params, it should return all offers in response.
  "Given unauthenticated user make the request
  "When all the headers are valid
  "And when we filter by single partner query param
  "Then we get all offers for that specific partner in response
    * configure headers = { Accept: 'application/json', X-Origin-Client: 'internal:test:integration', X-Correlation-Id: '85c95b5e-5190-4517-b35c-c5ac307419a3'}
    * def query = {region: 'ON',partner_id: 'b90e694d-3510-4cc2-84be-f9b6f61bd31b'}
    And params query
    And method GET
    Then status 200
    And match each response.offers contains {"partnerId":"b90e694d-3510-4cc2-84be-f9b6f61bd31b"}

  @local @dev @unauthenticated
  Scenario: Offer  service endpoint with filters unauthenticated -Filter by exclude Partner id
  Validate when the unauthenticated user makes a request with a valid headers and query params, it should return all offers in response.
  "Given unauthenticated user make the request
  "When all the headers are valid
  "And when we filter by exclude partner id query param
  "Then we get all offers excluding that partner in response
    * configure headers = { Accept: 'application/json', X-Origin-Client: 'internal:test:integration', X-Correlation-Id: 'a2edece4-6e43-4214-ace7-85e4102469fc'}
    * def query = {region: 'ON',exclude_partner_id: 'b90e694d-3510-4cc2-84be-f9b6f61bd31b'}
    And params query
    And method GET
    Then status 200

  @local @dev @unauthenticated
  Scenario: Offer  service endpoint with filters unauthenticated -Filter by single Partner invalid
  Validate when the unauthenticated user makes a request with a valid headers and invalid query params, it should return error in response.
  "Given unauthenticated user make the request
  "When all the headers are valid
  "And when we filter by invalid partner query param
  "Then we get error in response
    * configure headers = { Accept: 'application/json', X-Origin-Client: 'internal:test:integration', X-Correlation-Id: '45d7d3df-86bc-48b2-a769-644e13e39d66'}
    * def query = {region: 'ON',partner_id: 'test123'}
    And params query
    And method GET
    Then status 400
    And match response contains
   """
            {
            "code": "BAD_REQUEST",
            "message": "Failed to convert value of type 'java.lang.String' to required type 'java.util.List'; Invalid UUID string: test123"
            }
    """

  @local @dev @unauthenticated
  Scenario: Offer  service endpoint with filters unauthenticated -Filter by multiple Partners (Shell and Metro)
  Validate when the unauthenticated user makes a request with a valid headers and query params, it should return all offers in response.
  "Given unauthenticated user make the request
  "When all the headers are valid
  "And when we filter by multiple partners query param
  "Then we get all offers for that multiple partners in response
    * configure headers = { Accept: 'application/json', X-Origin-Client: 'internal:test:integration', X-Correlation-Id: '08093c78-decd-4e7b-9451-96894c7f09b4'}
    * def query = {region: 'ON',partner_id: 'b90e694d-3510-4cc2-84be-f9b6f61bd31b,4a755252-876b-478e-9440-42961525e307'}
    And params query
    And method GET
    Then status 200
    And match response.offers[*] contains deep {"partnerId":"b90e694d-3510-4cc2-84be-f9b6f61bd31b","partnerId":"4a755252-876b-478e-9440-42961525e307"}

  @local @dev @unauthenticated
  Scenario: Offer  service endpoint with filters unauthenticated -Filter by single Category (Grocery)
  Validate when the unauthenticated user makes a request with a valid headers and query params, it should return all offers in response.
  "Given unauthenticated user make the request
  "When all the headers are valid
  "And when we filter by single category query param
  "Then we get all offers for that specific category in response
    * configure headers = { Accept: 'application/json', X-Origin-Client: 'internal:test:integration', X-Correlation-Id: '02835565-7148-42f1-80db-d15c16eb1d75'}
    * def query = {region: 'ON',category_id: '22b666ae-97c2-4e57-a437-e977de6beef4'}
    And params query
    And method GET
    Then status 200
    And match each response.offers contains {"categoryId":"22b666ae-97c2-4e57-a437-e977de6beef4"}

  @local @dev @unauthenticated
  Scenario: Offer  service endpoint with filters unauthenticated -Filter by single category invalid
  Validate when the unauthenticated user makes a request with a valid headers and invalid query params, it should return error offers in response.
  "Given unauthenticated user make the request
  "When all the headers are valid
  "And when we filter by invalid category query param
  "Then we get error in response
    * configure headers = { Accept: 'application/json', X-Origin-Client: 'internal:test:integration', X-Correlation-Id: '8ee9713f-3b9b-421d-8ec7-7117568646d8'}
    * def query = {region: 'ON',category_id: '22b666ae-97c2-4e57-a437-e977de6beef41'}
    And params query
    And method GET
    Then status 400
    And match response contains
   """
            {
            "code": "BAD_REQUEST",
            "message": "Failed to convert value of type 'java.lang.String' to required type 'java.util.List'; UUID string too large"
            }
    """

  @local @dev @unauthenticated
  Scenario: Offer  service endpoint with filters unauthenticated -Filter by multiple Categories (Grocery and Fuel)
  Validate when the unauthenticated user makes a request with a valid headers and query params, it should return all offers in response.
  "Given unauthenticated user make the request
  "When all the headers are valid
  "And when we filter by multiple categories query param
  "Then we get all offers for that multiple categories in response
    * configure headers = { Accept: 'application/json', X-Origin-Client: 'internal:test:integration', X-Correlation-Id: '19bc2250-4cdd-4a74-a954-1f446b4cd44b'}
    * def query = {region: 'ON',category_id: '22b666ae-97c2-4e57-a437-e977de6beef4,0333d0e5-68be-4833-939f-3a20d0cc9806'}
    And params query
    And method GET
    Then status 200
    And match  response.offers[*] contains deep  {"categoryId":"22b666ae-97c2-4e57-a437-e977de6beef4","categoryId":"0333d0e5-68be-4833-939f-3a20d0cc9806"}

  @local @dev @unauthenticated
  Scenario: Offer  service endpoint with filters unauthenticated -Filter by Subcategory (Dairy)
  Validate when the unauthenticated user makes a request with a valid headers and query params, it should return all offers in response.
  "Given unauthenticated user make the request
  "When all the headers are valid
  "And when we filter by specific subcategory query param
  "Then we get all offers for that specific subcategory in response
    * configure headers = { Accept: 'application/json', X-Origin-Client: 'internal:test:integration', X-Correlation-Id: '8c9718bd-0ddf-4bd3-89f5-8cac799ad926'}
    * def query = {region: 'ON',category_id: 22b666ae-97c2-4e57-a437-e977de6beef4,subcategory_id: 'eab2662b-bfc5-4457-8cbf-33d9e10f9bb6'}
    And params query
    And method GET
    Then status 200
    And match each response.offers contains {"subCategoryId":"eab2662b-bfc5-4457-8cbf-33d9e10f9bb6"}

  @local @dev @unauthenticated
  Scenario: Offer  service endpoint with filters unauthenticated -Filter by Promotion Id (Flash Offer, More Miles)
  Validate when the unauthenticated user makes a request with a valid headers and query params, it should return all offers in response.
  "Given unauthenticated user make the request
  "When all the headers are valid
  "And when we filter by specific promoid query param
  "Then we get all offers for that promotion in response
    * configure headers = { Accept: 'application/json', X-Origin-Client: 'internal:test:integration', X-Correlation-Id: '7346defa-1ddf-43e7-9f0f-94a10cff10a0'}
    * def query = {region: 'ON', promotion_id: '8ccb04b6-e2fd-4eb6-8ca1-dc28652cca38'}
    And params query
    And method GET
    Then status 200
    And match each response.offers contains {"promotionId":"8ccb04b6-e2fd-4eb6-8ca1-dc28652cca38"}

  @local @dev @unauthenticated
  Scenario: Offer  service endpoint with filters unauthenticated -Filter by multiple Promotion Id (Flash Offer, More Miles)
  Validate when the unauthenticated user makes a request with a valid headers and invalid query params, it should return error in response.
  "Given unauthenticated user make the request
  "When all the headers are valid
  "And when we filter by multiple promo ids query param
  "Then we get error in response.
    * configure headers = { Accept: 'application/json', X-Origin-Client: 'internal:test:integration', X-Correlation-Id: 'd6db814c-da9b-414a-bab1-3f3337d8f3f6'}
    * def query = {region: 'ON', promotion_id: '8ccb04b6-e2fd-4eb6-8ca1-dc28652cca38,4db99143-1768-4f85-a44a-d6fa16011f7b'}
    And params query
    And method GET
    Then status 400
    And match response contains
     """
            {
            "code": "BAD_REQUEST",
            "message": "Failed to convert value of type 'java.lang.String' to required type 'java.util.UUID'; UUID string too large"
            }
    """

  @local @dev @unauthenticated
  Scenario: Offer  service endpoint with filters unauthenticated -Filter by Mass Offer = false
  Validate when the unauthenticated user makes a request with a valid headers and query params, it should return all offers in response.
  "Given unauthenticated user make the request
  "When all the headers are valid
  "And when we filter mass_offer= false query param
  "Then we get all offers in response with mass offer= true as it is unauthenticated flow.
    * configure headers = { Accept: 'application/json', X-Origin-Client: 'internal:test:integration', X-Correlation-Id: '4555f694-3a0f-41a3-bcfa-0217b1a942fc'}
    * def query = {region: 'ON', mass_offer: 'false'}
    And params query
    And method GET
    Then status 200
    And match each response.offers contains {"massOffer": true}

  @local @dev @unauthenticated
  Scenario: Offer  service endpoint with filters unauthenticated -Filter by Default Sort
  Validate when the unauthenticated user makes a request with a valid headers and query params, it should return all offers in response.
  "Given unauthenticated user make the request
  "When all the headers are valid
  "And when we filter by default sort query param
  "Then we get all offers as per default sort filter in response
    * configure headers = { Accept: 'application/json', X-Origin-Client: 'internal:test:integration', X-Correlation-Id: 'd4d38a4d-9295-4d59-92fe-890a166c9f8e'}
    * def query = {region: 'ON', sort: 'massOffer,partnerId,-displayPriority,endDate'}
    And params query
    And method GET
    Then status 200

  @local @dev @unauthenticated
  Scenario: Offer  service endpoint with filters unauthenticated -Filter by Ending soonest Sort
  Validate when the unauthenticated user makes a request with a valid headers and query params, it should return all offers in response.
  "Given unauthenticated user make the request
  "When all the headers are valid
  "And when we filter by ending soonest sort query param
  "Then we get all offers as per ending soonest sort filter in response
    * configure headers = { Accept: 'application/json', X-Origin-Client: 'internal:test:integration', X-Correlation-Id: 'b2a8d5e8-d8b6-4817-853c-d60e9e66f601'}
    * def query = {region: 'ON', sort: 'endDate,partnerId,-displayPriority'}
    And params query
    And method GET
    Then status 200

  @local @dev @unauthenticated
  Scenario: Offer  service endpoint with filters unauthenticated -Filter by region relevance Sort
  Validate when the unauthenticated user makes a request with a valid headers and query params, it should return all offers in response.
  "Given unauthenticated user make the request
  "When all the headers are valid
  "And when we filter by ending soonest sort query param
  "Then we get all offers as per ending soonest sort filter in response
    * configure headers = { Accept: 'application/json', X-Origin-Client: 'internal:test:integration', X-Correlation-Id: '89fb5699-365d-47a2-96be-c591cc5072bc'}
    * def query = {region: 'ON', sort: 'regionrelevance'}
    And params query
    And method GET
    Then status 200

  @local @dev @unauthenticated
  Scenario: Offer  service endpoint with filters unauthenticated -Filter by promotionId Sort
  Validate when the unauthenticated user makes a request with a valid headers and query params, it should return all offers in response.
  "Given unauthenticated user make the request
  "When all the headers are valid
  "And when we filter by promotionId sort query param
  "Then we get all offers as per promotionId sort filter in response
    * configure headers = { Accept: 'application/json', X-Origin-Client: 'internal:test:integration', X-Correlation-Id: '24ab1b8a-24de-4799-80a7-77e349cd65d4'}
    * def query = {region: 'ON', sort: 'promotionId,massOffer,partnerId,-displayPriority,endDate'}
    And params query
    And method GET
    Then status 200

  @local @dev @unauthenticated
  Scenario: Offer  service endpoint with filters for unauthenticated flow -Filter by program_type.
  Validate when the unauthenticated user makes a request with a valid headers and query params, it should return offers in response.
  "Given unauthenticated user make the request
  "When all the headers are valid
  "And when we filter by specific program_type query param
  "Then we get all offers for that program_type in response
    * configure headers = { Accept: 'application/json', X-Origin-Client: 'internal:test:integration', X-Correlation-Id: 'c9721cf0-3357-4d6d-88f7-c898d63309bd'}
    * def query = {region: 'ON', program_type: 'traditionalcore'}
    And params query
    And method GET
    Then status 200
    And match each response.offers contains {"programType":"traditionalcore"}

  @local @dev @unauthenticated
  Scenario: Offer  service endpoint with filters for unauthenticated flow -Filter by program_type cardlinked.
  Validate when the unauthenticated user makes a request with a valid headers and query params, it should return offers in response.
  "Given unauthenticated user make the request
  "When all the headers are valid
  "And when we filter by specific program_type query param
  "Then we get all offers for that program_type in response
    * configure headers = { Accept: 'application/json', X-Origin-Client: 'internal:test:integration', X-Correlation-Id: '22af0fd5-7a0f-4a5a-a392-c69dff5938c7'}
    * def query = {region: 'ON', program_type: 'cardlinked'}
    And params query
    And method GET
    Then status 200
    And match each response.offers contains {"programType":"cardlinked"}

  @local @dev @unauthenticated
  Scenario: Offer  service endpoint with filters for unauthenticated flow -Filter by program_type airmilesshops.
  Validate when the unauthenticated user makes a request with a valid headers and query params, it should return offers in response.
  "Given unauthenticated user make the request
  "When all the headers are valid
  "And when we filter by specific program_type query param
  "Then we get all offers for that program_type in response
    * configure headers = { Accept: 'application/json', X-Origin-Client: 'internal:test:integration', X-Correlation-Id: 'e5e06730-85ec-4110-9307-36b8445bec0a'}
    * def query = {region: 'ON', program_type: 'airmilesshops'}
    And params query
    And method GET
    Then status 200
    And match each response.offers contains {"programType":"airmilesshops"}

  @local @dev @unauthenticated
  Scenario: Offer  service endpoint with filters unauthenticated -Filter by extended_metadata = true
  Validate when the unauthenticated user makes a request with a valid headers and query params, it should return all offers in response.
  "Given unauthenticated user make the request
  "When all the headers are valid
  "And when we filter extended_metadata= true query param
  "Then we get all offers in response with extended_metadata= true as it is unauthenticated flow.
    * configure headers = { Accept: 'application/json', X-Origin-Client: 'internal:test:integration', X-Correlation-Id: '08093c78-decd-4e7b-9451-96894c7f09b4'}
    * def query = {region: 'ON', extended_metadata: 'true'}
    * def partners = {"id":"#uuid","count":"#number","label":"#string"}
    * def categories = {"id":"##uuid","label":"##string","count":"##number","subCategories":"#[]"}
    And params query
    And method GET
    Then status 200
    And match response.metadata contains
    """
            {
            "total":"#number",
            "partners":"#[] ##(partners)",
           }
    """
    And match response.metadata.categories[0].subCategories contains {"id":"#uuid","count":"#number","label":"#string"}


  @local @dev @unauthenticated
  Scenario: Offer  service endpoint with filters for unauthenticated flow -Filter by type buy.
  Validate when the authenticated user makes a request with a valid headers and query params, it should return offers in response.
  "Given authenticated user make the request
  "When all the headers are valid
  "And when we filter by specific type query param
  "Then we get all offers for that type in response
    * configure headers = { Accept: 'application/json', X-Origin-Client: 'internal:test:integration', X-Correlation-Id: 'fb1f366e-e986-45ba-8a01-c2824bb14242'}
    * def query = {region: 'ON', type: 'buy'}
    And params query
    And method GET
    Then status 200

  @local @dev @unauthenticated
  Scenario: Offer  service endpoint with filters for unauthenticated flow -Filter by type spend.
  Validate when the authenticated user makes a request with a valid headers and query params, it should return offers in response.
  "Given authenticated user make the request
  "When all the headers are valid
  "And when we filter by specific type query param
  "Then we get all offers for that type in response
    * configure headers = { Accept: 'application/json', X-Origin-Client: 'internal:test:integration', X-Correlation-Id: '2c4b15ad-0666-4acb-8ed8-1cab65a1771f'}
    * def query = {region: 'ON', type: 'spend'}
    And params query
    And method GET
    Then status 200

    #Deeplink scenarios unauthenticated
  @local @dev @unauthenticated
  Scenario:Offer endpoint by ID- HappyPath for a Deeplink with a Mass offer id and an unauthenticated user.
  Validate when the user makes a request with all valid headers and params,it should deeplink into a mass offer.
  "Given user make the request
  "When all headers are valid
  "And the params are also valid
  "Then user should be able to deeplink into mass offer
    * configure headers = {Accept:'application/json',X-Origin-Client:'internal:test:integration',X-Correlation-Id:'1af2540a-9b88-4f3c-a975-0317bdea1542'}
    * def query = {region: 'ON'}
    * def offer = massoffer_id
    * def mechanism = {"mechanismType":"#string","mechanismLabel":"##string","mechanismValue":"##string"}
    * def url1 = {"url":"#string"}
    * def tiers = {"awardValue":"##number","qualifierValue":"##number","awardLong":"#string","qualifierLong":"#string"}
    Given path offer
    And params query
    And method GET
    Then status 200
    And match response.offer contains
           """
           {
            "id":"#uuid",
           "partnerId":"#uuid",
           "partnerLabel":"#string",
           "partnerProfileURL":"#string",
           "partnerLogo":"#(url1)",
           "categoryId":"#uuid",
           "categoryLabel":"#string",
           "subCategoryId":"##uuid",
           "subCategoryLabel":"##string",
           "promotionId":"##uuid",
           "promotionLabel":"##string",
           "programType":"#string",
           "massOffer":"#boolean",
           "description":"##string",
           "awardShort":"#string",
           "qualifierShort":"#string",
           "displayDate":"#string",
           "startDate":"#string",
           "endDate":"#string",
           "displayPriority":"#number",
           "qualifierShort":"#string",
           "mechanisms":"#[] ##(mechanism)",
           "tiers":"#[] ##(tiers)",
           "image":"#(url1)",
           "legalText":"#string",
           "eventBasedOffer": "#boolean"
            }
"""
    And match response.warning == null


  @local @dev @unauthenticated
  Scenario:Offer endpoint by ID- UnHappyPath for a Deeplink with an invalid offer id.
  Validate when the user makes a request with all valid headers, params and invalid offer id,it should show the respective error.
  "Given user make the request
  "When all headers are valid
  "And the params are also valid
  "Then user should be able to see the "err-not-found" error
    * configure headers = {Accept:'application/json',X-Origin-Client:'internal:test:integration',X-Correlation-Id:'62229684-1ff9-43eb-87a0-f64f8cb340bd'}
    * def query = {region:'ON'}
    * def offer = invalid_id
    Given path offer
    And params query
    And method GET
    Then status 404
    And match response contains
      """{
          "code": "err-not-found",
          "message": "Offer Id does not exist, Offer Id not valid, Offer is not active"
          }"""

  @local @dev @unauthenticated
  Scenario:Offer  endpoint by ID- UnHappyPath for a Deeplink with a future offer id.
  Validate when the user makes a request with all valid headers, params and future offer id,it should show the respective error.
  "Given user make the request
  "When all headers are valid
  "And the params are also valid
  "Then user should be able to see the "err-not-live" error
    * configure headers = {Accept:'application/json',X-Origin-Client:'internal:test:integration',X-Correlation-Id:'628a857f-eccd-425d-a37b-a25b7dc44ee5'}
    * def query = {region:'ON'}
    * def offer = future_id
    Given path offer
    And params query
    And method GET
    Then status 404
    And match response contains
      """{
          "code": "err-not-live",
          "message": "Future Offer, Not Live Yet (current date < display start date)"
          }"""


  @local @dev @unauthenticated
  Scenario:Offer endpoint by ID- UnHappyPath for a Deeplink with an expired offer id.
  Validate when the user makes a request with all valid headers, params and an expired offerid,it should show the respective error.
  "Given user make the request
  "When all headers are valid
  "And the params are also valid
  "Then user should be able to see the "err-expired" error
    * configure headers = {Accept:'application/json',X-Origin-Client:'internal:test:integration',X-Correlation-Id:'a2faa2fd-2974-4af2-8bef-ea1e2b4841bb'}
    * def query = {region:'ON'}
    * def offer = expired_id
    Given path offer
    And params query
    And method GET
    Then status 404
    And match response contains
      """{
          "code": "err-expired",
          "message": "Offer is expired (current date > offer end date)"
          }"""


  @local @dev @unauthenticated
  Scenario:Offer endpoint by ID- HappyPath for a Deeplink with an out of region offer.
  Validate when the user makes a request with all valid headers, params and out of region offer it should show offer details with a warning.
  "Given user make the request
  "When all headers are valid
  "And the params are also valid
  "Then user should be able to see the offer details with the waring
    * configure headers = {Accept:'application/json',X-Origin-Client:'internal:test:integration',X-Correlation-Id:'7a1a8952-91a4-48c7-8c27-f9cd2269f574'}
    * def query = {region:'ON'}
    * def offer = outofregion_id
    * def mechanism = {"mechanismType":"#string","mechanismLabel":"##string","mechanismValue":"##string"}
    * def url1 = {"url":"#string"}
    * def tiers = {"awardValue":"##number","qualifierValue":"##number","awardLong":"#string","qualifierLong":"#string"}
    Given path offer
    And params query
    And method GET
    Then status 200
    And match response.offer contains
           """{
            "id":"#uuid",
           "partnerId":"#uuid",
           "partnerLabel":"#string",
           "partnerProfileURL":"#string",
           "partnerLogo":"#(url1)",
           "categoryId":"#uuid",
           "categoryLabel":"#string",
           "subCategoryId":"##uuid",
           "subCategoryLabel":"##string",
           "promotionId":"##uuid",
           "promotionLabel":"##string",
           "programType":"#string",
           "massOffer":"#boolean",
           "description":"##string",
           "awardShort":"#string",
           "qualifierShort":"#string",
           "displayDate":"#string",
           "startDate":"#string",
           "endDate":"#string",
           "displayPriority":"#number",
           "qualifierShort":"#string",
           "mechanisms":"#[] ##(mechanism)",
           "tiers":"#[] ##(tiers)",
           "image":"#(url1)",
           "legalText":"#string",
           "eventBasedOffer":"#boolean"
            }"""
    And match response.warning contains
      """{
          "errorCode": "err-region",
          "message": "Offer not available in user's current region"
          }"""

  @local @dev @unauthenticated
  Scenario:Offer endpoint by ID- HappyPath for a Deeplink with an targeted offer with an unauthenticated user.
  Validate when the user makes a request with all valid headers, params and targeted offer it should show offer details with a warning.
  "Given user make the request
  "When all headers are valid
  "And the params are also valid
  "Then user should be able to see the offer details with the waring
    * configure headers = {Accept:'application/json',X-Origin-Client:'internal:test:integration',X-Correlation-Id:'c76ba384-85e1-4a13-9af5-06cb773b0529'}
    * def query = {region:'ON'}
    * def offer = targeted_id
    * def mechanism = {"mechanismType":"#string","mechanismLabel":"##string","mechanismValue":"##string"}
    * def url1 = {"url":"#string"}
    * def tiers = {"awardValue":"##number","qualifierValue":"##number","awardLong":"#string","qualifierLong":"#string"}
    Given path offer
    And params query
    And method GET
    Then status 200
    And match response.offer contains
           """{
            "id":"#uuid",
           "partnerId":"#uuid",
           "partnerLabel":"#string",
           "partnerProfileURL":"#string",
           "partnerLogo":"#(url1)",
           "categoryId":"#uuid",
           "categoryLabel":"#string",
           "subCategoryId":"##uuid",
           "subCategoryLabel":"##string",
           "promotionId":"##uuid",
           "promotionLabel":"##string",
           "programType":"#string",
           "massOffer":"#boolean",
           "endDate":"#string",
           "description":"##string",
           "awardShort":"#string",
           "qualifierShort":"#string",
           "displayDate":"#string",
           "startDate":"#string",
           "endDate":"#string",
           "displayPriority":"#number",
           "qualifierShort":"#string",
           "mechanisms":"#[] ##(mechanism)",
           "tiers":"#[] ##(tiers)",
           "image":"#(url1)",
           "legalText":"#string",
           "eventBasedOffer":"#boolean"
            }"""
    And match response.warning contains
      """{
          "errorCode": "err-login-required",
          "message": "You may be eligible for this offer = This is a Targeted Offer and you are not authenticated. Sign in to check if you are eligible"
          }"""



    #Authenticated Flow Testcases

  @local @dev @authenticated
  Scenario: Offer  service endpoint -Happy Path for an authenticated user.
  Validate when the authenticated user makes a request with a valid headers and query params, it should return all offers in the response.
  "Given authenticated user makes the request
  "When all the headers are valid
  "And all the query params are valid
  "Then we get all offers in the response
    * def headers = { Accept: 'application/json',X-Origin-Client: 'internal:test:integration',X-Correlation-Id: 'f2772222-7b43-40ae-b7b2-aee63b694f4e',Collector-Number:84151103400}
    * def query = {region: 'ON'}
    * def mechanism = {"mechanismType":"#string","mechanismLabel":"##string","mechanismValue":"##string"}
    * def url1 = {"url":"#string"}
    * def tiers = {"awardValue":"##number","qualifierValue":"##number","awardLong":"#string", "qualifierLong":"#string"}
    * def state = {"name":"#string","value":"#string","updatedAt":"#string"}
    Given url apiUrl
    And headers headers
    And params query
    And method GET
    Then status 200
    And match response.metadata contains
             """{
             "total":"#number"
                 }  """
    And match response.offers[*] contains
          """{
             "id":"#uuid",
             "partnerId":"#uuid",
             "partnerLabel":"#string",
             "partnerProfileURL":"#string",
             "partnerLogo":"##(url1)",
             "categoryId":"#uuid",
             "categoryLabel":"#string",
             "subCategoryId":"##uuid",
             "subCategoryLabel":"##string",
             "promotionId":"##uuid",
             "promotionLabel":"##string",
             "programType":"#string",
             "massOffer":"#boolean",
             "description":"##string",
             "type":"##string",
             "awardShort":"#string",
             "qualifierShort":"#string",
             "displayDate":"#string",
             "startDate":"#string",
             "endDate":"#string",
             "displayPriority":"#number",
             "qualifierShort":"#string",
             "mechanisms":"#[] ##(mechanism)",
             "states":"##[]##(state)",
             "tiers":"#[] ##(tiers)",
             "image":"##(url1)",
             "legalText":"#string",
             "eventBasedOffer":"#boolean"
            }"""


  @local1 @dev @authenticated
  Scenario: Offer service endpoint -Happy Path for an authenticated user with french header.
  Validate when the authenticated user makes a request with a valid headers and query params, it should return success response.
  "Given authenticated user makes the  request
  "When all the headers are valid
  "And all the query params are valid
  "Then we get all offers response in French
    * def headers = { Accept: 'application/json',X-Origin-Client: 'internal:test:integration',Accept-Language:'fr-CA',X-Correlation-Id: '99b07fd7-0cf3-40f7-b464-8f5684fbd9d2',Collector-Number:84151103400}
    * def query = {region: 'ON'}
    Given url apiUrl
    And headers headers
    And params query
    And method GET
    Then status 200

  @local @dev @authenticated
  Scenario: Offer service endpoint -Happy Path for english language header.
  Validate when the authenticated user makes a request with a valid headers and query params, it should return all offers response for specific fields in English.
  "Given unauthenticated user make the request
  "When all the headers are valid
  "And all the query params are valid
  "Then we get all offers response in english
    * def headers = { Accept: 'application/json',Accept-Language:'en-US', X-Origin-Client: 'internal:test:integration',X-Correlation-Id: '96afe8fd-e8bf-4fa0-af1d-dd69f0336843',Collector-Number:84151103400}
    * def query = {region: 'ON'}
    Given url apiUrl
    And headers headers
    And params query
    And method GET
    Then status 200

  @local1 @dev @authenticated
  Scenario: Offer service endpoint -UnHappy Path for an authenticated user for when limit is set to 97.
  Validate when the unauthenticated user makes a request with a valid headers and invalid query params, it should return error response.
  "Given authenticated user makes the  request
  "When all the headers are valid
  "And limit parameter is invalid
  "Then we get error in response.
    * def headers = { Accept: 'application/json', X-Origin-Client: 'internal:test:integration',X-Correlation-Id: 'f5962aa9-7a6f-4cfc-bff5-4b9b809c05fc',Collector-Number:84151103400}
    * def query = {region: 'ON',limit: '97',offset: '1'}
    Given url apiUrl
    And headers headers
    And params query
    And method GET
    Then status 400
    And match response contains
     """
            {
         "code":"BAD_REQUEST",
         "message":"getOffers.limit: must be less than or equal to 96"
            }
    """

  @local1 @dev @authenticated
  Scenario: Offer service endpoint -UnHappy Path for an authenticated user for when region is not passed.
  Validate when the authenticated user makes a request with a valid headers and invalid query params, it should return error response.
  "Given authenticated user makes the  request
  "When all the headers are valid
  "And region parameter is not passed
  "Then we get error in response.
    * def headers = { Accept: 'application/json', X-Origin-Client: 'internal:test:integration',X-Correlation-Id: '22714e40-2c64-4de1-aa6d-5129072fac2a',Collector-Number:84151103400}
    * def query = {limit: '20', offset: '1'}
    Given url apiUrl
    And headers headers
    And params query
    And method GET
    Then status 400
    And match response contains
     """
            {
      "code": "BAD_REQUEST",
      "message": "Required request parameter 'region' for method parameter type RegionEnum is not present"
            }
    """


  @local1 @dev @unauthenticated
  Scenario: Offer service endpoint -UnHappy Path for an authenticated user for when region is passed as ON!.
  Validate when the authenticated user makes a request with a valid headers and invalid query params, it should return error response.
  "Given authenticated user makes the  request
  "When all the headers are valid
  "And region parameter is passed as ON!
  "Then we get error in response.
    * def headers = { Accept: 'application/json', X-Origin-Client: 'internal:test:integration',X-Correlation-Id: '3440b499-fe58-472e-9065-84acc542aaf4',Collector-Number:84151103400}
    * def query = {region: 'ON!',limit: '20',offset: '1'}
    Given url apiUrl
    And headers headers
    And params query
    And method GET
    Then status 400
    And match response contains
     """
            {
     "code": "BAD_REQUEST",
     "message": "Failed to convert value of type 'java.lang.String' to required type 'com.loyalty.offerservice.enums.RegionEnum'; Failed to convert from type [java.lang.String] to type [@org.springframework.web.bind.annotation.RequestParam com.loyalty.offerservice.enums.RegionEnum] for value [ON!]"
            }
    """


  @local1 @dev @authenticated
  Scenario: Offer service endpoint -UnHappy Path for an authenticated user for when x-origin client is not passed.
  Validate when the authenticated user makes a request with a valid headers and invalid query params, it should return error response.
  "Given authenticated user makes the  request
  "When x -origin client header is not passed
  "And valid query params are passed
  "Then we get error in response.
    * def headers = { Accept: 'application/json',X-Correlation-Id: 'b2143910-4aff-481b-bcc2-58166d9dec1f',Collector-Number:84151103400}
    * def query = {region: 'ON',limit: '20',offset: '1'}
    Given url apiUrl
    And headers headers
    And params query
    And method GET
    Then status 400
    And match response contains
     """
            {
      "code": "BAD_REQUEST",
      "message": "Required request header 'X-Origin-Client' for method parameter type String is not present"
            }
    """


  @local @dev @authenticated
  Scenario: Offer  service endpoint with filters authenticated -Filter by single Partner (Shell)
  Validate when the authenticated user makes a request with a valid headers and query params, it should return all offers in response.
  "Given authenticated user make the request
  "When all the headers are valid
  "And when we filter by single partner query param
  "Then we get all offers for that specific partner in response
    * def headers = {Accept: 'application/json',X-Origin-Client: 'internal:test:integration',X-Correlation-Id: '39414895-3bf8-4e30-acf8-88b51eebe184',Collector-Number:84151103400}
    * def query = {region: 'ON',partner_id: 'b90e694d-3510-4cc2-84be-f9b6f61bd31b'}
    Given url apiUrl
    And headers headers
    And params query
    And method GET
    Then status 200
    And match each response.offers contains {"partnerId":"b90e694d-3510-4cc2-84be-f9b6f61bd31b"}

  @local @dev @authenticated
  Scenario: Offer  service endpoint with filters authenticated -Filter by exclude Partner id
  Validate when the authenticated user makes a request with a valid headers and query params, it should return all offers in response.
  "Given unauthenticated user make the request
  "When all the headers are valid
  "And when we filter by exclude partner id query param
  "Then we get all offers excluding that partner in response
    * def headers = {Accept: 'application/json',X-Origin-Client: 'internal:test:integration',X-Correlation-Id: '39414895-3bf8-4e30-acf8-88b51eebe185',Collector-Number:84151103400}
    * def query = {region: 'ON',exclude_partner_id: 'b90e694d-3510-4cc2-84be-f9b6f61bd31b'}
    Given url apiUrl
    And headers headers
    And params query
    And method GET
    Then status 200

  @local @dev @authenticated
  Scenario: Offer  service endpoint with filters authenticated -Filter by single Partner invalid
  Validate when the authenticated user makes a request with a valid headers and query params, it should return all offers in response.
  "Given authenticated user make the request
  "When all the headers are valid
  "And when we filter by single partner query param invalid
  "Then we get error in response
    * def headers = {Accept: 'application/json',X-Origin-Client: 'internal:test:integration',X-Correlation-Id: '7438d687-7376-44ab-a916-57476442b528',Collector-Number:84151103400}
    * def query = {region: 'ON',partner_id: 'test123'}
    Given url apiUrl
    And headers headers
    And params query
    And method GET
    Then status 400
    And match response contains
   """
            {
            "code": "BAD_REQUEST",
            "message": "Failed to convert value of type 'java.lang.String' to required type 'java.util.List'; Invalid UUID string: test123"
            }
    """

  @local @dev @authenticated
  Scenario: Offer  service endpoint with filters authenticated -Filter by multiple Partners (Shell and Metro)
  Validate when the authenticated user makes a request with a valid headers and query params, it should return all offers in response.
  "Given authenticated user make the request
  "When all the headers are valid
  "And when we filter by multiple partners query param
  "Then we get all offers for that multiple partners in response
    * def headers = {Accept: 'application/json',X-Origin-Client: 'internal:test:integration',X-Correlation-Id: '1ec0f6cf-050c-46e1-8945-fd52bf533a6f',Collector-Number:84151103400}
    * def query = {region: 'ON',partner_id: 'b90e694d-3510-4cc2-84be-f9b6f61bd31b,4a755252-876b-478e-9440-42961525e307'}
    Given url apiUrl
    And headers headers
    And params query
    And method GET
    Then status 200
    And match response.offers[*] contains deep {"partnerId":"b90e694d-3510-4cc2-84be-f9b6f61bd31b","partnerId":"4a755252-876b-478e-9440-42961525e307"}


  @local @dev @authenticated
  Scenario: Offer  service endpoint with filters authenticated -Filter by single Category (Grocery)
  Validate when the authenticated user makes a request with a valid headers and query params, it should return all offers in response.
  "Given authenticated user make the request
  "When all the headers are valid
  "And when we filter by category query param
  "Then we get all offers for that specific category in response
    * def headers = {Accept: 'application/json',X-Origin-Client: 'internal:test:integration',X-Correlation-Id: '89dae018-e62f-402f-bd9a-741b318b313f',Collector-Number:84151103400}
    * def query = {region: 'ON',category_id: '22b666ae-97c2-4e57-a437-e977de6beef4'}
    Given url apiUrl
    And headers headers
    And params query
    And method GET
    Then status 200
    And match each response.offers contains {"categoryId":"22b666ae-97c2-4e57-a437-e977de6beef4"}

  @local @dev @authenticated
  Scenario: Offer  service endpoint with filters authenticated -Filter by single Category invalid.
  Validate when the authenticated user makes a request with a valid headers and query params, it should get error in response.
  "Given authenticated user make the request
  "When all the headers are valid
  "And when we filter by category query param invalid
  "Then we get error in response
    * def headers = {Accept: 'application/json',X-Origin-Client: 'internal:test:integration',X-Correlation-Id: 'f024ad33-4383-429c-9937-427d9f5053af',Collector-Number:84151103400}
    * def query = {region: 'ON',category_id: '22b666ae-97c2-4e57-a437-e977de6beef41'}
    Given url apiUrl
    And headers headers
    And params query
    And method GET
    Then status 400
    And match response contains
   """
            {
            "code": "BAD_REQUEST",
            "message": "Failed to convert value of type 'java.lang.String' to required type 'java.util.List'; UUID string too large"
            }
    """

  @local @dev @authenticated
  Scenario: Offer  service endpoint with filters authenticated -Filter by Categories (Grocery and Fuel)
  Validate when the authenticated user makes a request with a valid headers and query params, it should return all offers in response.
  "Given authenticated user make the request
  "When all the headers are valid
  "And when we filter by categories query param
  "Then we get all offers for that specific categories in response
    * def headers = {Accept: 'application/json',X-Origin-Client: 'internal:test:integration',X-Correlation-Id: 'ea8cce9d-781e-45e5-9fa0-ace55d5bdd30',Collector-Number:84151103400}
    * def query = {region: 'ON',category_id: '22b666ae-97c2-4e57-a437-e977de6beef4,0333d0e5-68be-4833-939f-3a20d0cc9806'}
    And headers headers
    And params query
    And method GET
    Then status 200
    And match response.offers[*] contains deep {"categoryId":"22b666ae-97c2-4e57-a437-e977de6beef4", "categoryId":"0333d0e5-68be-4833-939f-3a20d0cc9806"}

  @local @dev @authenticated
  Scenario: Offer  service endpoint with filters authenticated -Filter by Subcategory (Dairy)
  Validate when the authenticated user makes a request with a valid headers and query params, it should return all offers in response.
  "Given unauthenticated user make the request
  "When all the headers are valid
  "And when we filter by specific subcategory query param
  "Then we get all offers for that specific subcategory in response
    * def headers = {Accept: 'application/json',X-Origin-Client: 'internal:test:integration',X-Correlation-Id: '076c7f90-8b5c-499c-ad8c-693e3aab6000',Collector-Number:84151103400}
    * def query = {region: 'ON',category_id: 22b666ae-97c2-4e57-a437-e977de6beef4,subcategory_id: 'eab2662b-bfc5-4457-8cbf-33d9e10f9bb6'}
    Given url apiUrl
    And headers headers
    And params query
    And method GET
    Then status 200
    And match each response.offers contains {"subCategoryId":"eab2662b-bfc5-4457-8cbf-33d9e10f9bb6"}

  @local @dev @authenticated
  Scenario: Offer  service endpoint with filters authenticated -Filter by Promotion Id (Flash Offer, More Miles)
  Validate when the authenticated user makes a request with a valid headers and query params, it should return all offers in response.
  "Given authenticated user make the request
  "When all the headers are valid
  "And when we filter by specific promoid query param
  "Then we get all offers for that promotion in response
    * def headers = {Accept: 'application/json',X-Origin-Client: 'internal:test:integration',X-Correlation-Id: '40840e0b-15ec-48fb-8074-7bd7f7ae7441',Collector-Number:84151103400}
    * def query = {region: 'ON', promotion_id: '8ccb04b6-e2fd-4eb6-8ca1-dc28652cca38'}
    Given url apiUrl
    And headers headers
    And params query
    And method GET
    Then status 200
    And match each response.offers contains {"promotionId":"8ccb04b6-e2fd-4eb6-8ca1-dc28652cca38"}

  @local @dev @authenticated
  Scenario: Offer  service endpoint with filters authenticated -Filter by multiple Promotion Id
  Validate when the authenticated user makes a request with a valid headers and query params, it should return all offers in response.
  "Given authenticated user make the request
  "When all the headers are valid
  "And when we filter by multiple promoid query param
  "Then we get error in response
    * def headers = {Accept: 'application/json',X-Origin-Client: 'internal:test:integration',X-Correlation-Id: 'e94d8228-3af6-41f7-8a28-4e051091a65c',Collector-Number:84151103400}
    * def query = {region: 'ON', promotion_id: '8ccb04b6-e2fd-4eb6-8ca1-dc28652cca38,4db99143-1768-4f85-a44a-d6fa16011f7b'}
    Given url apiUrl
    And headers headers
    And params query
    And method GET
    Then status 400
    And match response contains
     """
            {
            "code": "BAD_REQUEST",
            "message": "Failed to convert value of type 'java.lang.String' to required type 'java.util.UUID'; UUID string too large"
            }
    """

  @local @dev @authenticated
  Scenario: Offer  service endpoint with filters authenticated -Filter by Mass Offer = false
  Validate when the authenticated user makes a request with a valid headers and query params, it should return all offers in response.
  "Given authenticated user make the request
  "When all the headers are valid
  "And when we filter mass_offer= false query param
  "Then we get all offers in response with mass offer= false as it is authenticated flow.
    * def headers = {Accept: 'application/json',X-Origin-Client: 'internal:test:integration',X-Correlation-Id: 'd37ea613-1454-4dfd-ae76-35725a63b5b8',Collector-Number:84151103400}
    * def query = {region: 'ON', mass_offer: 'false'}
    Given url apiUrl
    And headers headers
    And params query
    And method GET
    Then status 200
    And match each response.offers contains {"massOffer": false}

  @local @dev @authenticated
  Scenario: Offer  service endpoint with filters authenticated -Filter by Default Sort
  Validate when the authenticated user makes a request with a valid headers and query params, it should return all offers in response.
  "Given authenticated user make the request
  "When all the headers are valid
  "And when we filter by default sort query param
  "Then we get all offers as per default sort filter in response
    * def headers = {Accept: 'application/json',X-Origin-Client: 'internal:test:integration',X-Correlation-Id: 'be4dcc27-6e43-4d88-a5c5-8ccb17cf0fcb',Collector-Number:84151103400}
    * def query = {region: 'ON', sort: 'massOffer,partnerId,-displayPriority,endDate'}
    Given url apiUrl
    And headers headers
    And params query
    And method GET
    Then status 200

  @local @dev @authenticated
  Scenario: Offer  service endpoint with filters authenticated -Filter by Ending soonest Sort
  Validate when the authenticated user makes a request with a valid headers and query params, it should return all offers in response.
  "Given unauthenticated user make the request
  "When all the headers are valid
  "And when we filter by ending soonest sort query param
  "Then we get all offers as per ending soonest sort filter in response
    * def headers = {Accept: 'application/json',X-Origin-Client: 'internal:test:integration',X-Correlation-Id: '12b86af0-30ce-47e5-8370-dd7e8c8488d1',Collector-Number:84151103400}
    * def query = {region: 'ON', sort: 'endDate,partnerId,-displayPriority'}
    Given url apiUrl
    And headers headers
    And params query
    And method GET
    Then status 200

  @local @dev @authenticated
  Scenario: Offer  service endpoint with filters authenticated -Filter by region relevance Sort
  Validate when the authenticated user makes a request with a valid headers and query params, it should return all offers in response.
  "Given unauthenticated user make the request
  "When all the headers are valid
  "And when we filter by region relevance sort query param
  "Then we get all offers as per region relevance sort filter in response
    * def headers = {Accept: 'application/json',X-Origin-Client: 'internal:test:integration',X-Correlation-Id: 'df1c1121-7bbd-4893-b2a1-f06d4a193231',Collector-Number:84151103400}
    * def query = {region: 'ON', sort: 'regionrelevance'}
    Given url apiUrl
    And headers headers
    And params query
    And method GET
    Then status 200

  @local @dev @authenticated
  Scenario: Offer  service endpoint with filters authenticated -Filter by promotionId Sort
  Validate when the authenticated user makes a request with a valid headers and query params, it should return all offers in response.
  "Given unauthenticated user make the request
  "When all the headers are valid
  "And when we filter by region promotionId sort query param
  "Then we get all offers as per promoid sort filter in response
    * def headers = {Accept: 'application/json',X-Origin-Client: 'internal:test:integration',X-Correlation-Id: 'f2f804d7-d631-476d-8fcf-b3e24ab64f2c',Collector-Number:84151103400}
    * def query = {region: 'ON', sort: 'promotionId,massOffer,partnerId,-displayPriority,endDate'}
    Given url apiUrl
    And headers headers
    And params query
    And method GET
    Then status 200

  @local @dev @authenticated
  Scenario: Offer  service endpoint with filters authenticated -Filter by collector relevance Sort
  Validate when the authenticated user makes a request with a valid headers and query params, it should return all offers in response.
  "Given unauthenticated user make the request
  "When all the headers are valid
  "And when we filter by collector relevance sort query param
  "Then we get all offers as per collector relevance sort filter in response
    * def headers = {Accept: 'application/json',X-Origin-Client: 'internal:test:integration',X-Correlation-Id: '06c3c7b6-72d5-4e73-992d-00f148f0fd88',Collector-Number:84151103400}
    * def query = {region: 'ON', sort: 'collectorrelevance'}
    Given url apiUrl
    And headers headers
    And params query
    And method GET
    Then status 200

  @local @dev @authenticated
  Scenario: Offer  service endpoint with filters for authenticated flow -Filter by program_type.
  Validate when the authenticated user makes a request with a valid headers and query params, it should return offers in response.
  "Given authenticated user make the request
  "When all the headers are valid
  "And when we filter by specific program_type query param
  "Then we get all offers for that program_type in response
    * def headers = {Accept: 'application/json',X-Origin-Client: 'internal:test:integration',X-Correlation-Id: 'f5a989d4-b5d1-4236-b891-9043c11a2769',Collector-Number:84151103400}
    * def query = {region: 'ON', program_type: 'traditionalcore'}
    Given url apiUrl
    And headers headers
    And params query
    And method GET
    Then status 200
    And match each response.offers contains {"programType":"traditionalcore"}

  @local @dev @authenticated
  Scenario: Offer  service endpoint with filters for authenticated flow -Filter by program_type.
  Validate when the authenticated user makes a request with a valid headers and query params, it should return offers in response.
  "Given authenticated user make the request
  "When all the headers are valid
  "And when we filter by specific program_type query param
  "Then we get all offers for that program_type in response
    * def headers = {Accept: 'application/json',X-Origin-Client: 'internal:test:integration',X-Correlation-Id: 'e8c0e0de-e3ea-48c5-907d-ff96b8e0a967',Collector-Number:84151103400}
    * def query = {region: 'ON', program_type: 'cardlinked'}
    Given url apiUrl
    And headers headers
    And params query
    And method GET
    Then status 200
    And match each response.offers contains {"programType":"cardlinked"}

  @local @dev @authenticated
  Scenario: Offer  service endpoint with filters for authenticated flow -Filter by program_type.
  Validate when the authenticated user makes a request with a valid headers and query params, it should return offers in response.
  "Given authenticated user make the request
  "When all the headers are valid
  "And when we filter by specific program_type query param
  "Then we get all offers for that program_type in response
    * def headers = {Accept: 'application/json',X-Origin-Client: 'internal:test:integration',X-Correlation-Id: 'b42118f0-0e33-4d48-9af4-c0dfba38bf26',Collector-Number:84151103400}
    * def query = {region: 'ON', program_type: 'airmilesshops'}
    Given url apiUrl
    And headers headers
    And params query
    And method GET
    Then status 200
    And match each response.offers contains {"programType":"airmilesshops"}

  @local @dev @authenticated
  Scenario: Offer  service endpoint with filters for authenticated flow -Filter by type buy.
  Validate when the authenticated user makes a request with a valid headers and query params, it should return offers in response.
  "Given authenticated user make the request
  "When all the headers are valid
  "And when we filter by specific type query param
  "Then we get all offers for that type in response
    * def headers = {Accept: 'application/json',X-Origin-Client: 'internal:test:integration',X-Correlation-Id: 'f17d235d-e202-4c9d-aa55-98eeb9b1596d',Collector-Number:84151103400}
    * def query = {region: 'ON', type: 'buy'}
    Given url apiUrl
    And headers headers
    And params query
    And method GET
    Then status 200


  @local @dev @authenticated
  Scenario: Offer  service endpoint with filters for authenticated flow -Filter by type spend.
  Validate when the authenticated user makes a request with a valid headers and query params, it should return offers in response.
  "Given authenticated user make the request
  "When all the headers are valid
  "And when we filter by specific type query param
  "Then we get all offers for that type in response
    * def headers = {Accept: 'application/json',X-Origin-Client: 'internal:test:integration',X-Correlation-Id: 'fc9d4b28-d1b0-4835-ba1b-86f723361745',Collector-Number:84151103400}
    * def query = {region: 'ON', type: 'spend'}
    Given url apiUrl
    And headers headers
    And params query
    And method GET
    Then status 200

    # Deeplink Scenarios authenticated
  @local @dev @authenticated
  Scenario:Offer endpoint by ID- HappyPath for a Deeplink with a Targeted offer id and an authenticated user.
  Validate when the user makes a request with all valid headers and params,it should deeplink into a targeted offer.
  "Given user make the request
  "When all headers are valid
  "And the params are also valid
  "Then user should be able to deeplink into targeted offer
    * def offer = auth_targeted_id
    * def headers = {Accept: 'application/json',X-Origin-Client: 'internal:test:integration',X-Correlation-Id: 'ca1e9e3e-3fa1-4ec0-af6e-9605b41b8a5a',Collector-Number:84151103400}
    * def query = {region: 'ON'}
    * def mechanism = {"mechanismType":"#string","mechanismLabel":"##string","mechanismValue":"##string"}
    * def url1 = {"url":"#string"}
    * def tiers = {"awardValue":"##number","qualifierValue":"##number","awardLong":"#string","qualifierLong":"#string"}
    * def state = {"name":"#string","value":"#string","updatedAt":"#string"}
    Given url apiUrl
    Given path offer
    And headers headers
    And params query
    And method GET
    Then status 200
    And match response.offer contains
           """{
            "id":"#uuid",
           "partnerId":"#uuid",
           "partnerLabel":"#string",
           "partnerProfileURL":"#string",
           "partnerLogo":"#(url1)",
           "categoryId":"#uuid",
           "categoryLabel":"#string",
           "subCategoryId":"##uuid",
           "subCategoryLabel":"##string",
           "promotionId":"##uuid",
           "promotionLabel":"##string",
           "programType":"#string",
           "massOffer":"#boolean",
           "description":"##string",
           "awardShort":"#string",
           "qualifierShort":"#string",
           "displayDate":"#string",
           "startDate":"#string",
           "endDate":"#string",
           "displayPriority":"#number",
           "qualifierShort":"#string",
           "mechanisms":"#[] ##(mechanism)",
           "states":"##[] ##(state)",
           "tiers":"#[] ##(tiers)",
           "image":"#(url1)",
           "legalText":"#string",
           "eventBasedOffer":"#boolean"
            }"""
    And match response.warning == null

  @local @dev @authenticated
  Scenario:Offer endpoint by ID- HappyPath for a Deeplink with a Mass offer id and an authenticated user.
  Validate when the user makes a request with all valid headers and params,it should deeplink into a targeted offer.
  "Given user make the request
  "When all headers are valid
  "And the params are also valid
  "Then user should be able to deeplink into mass offer id
    * def offer = massoffer_id
    * def headers = {Accept: 'application/json',X-Origin-Client: 'internal:test:integration',X-Correlation-Id: '08093c78-decd-4e7b-9451-96894c7f09b4',Collector-Number:84151103400}
    * def query = {region: 'ON'}
    * def mechanism = {"mechanismType":"#string","mechanismLabel":"##string","mechanismValue":"##string"}
    * def url1 = {"url":"#string"}
    * def tiers = {"awardValue":"##number","qualifierValue":"##number","awardLong":"#string","qualifierLong":"#string"}
    Given url apiUrl
    Given path offer
    And headers headers
    And params query
    And method GET
    Then status 200
    And match response.offer contains
           """{
            "id":"#uuid",
           "partnerId":"#uuid",
           "partnerLabel":"#string",
           "partnerProfileURL":"#string",
           "partnerLogo":"#(url1)",
           "categoryId":"#uuid",
           "categoryLabel":"#string",
           "subCategoryId":"##uuid",
           "subCategoryLabel":"##string",
           "promotionId":"##uuid",
           "promotionLabel":"##string",
           "programType":"#string",
           "massOffer":"#boolean",
           "endDate":"#string",
           "description":"##string",
           "awardShort":"#string",
           "qualifierShort":"#string",
           "displayDate":"#string",
           "startDate":"#string",
           "endDate":"#string",
           "displayPriority":"#number",
           "qualifierShort":"#string",
           "mechanisms":"#[] ##(mechanism)",
           "tiers":"#[] ##(tiers)",
           "image":"#(url1)",
           "legalText":"#string",
           "eventBasedOffer":"#boolean"
            }"""
    And match response.warning == null

  @local @dev @authenticated
  Scenario:Offer endpoint by ID- UnHappyPath for a Deeplink with an invalid offer id.
  Validate when the user makes a request with all valid headers, params and invalid offer id,it should show the respective error.
  "Given user make the request
  "When all headers are valid
  "And the params are also valid
  "Then user should be able to see the "err-not-found" error
    * def offer = invalid_id
    * def headers = {Accept: 'application/json',X-Origin-Client: 'internal:test:integration',X-Correlation-Id: 'b52e28f1-9859-439d-b563-5a35660f3265',Collector-Number:84151103400}
    * def query = {region:'ON'}
    Given url apiUrl
    Given path offer
    And headers headers
    And params query
    And method GET
    Then status 404
    And match response contains
      """{
          "code": "err-not-found",
          "message": "Offer Id does not exist OR Offer Id not valid OR Offer inactive (active=false)"
          }"""

  @local @dev @authenticated
  Scenario:Offer  endpoint by ID- UnHappyPath for a Deeplink with a future offer id.
  Validate when the user makes a request with all valid headers, params and future offer id,it should show the respective error.
  "Given user make the request
  "When all headers are valid
  "And the params are also valid
  "Then user should be able to see the "err-not-live" error
    * def offer = auth_future_id
    * def headers = {Accept: 'application/json',X-Origin-Client: 'internal:test:integration',X-Correlation-Id: '1c7dfaac-5f8c-49cd-991f-9d177b16807c',Collector-Number:84151103400}
    * def query = {region:'ON'}
    Given url apiUrl
    Given path offer
    And headers headers
    And params query
    And method GET
    Then status 404
    And match response contains
        """{
          "code": "err-not-live",
          "message": "Future Offer, Not Live Yet (current date < display start date)"
          }"""

  @local @dev @authenticated
  Scenario:Offer  endpoint by ID- UnHappyPath for a Deeplink with a expired offer id.
  Validate when the user makes a request with all valid headers, params and expired offer id,it should show the respective error.
  "Given user make the request
  "When all headers are valid
  "And the params are also valid
  "Then user should be able to see the "err-expired" error
    * def offer = expired_id
    * def headers = {Accept: 'application/json',X-Origin-Client: 'internal:test:integration',X-Correlation-Id: '1c7dfaac-5f8c-49cd-991f-9d177b16807c',Collector-Number:84151103400}
    * def query = {region:'ON'}
    Given url apiUrl
    Given path offer
    And headers headers
    And params query
    And method GET
    Then status 404
    And match response contains
       """{
          "code": "err-expired",
          "message": "Offer is expired (current date > offer end date)"
          }"""

  @local @dev @authenticated
  Scenario:Offer  endpoint by ID- UnHappyPath for a Deeplink into a targeted offer which is not assigned to collector.
  Validate when the user makes a request with all valid headers, params and future offer id,it should show the respective error.
  "Given user make the request
  "When all headers are valid
  "And the params are also valid
  "Then user should be able to see the "err-exclusive" error
    * def offer = auth_notassigned_id
    * def headers = {Accept: 'application/json',X-Origin-Client: 'internal:test:integration',X-Correlation-Id: 'b8f04969-4a46-4d1b-b83b-903fdee703f5',Collector-Number:84151103400}
    * def query = {region:'ON'}
    Given url apiUrl
    Given path offer
    And headers headers
    And params query
    And method GET
    Then status 404
    And match response contains
        """{
          "code": "err-exclusive",
          "message": "Offer is Targeted (mass=false) AND Collector Number is not targeted for the offer"
          }"""

  @local @dev @authenticated
  Scenario:Offer endpoint by ID- HappyPath for a Deeplink with an out of region offer.
  Validate when the user makes a request with all valid headers, params and out of region offer it should show offer details with a warning.
  "Given user make the request
  "When all headers are valid
  "And the params are also valid
  "Then user should be able to see the offer details with the waring
    * def offer = auth_outofregion_id
    * def headers = {Accept: 'application/json',X-Origin-Client: 'internal:test:integration',X-Correlation-Id: '53a10bc8-84ea-49bd-9ea4-29cb5e88dba7',Collector-Number:84151103400}
    * def query = {region:'ON'}
    * def mechanism = {"mechanismType":"#string","mechanismLabel":"##string","mechanismValue":"##string"}
    * def url1 = {"url":"#string"}
    * def tiers = {"awardValue":"##number","qualifierValue":"##number","awardLong":"#string","qualifierLong":"#string"}
    Given url apiUrl
    Given path offer
    And headers headers
    And params query
    And method GET
    Then status 200
    And match response.offer contains
           """{
             "id":"#uuid",
             "partnerId":"#uuid",
             "partnerLabel":"#string",
             "partnerProfileURL":"#string",
             "partnerLogo":"##(url1)",
             "categoryId":"#uuid",
             "categoryLabel":"#string",
             "subCategoryId":"##uuid",
             "subCategoryLabel":"##string",
             "promotionId":"##uuid",
             "promotionLabel":"##string",
             "programType":"#string",
             "massOffer":"#boolean",
             "description":"##string",
             "awardShort":"#string",
             "qualifierShort":"#string",
             "displayDate":"#string",
             "startDate":"#string",
             "endDate":"#string",
             "displayPriority":"#number",
             "qualifierShort":"#string",
             "mechanisms":"#[] ##(mechanism)",
             "tiers":"#[] ##(tiers)",
             "image":"##(url1)",
             "legalText":"#string",
             "eventBasedOffer":"#boolean"
            }"""
    And match response.warning contains
        """{
          "errorCode": "err-region",
          "message": "Offer not available in user's current region"
          }"""

    #OfferStates Endpoints
  @local @dev @authenticated
  Scenario: Offer State endpoint -Happy Path
  Validate when the user makes a request with all valid headers and body, it should update state of an offer.
  "Given user make the request
  "When all headers are valid
  "And the body is also valid
  "Then offer state is updated and opted in.
    * def offer = states_id
    * def headers = {Accept: 'application/json',X-Origin-Client: 'internal:test:integration',X-Correlation-Id: '077530d1-3c97-4b0c-94c3-3456a38a877d',Collector-Number:84151103400}
    * def BODY =
           """
           {
    "states": [
        {
            "name": "SAVE",
            "value": "SAVED"
        }
    ]
}
           """
    Given url apiUrl
    Given path offer
    And headers headers
    And request BODY
    And method PUT
    Then status 204

  @local @dev @authenticated
  Scenario: Offer State endpoint -Happy Path for opting into an offer
  Validate when the user makes a request with all valid headers and body, it should optin to an offer.
  "Given user make the request
  "When all headers are valid
  "And the body is also valid
  "Then offer is  opted in.
    * def offer = states_id
    * def headers = {Accept: 'application/json',X-Origin-Client: 'internal:test:integration',X-Correlation-Id: 'c7f934d7-ed81-4fb5-ba97-1950183e198c',Collector-Number:84151103400}
    * def BODY =
           """
           {
        "states": [
        {
           "name": "OPT_IN",
           "value": "OPTED_IN"
        }
        ]
       }
           """
    Given url apiUrl
    Given path offer
    And headers headers
    And request BODY
    And method PUT
    Then status 204

  @local1 @dev @authenticated
  Scenario: Offer states endpoint -Happy Path getting states of an offers with states query param.
  Validate when the authenticated user makes a request with a valid headers and query params, it should return all saved offers for that collector number in response.
  "Given authenticated user make the request
  "When all the headers are valid
  "And all the query params are valid
  "Then we get all saved offers in response
  "Then offer state is updated and opted in.
    * def headers = {Accept: 'application/json',X-Origin-Client: 'internal:test:integration',X-Correlation-Id: '59d761ed-b8d0-436c-bf90-01d2f3dd5045',Collector-Number:84151103400}
    * def query = {region: 'ON',states: 'SAVED'}
    * def mechanism = {"mechanismType":"#string","mechanismLabel":"##string","mechanismValue":"##string"}
    * def url1 = {"url":"#string"}
    * def tiers = {"awardValue":"##number","qualifierValue":"##number","awardLong":"#string", "qualifierLong":"#string"}
    * def state = {"name":"#string","value":"#string","updatedAt":"#string"}
    And headers headers
    And params query
    And method GET
    Then status 200
    And match response.metadata contains
    """
            {
             "total":"#number"
            }
    """
    And match response.offers[*] contains

          """{
             "id":"#uuid",
             "partnerId":"#uuid",
             "partnerLabel":"#string",
             "partnerProfileURL":"#string",
             "partnerLogo":"##(url1)",
             "categoryId":"#uuid",
             "categoryLabel":"#string",
             "subCategoryId":"##uuid",
             "subCategoryLabel":"##string",
             "promotionId":"##uuid",
             "promotionLabel":"##string",
             "programType":"#string",
             "massOffer":"#boolean",
             "description":"##string",
             "awardShort":"#string",
             "qualifierShort":"#string",
             "displayDate":"#string",
             "startDate":"#string",
             "endDate":"#string",
             "displayPriority":"#number",
             "qualifierShort":"#string",
             "mechanisms":"#[] ##(mechanism)",
             "states":"#[] ##(state)",
             "tiers":"#[] ##(tiers)",
             "image":"##(url1)",
             "legalText":"#string",
             "eventBasedOffer":"#boolean"
            }"""

  @local1 @dev @authenticated
  Scenario: Offer states endpoint -Happy Path getting states of an offers with opted-in query param.
  Validate when the authenticated user makes a request with a valid headers and query params, it should return all saved offers for that collector number in response.
  "Given authenticated user make the request
  "When all the headers are valid
  "And all the query params are valid
  "Then we get all opted offers in response
  "Then offer state is updated to opted in.
    * def headers = {Accept: 'application/json',X-Origin-Client: 'internal:test:integration',X-Correlation-Id: '27ca531b-0e90-47cd-b0b8-89c1be69ad75',Collector-Number:84151103400}
    * def query = {region: 'ON',states: 'OPTED_IN'}
    * def mechanism = {"mechanismType":"#string","mechanismLabel":"##string","mechanismValue":"##string"}
    * def url1 = {"url":"#string"}
    * def tiers = {"awardValue":"##number","qualifierValue":"##number","awardLong":"#string", "qualifierLong":"#string"}
    * def state = {"name":"#string","value":"#string","updatedAt":"#string"}
    And headers headers
    And params query
    And method GET
    Then status 200
    And match response.metadata contains
    """
            {
             "total":"#number"
            }
    """
    And match response.offers[*] contains

          """{
             "id":"#uuid",
             "partnerId":"#uuid",
             "partnerLabel":"#string",
             "partnerProfileURL":"#string",
             "partnerLogo":"##(url1)",
             "categoryId":"#uuid",
             "categoryLabel":"#string",
             "subCategoryId":"##uuid",
             "subCategoryLabel":"##string",
             "promotionId":"##uuid",
             "promotionLabel":"##string",
             "programType":"#string",
             "massOffer":"#boolean",
             "description":"##string",
             "awardShort":"#string",
             "qualifierShort":"#string",
             "displayDate":"#string",
             "startDate":"#string",
             "endDate":"#string",
             "displayPriority":"#number",
             "qualifierShort":"#string",
             "mechanisms":"#[] ##(mechanism)",
             "states":"#[] ##(state)",
             "tiers":"#[] ##(tiers)",
             "image":"##(url1)",
             "legalText":"#string",
             "eventBasedOffer":"#boolean"
            }"""

  @local @dev @authenticated
  Scenario: Offer State endpoint -Happy Path for unsaving an offer
  Validate when the user makes a request with all valid headers and body, it should update state to unsaved.
  "Given user make the request
  "When all headers are valid
  "And the body is also valid
  "Then offer state should be updated to unsaved.
    * def offer = states_id
    * def headers = {Accept: 'application/json',X-Origin-Client: 'internal:test:integration',X-Correlation-Id: '365d753e-0e94-4e0e-a092-3e032669e1f0',Collector-Number:84151103400}
    * def BODY =
           """
           {
    "states": [
        {
            "name": "SAVE",
            "value": "UNSAVED"
        }
    ]
}
           """
    Given url apiUrl
    Given path offer
    And headers headers
    And request BODY
    And method PUT
    Then status 204

  @local1 @dev @authenticated
  Scenario: Offer states endpoint -Happy Path getting states of an offers with unsaved states query param.
  Validate when the authenticated user makes a request with a valid headers and query params, it should return all saved offers for that collector number in response.
  "Given authenticated user make the request
  "When all the headers are valid
  "And all the query params are valid
  "Then we get all saved offers in response
  "Then offer state is updated and opted in.
    * def headers = {Accept: 'application/json',X-Origin-Client: 'internal:test:integration',X-Correlation-Id: '5d9c864e-c972-4f2a-8de1-7c158347c4b7',Collector-Number:84151103400}
    * def query = {region: 'ON',states: 'UNSAVED'}
    * def mechanism = {"mechanismType":"#string","mechanismLabel":"##string","mechanismValue":"##string"}
    * def url1 = {"url":"#string"}
    * def tiers = {"awardValue":"##number","qualifierValue":"##number","awardLong":"#string", "qualifierLong":"#string"}
    * def state = {"name":"#string","value":"#string","updatedAt":"#string"}
    And headers headers
    And params query
    And method GET
    Then status 200
    And match response.metadata contains
    """
            {
             "total":"#number"
            }
    """
    And match response.offers[*] contains

          """{
             "id":"#uuid",
             "partnerId":"#uuid",
             "partnerLabel":"#string",
             "partnerProfileURL":"#string",
             "partnerLogo":"##(url1)",
             "categoryId":"#uuid",
             "categoryLabel":"#string",
             "subCategoryId":"##uuid",
             "subCategoryLabel":"##string",
             "promotionId":"##uuid",
             "promotionLabel":"##string",
             "programType":"#string",
             "massOffer":"#boolean",
             "description":"##string",
             "awardShort":"#string",
             "qualifierShort":"#string",
             "displayDate":"#string",
             "startDate":"#string",
             "endDate":"#string",
             "displayPriority":"#number",
             "qualifierShort":"#string",
             "mechanisms":"#[] ##(mechanism)",
             "states":"#[] ##(state)",
             "tiers":"#[] ##(tiers)",
             "image":"##(url1)",
             "legalText":"#string",
             "eventBasedOffer":"#boolean"
            }"""

  @local1 @dev @authenticated
  Scenario: Offer states endpoint -Happy Path getting states of an offers with invalid states query param.
  Validate when the authenticated user makes a request with a valid headers and query params, it should return all saved offers for that collector number in response.
  "Given authenticated user make the request
  "When all the headers are valid
  "And  the query params for states is not valid
  "Then we dont get saved offers in response
    * def headers = {Accept: 'application/json',X-Origin-Client: 'internal:test:integration',X-Correlation-Id: 'ec2ac8df-a6eb-49f6-bf41-0b0e965004d8',Collector-Number:84151103400}
    * def query = {region: 'ON',states: 'saved'}
    * def mechanism = {"mechanismType":"#string","mechanismLabel":"##string","mechanismValue":"##string"}
    * def url1 = {"url":"#string"}
    * def tiers = {"awardValue":"##number","qualifierValue":"##number","awardLong":"#string", "qualifierLong":"#string"}
    * def state = {"name":"#string","value":"#string","updatedAt":"#string"}
    And headers headers
    And params query
    And method GET
    Then status 400

  @local1 @dev @authenticated
  Scenario: Offer states endpoint -UnHappy Path getting states of an offers by states filter and limit is set to zero.
  Validate when the authenticated user makes a request with a valid headers and limit query param as 0 then it should give error
  "Given authenticated user make the request
  "When all the headers are valid
  "And limit query params is invalid
  "Then we get error in response.
    * def headers = {Accept: 'application/json',X-Origin-Client: 'internal:test:integration',X-Correlation-Id: '50f69ffa-fdeb-48bf-8595-92ac78cb0c8c',Collector-Number:84151103400}
    * def query = {region: 'ON',states: 'SAVED',limit:'0'}
    Given url apiUrl
    And headers headers
    And params query
    And method GET
    Then status 400

  @local @dev @authenticated
  Scenario: Offer State endpoint - Passing the wrong body.
  Validate when the user makes a request with all valid headers and body is not correct, it should give error.
  "Given user make the request
  "When all headers are valid
  "And the body is  invalid
  "Then offer state should give error.
    * def offer = states_id
    * def headers = {Accept: 'application/json',X-Origin-Client: 'internal:test:integration',X-Correlation-Id: '91494374-52d2-4e34-a1c1-034c24ce44af',Collector-Number:84151103400}
    * def BODY =
           """
         {
         "states": [
         {
         "name": "SAVE",
         "value": "SAVEs"
         }
    ]
}
           """
    Given url apiUrl
    Given path offer
    And headers headers
    And request BODY
    And method PUT
    Then status 400
    And match response contains
    """
  {
  "code": "BAD_REQUEST",
  "message": "States of SAVE cannot have the value of SAVEs"
  }
    """

  @local @dev @authenticated
  Scenario: Event Based Offer - Happy Path for an authenticated user.
  Validate when the authenticated user makes a request with a valid headers and query params, it should return all offers in the response.
  "Given authenticated user makes the request
  "When all the headers are valid
  "And all the query params are valid
  "Then we get all The event based offers in the response
    * def offer = eventBasedOffer_id
    * def headers = {Accept: 'application/json',X-Origin-Client: 'internal:test:integration',X-Correlation-Id: '08093c78-decd-4e7b-9451-96894c7f09b4',Collector-Number:84151103400}
    * def query = {region: 'ON'}
    * def mechanism = {"mechanismType":"#string","mechanismLabel":"##string","mechanismValue":"##string"}
    * def url1 = {"url":"#string"}
    * def tiers = {"awardValue":"##number","qualifierValue":"##number","awardLong":"#string","qualifierLong":"#string"}
    Given url apiUrl
    Given path offer
    And headers headers
    And params query
    And method GET
    Then status 200
    And match response.offer contains
           """{
            "id":"#uuid",
           "partnerId":"#uuid",
           "partnerLabel":"#string",
           "partnerProfileURL":"##string",
           "partnerLogo":"#(url1)",
           "categoryId":"#uuid",
           "categoryLabel":"#string",
           "subCategoryId":"##uuid",
           "subCategoryLabel":"##string",
           "promotionId":"##uuid",
           "promotionLabel":"##string",
           "programType":"#string",
           "massOffer":"#boolean",
           "endDate":"#string",
           "description":"##string",
           "awardShort":"#string",
           "qualifierShort":"#string",
           "displayDate":"#string",
           "startDate":"#string",
           "endDate":"#string",
           "displayPriority":"#number",
           "qualifierShort":"#string",
           "mechanisms":"#[] ##(mechanism)",
           "tiers":"#[] ##(tiers)",
           "image":"#(url1)",
           "legalText":"#string",
           "eventBasedOffer":"#boolean",
           "eligibilityDuration":"#number",
           "firstQualificationDate":"#string",
           "lastQualificationDate":"#string",
           "eligibilityDurationUnit":"#string"
            }"""
    And match response.warning == null

  @local @dev @authenticated
  Scenario: Offer Admin Endpoint - Happy Path for admin endpoint.
  Validate when the authenticated user makes a request with a valid headers and query params, it should return offers id in the response for admin endpoint.
  "Given authenticated user makes the request
  "When all the headers are valid
  "And all the query params are valid
  "Then we get event based offers id in the response for admin endpoint.
    * def offer = eventBasedOffer_id
    * def headers = {Accept: 'application/json',X-Origin-Client: 'internal:test:integration',X-Correlation-Id: '08093c78-decd-4e7b-9451-96894c7f09b4',Collector-Number:84151103400}
    * def query = {region: 'ON'}
    * def mechanism = {"mechanismType":"#string","mechanismLabel":"##string","mechanismValue":"##string"}
    * def url1 = {"url":"#string"}
    * def tiers = {"awardValue":"##number","qualifierValue":"##number","awardLong":"#string","qualifierLong":"#string"}
    Given url adminUrl
    Given path offer
    And headers headers
    And params query
    And method GET
    Then status 200
    And match response.offer contains
           """{
            "id":"#uuid",
           "partnerId":"#uuid",
           "partnerLabel":"#string",
           "partnerProfileURL":"##string",
           "partnerLogo":"#(url1)",
           "categoryId":"#uuid",
           "categoryLabel":"#string",
           "subCategoryId":"##uuid",
           "subCategoryLabel":"##string",
           "promotionId":"##uuid",
           "promotionLabel":"##string",
           "programType":"#string",
           "massOffer":"#boolean",
           "endDate":"#string",
           "description":"##string",
           "awardShort":"#string",
           "qualifierShort":"#string",
           "displayDate":"#string",
           "startDate":"#string",
           "endDate":"#string",
           "displayPriority":"#number",
           "qualifierShort":"#string",
           "mechanisms":"#[] ##(mechanism)",
           "tiers":"#[] ##(tiers)",
           "image":"#(url1)",
           "legalText":"#string",
           "eventBasedOffer":"#boolean",
           "eligibilityDuration":"#number",
           "firstQualificationDate":"#string",
           "lastQualificationDate":"#string",
           "eligibilityDurationUnit":"#string"
            }"""
    And match response.warning == null

