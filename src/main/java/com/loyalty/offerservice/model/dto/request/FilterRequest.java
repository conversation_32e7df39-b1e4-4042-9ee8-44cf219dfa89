package com.loyalty.offerservice.model.dto.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.loyalty.offerservice.enums.ProgramTypeEnum;
import com.loyalty.offerservice.enums.RegionEnum;
import lombok.*;

import java.util.List;
import java.util.Objects;
import java.util.UUID;


@Getter
@AllArgsConstructor
@Setter
@ToString
@Builder(toBuilder = true)
@JsonInclude(Include.NON_NULL)
public class FilterRequest {

    private RegionEnum region;
    private List<UUID> partnerId;
    private List<UUID> categoryId;
    private List<UUID> subCategoryId;
    private UUID promotionId;
    private List<String> type;
    private List<ProgramTypeEnum> programType;
    private OfferState states;
    private List<String> availability;
    private Boolean massOffer;
    private Boolean eventBased;
    private UUID experiment;
    private List<UUID> excludePartnerId;

    /**
     * Takes an instance of FilterRequest and overrides the original instance's
     * attributes with the param's non-null attributes.
     *
     * @param overrides FilterRequest object to replace original object's attributes
     * @return a FilterRequest object overridden with the param's values
     */
    public FilterRequest override(FilterRequest overrides) {
        if (overrides == null)
            return this;

        return this.toBuilder()
                .region(overrides.region != null ? overrides.region : this.region)
                .partnerId(overrides.partnerId != null ? overrides.partnerId : this.partnerId)
                .categoryId(overrides.categoryId != null ? overrides.categoryId : this.categoryId)
                .subCategoryId(overrides.subCategoryId != null ? overrides.subCategoryId : this.subCategoryId)
                .promotionId(overrides.promotionId != null ? overrides.promotionId : this.promotionId)
                .type(overrides.type != null ? overrides.type : this.type)
                .programType(overrides.programType != null ? overrides.programType : this.programType)
                .states(overrides.states != null ? overrides.states : this.states)
                .availability(overrides.availability != null ? overrides.availability : this.availability)
                .massOffer(overrides.massOffer != null ? overrides.massOffer : this.massOffer)
                .eventBased(overrides.eventBased != null ? overrides.eventBased : this.eventBased)
                .experiment(overrides.experiment != null ? overrides.experiment : this.experiment)
                .excludePartnerId(overrides.excludePartnerId != null ? overrides.excludePartnerId : this.excludePartnerId)
                .build();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;

        if (o == null || getClass() != o.getClass())
            return false;

        FilterRequest that = (FilterRequest) o;
        return Objects.equals(region, that.region) &&
                Objects.equals(partnerId, that.partnerId) &&
                Objects.equals(categoryId, that.categoryId) &&
                Objects.equals(subCategoryId, that.subCategoryId) &&
                Objects.equals(promotionId, that.promotionId) &&
                Objects.equals(type, that.type) &&
                Objects.equals(programType, that.programType) &&
                Objects.equals(states, that.states) &&
                Objects.equals(availability, that.availability) &&
                Objects.equals(massOffer, that.massOffer) &&
                Objects.equals(eventBased, that.eventBased) &&
                Objects.equals(experiment, that.experiment) &&
                Objects.equals(excludePartnerId, that.excludePartnerId);
    }
}
