package com.loyalty.offerservice.config;

import com.loyalty.offerservice.helper.CorsHelper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import jakarta.servlet.http.HttpServletRequest;
import java.util.Collections;
import java.util.List;

@Configuration
@EnableWebSecurity
public class SecurityConfig {

    private final CorsHelper corsHelper;

    public SecurityConfig(CorsHelper corsHelper) {
        this.corsHelper = corsHelper;
    }

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            .cors(cors -> cors.configurationSource(createCorsConfigurationSource()))
            .csrf(csrf -> csrf.disable())
            .authorizeHttpRequests(auth -> auth
                .requestMatchers("/**").permitAll()
            );

        return http.build();
    }

    private CorsConfigurationSource createCorsConfigurationSource() {
        return new CorsConfigurationSource() {
            @Override
            public CorsConfiguration getCorsConfiguration(HttpServletRequest request) {
                String origin = request.getHeader("Origin");
                String method = request.getMethod();

                // Check for authorization in both preflight and actual requests
                String accessRequestHeader = request.getHeader("Access-Control-Request-Headers");
                String authorizationHeader = request.getHeader("Authorization");
                String collectorNumberHeader = request.getHeader("Collector-Number");

                // For preflight requests, check Access-Control-Request-Headers
                boolean isPreflightAuthorized = accessRequestHeader != null &&
                    (accessRequestHeader.toLowerCase().contains("authorization") ||
                     accessRequestHeader.toLowerCase().contains("collector-number"));

                // For actual requests, check if authorization or collector-number headers are present
                boolean isActualRequestAuthorized = authorizationHeader != null || collectorNumberHeader != null;

                // For PUT requests to /states endpoint, always treat as authorized if origin is allowed
                boolean isPutStatesRequest = "PUT".equals(method) &&
                    request.getRequestURI() != null &&
                    request.getRequestURI().contains("/states");

                boolean isAuthorized = isPreflightAuthorized || isActualRequestAuthorized || isPutStatesRequest;
                CorsConfiguration config = new CorsConfiguration();
                boolean isOriginAllowed = corsHelper.isAllowedOrigin(origin);

                if (isOriginAllowed && isAuthorized) {
                    // Trusted and authorized
                    config.setAllowedOrigins(List.of(origin));
                    config.setAllowedMethods(List.of("GET", "PUT", "OPTIONS"));
                    config.setAllowedHeaders(List.of("*"));
                    config.setAllowCredentials(true);
                    return config;

                } else if (isOriginAllowed) {
                    config.setAllowedOrigins(List.of(origin));
                    config.setAllowedMethods(List.of("GET", "OPTIONS"));
                    config.setAllowedHeaders(List.of("*"));
                    config.setAllowCredentials(false);
                    return config;
                } else {
                    config.setAllowedOrigins(Collections.singletonList("*"));
                    config.setAllowedMethods(List.of("GET", "OPTIONS"));
                    config.setAllowedHeaders(Collections.singletonList("*"));
                    config.setAllowCredentials(false);
                    return config;
                }
            }
        };
    }
}